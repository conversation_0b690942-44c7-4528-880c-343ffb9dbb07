package net.summerfarm.manage.starter.monitor;

import net.summerfarm.manage.starter.aspect.UnCollectByAspect;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MonitorController {

    /**
     * 心跳检测
     * @return success
     */
    @GetMapping(value = "/ok")
    @UnCollectByAspect
    public String heartbeat(){
        return "success";
    }
}
