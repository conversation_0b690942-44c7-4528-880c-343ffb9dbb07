package net.summerfarm.manage.application.test.service;

import com.alibaba.fastjson.JSON;
import net.summerfarm.manage.application.inbound.controller.dts.BinlogNormalListener;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceQueryInput;
import net.summerfarm.manage.application.inbound.schedule.MemberGradeV2Processor;
import net.summerfarm.manage.application.service.product.mall.MajorPriceQueryService;
import net.summerfarm.manage.application.service.wx.WeixinShippingServiceImpl;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.Conf;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmTaskParamDTO;
import net.summerfarm.manage.common.msg.CustomizationConfirmMsg;
import net.summerfarm.manage.common.util.WeChatUtils;
import net.summerfarm.manage.facade.message.WxSendMessageFacade;
import net.summerfarm.manage.facade.message.input.SendMessageInput;
import net.summerfarm.manage.starter.Application;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @author: xiaowk
 * @time: 2024/7/29 下午4:58
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class WeChatTest {

    @Autowired
    private WeixinShippingServiceImpl weixinShippingService;

    @Resource
    private MajorPriceQueryService majorPriceQueryService;

    @Resource
    private BinlogNormalListener binlogNormalListener;
    @Autowired
    MemberGradeV2Processor processor;

    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Autowired
    private WxSendMessageFacade wxSendMessageFacade;

    @Test
    public void uploadShippingInfoByMasterOrderNo() {
//        *****************
        //weixinShippingService.uploadShippingInfoByMasterOrderNo("MPO172907025135882");
    }

    @Test
    public void process() {
        String a = "{\"data\":[{\"order_id\":\"8450648\",\"order_no\":\"0124PE6PSS1224163441\",\"m_id\":\"350798\",\"order_time\":\"2024-12-24 16:34:20\",\"type\":\"0\",\"status\":\"3\",\"delivery_fee\":\"0.0\",\"total_price\":\"36.0\",\"remark\":null,\"confirm_time\":null,\"area_name\":null,\"out_times\":\"0\",\"discount_type\":\"0\",\"out_times_fee\":\"0.0\",\"area_no\":\"1001\",\"m_size\":\"单店\",\"direct\":null,\"sku_show\":null,\"red_pack_amount\":null,\"card_rule_id\":null,\"account_id\":\"10155\",\"origin_price\":\"36.0\",\"out_stock\":\"0\",\"discount_card_id\":null,\"order_sale_type\":\"0\",\"receivable_status\":\"0\",\"admin_id\":null,\"invoice_status\":\"0\",\"financial_invoice_id\":null,\"update_time\":\"2024-12-24 16:34:23\",\"operate_id\":null,\"order_pay_type\":\"2\"}],\"database\":\"xianmudb\",\"es\":*************,\"hashKeyForOrderedQueue\":\"orders.order_id.8450648\",\"id\":**********,\"isDdl\":false,\"mysqlType\":null,\"old\":[{\"status\":\"1\",\"update_time\":null}],\"pkNames\":[\"order_id\"],\"sql\":\"\",\"sqlType\":null,\"table\":\"orders\",\"threadId\":1496629,\"ts\":*************,\"type\":\"UPDATE\",\"user\":\"dev2\"}";
        DtsModel dts = JSON.parseObject(a, DtsModel.class);
        binlogNormalListener.process(dts);
    }

    @Test
    public void updateMerchantCurrentGradeFlag() {
//        *****************
        XmJobInput input = new XmJobInput();
        XmTaskParamDTO dto = new XmTaskParamDTO();
        //dto.setDateTag("20241023");
        dto.setEndId(30L);
        dto.setOffset(10);
        //dto.setUpdateMerchantCurrentGradeFlag(true);
        input.setJobParameters(JSON.toJSONString(dto));
        try {
            processor.processResult(input);
        } catch (Exception e){

        }
    }


    @Test
    public void majorPricePage() {
        MajorPriceQueryInput input = new MajorPriceQueryInput();
        input.setAdminId(1);
        input.setPageIndex(1);
        input.setPageSize(10);
        System.out.println(JSON.toJSONString(majorPriceQueryService.majorPricePage(input)));
    }

    @Test
    public void testWxTemplateMessage(){
        SendMessageInput sendMessageInput = new SendMessageInput();
        sendMessageInput.setMessage(CustomizationConfirmMsg.buildTemplateMessage("0225KT9EKF0723165786","鲜沐杯子17号", 80, BigDecimal.TEN));
        sendMessageInput.setOpenId("oRRzJ6X9AmbIZR69-eyLP8015XeQ");
        sendMessageInput.setTemplateId(nacosPropertiesHolder.getCustomizationRequestDesignConfirmWxTemplateId());
        sendMessageInput.setJumpUrl(WeChatUtils.getWeChatCode(Conf.CUSTOM_DETAIL_URL + "0225KT9EKF0723165786"));
        wxSendMessageFacade.sendMessage(sendMessageInput);
    }

}
