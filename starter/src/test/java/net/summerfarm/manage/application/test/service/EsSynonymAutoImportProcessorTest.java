package net.summerfarm.manage.application.test.service;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.schedule.EsSynonymAutoImportProcessor;
import net.summerfarm.manage.starter.Application;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = Application.class)
public class EsSynonymAutoImportProcessorTest {

    @Autowired
    private EsSynonymAutoImportProcessor esSynonymAutoImportProcessor;

    @Test
    public void testProcessResult() throws Exception {
        ProcessResult result = esSynonymAutoImportProcessor.processResult(new XmJobInput());
        log.info("result:{}", result);
        assert result != null;
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }
}
