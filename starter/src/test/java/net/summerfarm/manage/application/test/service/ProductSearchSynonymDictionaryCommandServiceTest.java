package net.summerfarm.manage.application.test.service;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.command.ProductSearchSynonymDictionaryCommandInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;
import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryCommandService;
import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryQueryService;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.starter.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ProductSearchSynonymDictionaryCommandServiceTest {

    @Resource
    private ProductSearchSynonymDictionaryCommandService productSearchSynonymDictionaryCommandService;

    @Resource
    ProductSearchSynonymDictionaryQueryService productSearchSynonymDictionaryQueryService;

    @Test
    public void testQuery() {
        ProductSearchSynonymDictionaryQueryInput input = new ProductSearchSynonymDictionaryQueryInput();
        input.setSynonymTerms("芒果");
        input.setPageSize(10);
        input.setPageIndex(1);
        PageInfo<ProductSearchSynonymDictionaryEntity> result = productSearchSynonymDictionaryQueryService.getPage(input);
        log.info("Query result: {}", result);
        Assert.assertTrue(null != result);
        Assert.assertTrue(result.getList().size() > 0);
    }

    @Test
    public void testInsert() {
        ProductSearchSynonymDictionaryCommandInput input = new ProductSearchSynonymDictionaryCommandInput();
        input.setSynonymTerms("芒果,台农,小台农,大青芒,青芒");
        ProductSearchSynonymDictionaryEntity entity = productSearchSynonymDictionaryCommandService.insert(input);
        log.info("Inserted entity: {}", entity);
    }
}
