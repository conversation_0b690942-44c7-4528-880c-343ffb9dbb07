package net.summerfarm.manage.infrastructure.mapper.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Mapper
public interface MerchantFrequentlyBuyingSkuNotificationConfigMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MerchantFrequentlyBuyingSkuNotificationConfig record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MerchantFrequentlyBuyingSkuNotificationConfig record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MerchantFrequentlyBuyingSkuNotificationConfig selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<MerchantFrequentlyBuyingSkuNotificationConfig> selectByCondition(MerchantFrequentlyBuyingSkuNotificationConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<MerchantFrequentlyBuyingSkuNotificationConfigEntity> getPage(MerchantFrequentlyBuyingSkuNotificationConfigQueryParam param);
}

