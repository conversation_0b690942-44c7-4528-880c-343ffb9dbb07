package net.summerfarm.manage.infrastructure.model.timing;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 定期送规则数据模型
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class TimingRule {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 团购商品sku
     */
    private String timingSku;

    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 展示标记：0不展示，1展示
     */
    private Boolean display;

    /**
     * 团购开始时间
     */
    private LocalDateTime startTime;

    /**
     * 团购结束时间
     */
    private LocalDateTime endTime;

    /**
     * 配送开始时间
     */
    private LocalDate deliveryStart;

    /**
     * 配送结束时间
     */
    private LocalDate deliveryEnd;

    /**
     * 规则描述
     */
    private String ruleInformation;

    /**
     * 起送数量
     */
    private Integer deliveryUnit;

    /**
     * 单次配送上限
     */
    private Integer deliveryUpperLimit;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 类型：0省心送，1预售
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer priority;

    /**
     * 是否自动计算配送次数
     */
    private Boolean autoCalculate;

    /**
     * 配送周期
     */
    private Integer deliveryPeriod;

    /**
     * 开始配送类型：0、下一个配送日 1、指定开始日期（delivery_start）
     */
    private Integer deliveryStartType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 省心送门槛
     */
    private Integer threshold;

    /**
     * 下单日期+N的N值
     */
    private Integer plusDay;
}
