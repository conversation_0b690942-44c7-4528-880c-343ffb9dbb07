package net.summerfarm.manage.infrastructure.repository.admin;


import net.summerfarm.manage.infrastructure.model.admin.Invitecode;
import net.summerfarm.manage.infrastructure.mapper.admin.InvitecodeMapper;
import net.summerfarm.manage.infrastructure.converter.admin.InvitecodeConverter;
import net.summerfarm.manage.domain.admin.repository.InvitecodeQueryRepository;
import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import net.summerfarm.manage.domain.admin.param.query.InvitecodeQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-06-18 13:07:30
* @version 1.0
*
*/
@Repository
public class InvitecodeQueryRepositoryImpl implements InvitecodeQueryRepository {

    @Autowired
    private InvitecodeMapper invitecodeMapper;


    @Override
    public PageInfo<InvitecodeEntity> getPage(InvitecodeQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<InvitecodeEntity> entities = invitecodeMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public InvitecodeEntity selectById(Long inviteId) {
        return InvitecodeConverter.toInvitecodeEntity(invitecodeMapper.selectById(inviteId));
    }


    @Override
    public List<InvitecodeEntity> selectByCondition(InvitecodeQueryParam param) {
        return InvitecodeConverter.toInvitecodeEntityList(invitecodeMapper.selectByCondition(param));
    }

}