package net.summerfarm.manage.infrastructure.repository.marketItem;

import net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt;
import net.summerfarm.manage.infrastructure.mapper.marketItem.MarketItemAiExtMapper;
import net.summerfarm.manage.infrastructure.converter.marketItem.MarketItemAiExtConverter;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtCommandRepository;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Repository
public class MarketItemAiExtCommandRepositoryImpl implements MarketItemAiExtCommandRepository {

    @Autowired
    private MarketItemAiExtMapper marketItemAiExtMapper;
    @Override
    public MarketItemAiExtEntity insertSelective(MarketItemAiExtCommandParam param) {
        MarketItemAiExt marketItemAiExt = MarketItemAiExtConverter.toMarketItemAiExt(param);
        marketItemAiExtMapper.insertSelective(marketItemAiExt);
        return MarketItemAiExtConverter.toMarketItemAiExtEntity(marketItemAiExt);
    }

    @Override
    public int updateSelectiveById(MarketItemAiExtCommandParam param){
        return marketItemAiExtMapper.updateSelectiveById(MarketItemAiExtConverter.toMarketItemAiExt(param));
    }


    @Override
    public int remove(Long id) {
        return marketItemAiExtMapper.remove(id);
    }
}