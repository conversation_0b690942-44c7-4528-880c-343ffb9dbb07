package net.summerfarm.manage.infrastructure.repository.merchant;


import net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantFrequentlyBuyingSkuMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantFrequentlyBuyingSkuConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
@Repository
public class MerchantFrequentlyBuyingSkuQueryRepositoryImpl implements MerchantFrequentlyBuyingSkuQueryRepository {

    @Autowired
    private MerchantFrequentlyBuyingSkuMapper merchantFrequentlyBuyingSkuMapper;


    @Override
    public PageInfo<MerchantFrequentlyBuyingSkuEntity> getPage(MerchantFrequentlyBuyingSkuQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantFrequentlyBuyingSkuEntity> entities = merchantFrequentlyBuyingSkuMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MerchantFrequentlyBuyingSkuEntity selectById(Long id) {
        return MerchantFrequentlyBuyingSkuConverter.toMerchantFrequentlyBuyingSkuEntity(merchantFrequentlyBuyingSkuMapper.selectById(id));
    }


    @Override
    public List<MerchantFrequentlyBuyingSkuEntity> selectByCondition(MerchantFrequentlyBuyingSkuQueryParam param) {
        return MerchantFrequentlyBuyingSkuConverter.toMerchantFrequentlyBuyingSkuEntityList(merchantFrequentlyBuyingSkuMapper.selectByCondition(param));
    }

}