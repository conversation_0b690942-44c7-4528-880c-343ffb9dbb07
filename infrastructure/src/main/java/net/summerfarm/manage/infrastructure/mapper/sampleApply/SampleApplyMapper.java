package net.summerfarm.manage.infrastructure.mapper.sampleApply;

import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject;
import net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply;
import net.summerfarm.manage.domain.sampleApply.param.query.SampleApplyQueryParam;
import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-02 14:00:39
 * @version 1.0
 *
 */
@Mapper
public interface SampleApplyMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(SampleApply record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(SampleApply record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("sampleId") Long sampleId);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param sampleId
     * @return
     */
    SampleApply selectById(@Param("sampleId") Long sampleId);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<SampleApply> selectByCondition(SampleApplyQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<SampleApplyEntity> getPage(SampleApplyQueryParam param);

    /**
     * 查询有效的样品单
     * @param sampleIdList 样品单ID
     * @return 结果
     */
    List<SampleOrderFlatObject> queryValidSampleOrderDeliveryDetail(@Param("sampleIdList") List<String> sampleIdList);
}

