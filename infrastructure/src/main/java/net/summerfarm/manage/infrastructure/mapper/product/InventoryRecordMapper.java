package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.infrastructure.model.product.InventoryRecord;
import net.summerfarm.manage.domain.product.param.query.InventoryRecordQueryParam;
import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-06 15:10:53
 * @version 1.0
 *
 */
@Mapper
public interface InventoryRecordMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(InventoryRecord record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(InventoryRecord record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    InventoryRecord selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<InventoryRecord> selectByCondition(InventoryRecordQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<InventoryRecordEntity> getPage(InventoryRecordQueryParam param);

    void saveBatch(@Param("inventoryRecords") List<InventoryRecord> inventoryRecords);
}

