package net.summerfarm.manage.infrastructure.repository.activity;

import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice;
import net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuPriceMapper;
import net.summerfarm.manage.infrastructure.converter.activity.ActivitySkuPriceConverter;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuPriceCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-04-09 15:06:01
* @version 1.0
*
*/
@Repository
public class ActivitySkuPriceCommandRepositoryImpl implements ActivitySkuPriceCommandRepository {

    @Autowired
    private ActivitySkuPriceMapper activitySkuPriceMapper;
    @Override
    public ActivitySkuPriceEntity insertSelective(ActivitySkuPriceCommandParam param) {
        ActivitySkuPrice activitySkuPrice = ActivitySkuPriceConverter.toActivitySkuPrice(param);
        activitySkuPriceMapper.insertSelective(activitySkuPrice);
        return ActivitySkuPriceConverter.toActivitySkuPriceEntity(activitySkuPrice);
    }

    @Override
    public int updateSelectiveById(ActivitySkuPriceCommandParam param){
        return activitySkuPriceMapper.updateSelectiveById(ActivitySkuPriceConverter.toActivitySkuPrice(param));
    }


    @Override
    public int remove(Long id) {
        return activitySkuPriceMapper.remove(id);
    }
}