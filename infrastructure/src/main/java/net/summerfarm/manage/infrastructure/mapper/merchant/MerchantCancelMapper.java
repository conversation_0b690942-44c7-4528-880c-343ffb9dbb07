package net.summerfarm.manage.infrastructure.mapper.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantCancel;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-12-27 14:01:49
 * @version 1.0
 *
 */
@Mapper
public interface MerchantCancelMapper{

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MerchantCancel selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<MerchantCancel> selectByCondition(MerchantCancelQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<MerchantCancelEntity> getPage(MerchantCancelQueryParam param);
}

