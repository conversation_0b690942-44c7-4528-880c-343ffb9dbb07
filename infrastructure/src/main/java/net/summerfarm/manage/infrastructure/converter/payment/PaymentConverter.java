package net.summerfarm.manage.infrastructure.converter.payment;

import net.summerfarm.manage.infrastructure.model.payment.Payment;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.PaymentCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-12-23 17:01:31
 * @version 1.0
 *
 */
public class PaymentConverter {

    private PaymentConverter() {
        // 无需实现
    }




    public static List<PaymentEntity> toPaymentEntityList(List<Payment> paymentList) {
        if (paymentList == null) {
            return Collections.emptyList();
        }
        List<PaymentEntity> paymentEntityList = new ArrayList<>();
        for (Payment payment : paymentList) {
            paymentEntityList.add(toPaymentEntity(payment));
        }
        return paymentEntityList;
}


    public static PaymentEntity toPaymentEntity(Payment payment) {
        if (payment == null) {
             return null;
        }
        PaymentEntity paymentEntity = new PaymentEntity();
        paymentEntity.setPaymentId(payment.getPaymentId());
        paymentEntity.setPayType(payment.getPayType());
        paymentEntity.setOrderNo(payment.getOrderNo());
        paymentEntity.setTransactionNumber(payment.getTransactionNumber());
        paymentEntity.setMoney(payment.getMoney());
        paymentEntity.setEndTime(payment.getEndTime());
        paymentEntity.setTradeType(payment.getTradeType());
        paymentEntity.setBankType(payment.getBankType());
        paymentEntity.setStatus(payment.getStatus());
        paymentEntity.setErrCode(payment.getErrCode());
        paymentEntity.setErrCodeDes(payment.getErrCodeDes());
        paymentEntity.setCompanyAccountId(payment.getCompanyAccountId());
        paymentEntity.setScanCode(payment.getScanCode());
        paymentEntity.setAccountInfo(payment.getAccountInfo());
        paymentEntity.setBocPayType(payment.getBocPayType());
        paymentEntity.setOnlinePayEndTime(payment.getOnlinePayEndTime());
        paymentEntity.setPaymentChannelId(payment.getPaymentChannelId());
        return paymentEntity;
    }








    public static Payment toPayment(PaymentCommandParam param) {
        if (param == null) {
            return null;
        }
        Payment payment = new Payment();
        payment.setPaymentId(param.getPaymentId());
        payment.setPayType(param.getPayType());
        payment.setOrderNo(param.getOrderNo());
        payment.setTransactionNumber(param.getTransactionNumber());
        payment.setMoney(param.getMoney());
        payment.setEndTime(param.getEndTime());
        payment.setTradeType(param.getTradeType());
        payment.setBankType(param.getBankType());
        payment.setStatus(param.getStatus());
        payment.setErrCode(param.getErrCode());
        payment.setErrCodeDes(param.getErrCodeDes());
        payment.setCompanyAccountId(param.getCompanyAccountId());
        payment.setScanCode(param.getScanCode());
        payment.setAccountInfo(param.getAccountInfo());
        payment.setBocPayType(param.getBocPayType());
        payment.setOnlinePayEndTime(param.getOnlinePayEndTime());
        payment.setPaymentChannelId(param.getPaymentChannelId());
        return payment;
    }
}
