package net.summerfarm.manage.infrastructure.model.product;


import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-12-12 11:19:19
 * @version 1.0
 *
 */
@Data
public class AppPopBiaoguoProductsDf {
	/**
	 * 数据库唯一标识
	 */
	private Long id;

	/**
	 * 完整类目名称
	 */
	private String categoryName;

	/**
	 * 一级类目
	 */
	private String category1;

	/**
	 * 二级类目
	 */
	private String category2;

	/**
	 * 三级类目
	 */
	private String category3;

	/**
	 * 后台类目名称
	 */
	private String backCategoryName;

	/**
	 * 竞争对手
	 */
	private String competitor;

	/**
	 * SKU编码
	 */
	private String skuCode;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 商品详情的描述
	 */
	private String babyName;

	/**
	 * 标准价格
	 */
	private String standardPrice;

	/**
	 * 最终标准价格
	 */
	private String finalStandardPrice;

	/**
	 * 上次标准价格
	 */
	private String lastTimeStandardPrice;

	/**
	 * 最终市斤价格
	 */
	private String finalUnitPriceCatty;

	/**
	 * 单位市斤价格
	 */
	private String unitPriceCatty;

	/**
	 * 商品类型
	 */
	private String goodsType;

	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 毛重
	 */
	private String grossWeight;

	/**
	 * 净重
	 */
	private String netWeight;

	/**
	 * 月销量
	 */
	private String monthSale;

	/**
	 * 商品佣金比例
	 */
	private String goodsSiphonCommissionRate;

	/**
	 * 卖家佣金比例
	 */
	private String sellerSiphonCommissionRate;

	/**
	 * 卖家名称
	 */
	private String sellerName;

	/**
	 * 商品属性详情列表
	 */
	private String goodsPropDetailList;

	/**
	 * 商品链接
	 */
	private String url;

	/**
	 * 七天售后次数
	 */
	private String sevenDayAfterSale;

	/**
	 * 爬虫抓取时间
	 */
	private String spiderFetchTime;

	/**
	 * 商品创建时间(来自标果)
	 */
	private String createTime;

	/**
	 * 数据日期分区
	 */
	private String ds;



}