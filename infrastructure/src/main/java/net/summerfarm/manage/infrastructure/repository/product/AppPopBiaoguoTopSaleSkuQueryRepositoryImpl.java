package net.summerfarm.manage.infrastructure.repository.product;


import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.infrastructure.offlinemapper.AppPopBiaoguoTopSaleSkuMapper;
import net.summerfarm.manage.infrastructure.converter.product.AppPopBiaoguoTopSaleSkuConverter;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoTopSaleSkuQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
@Repository
public class AppPopBiaoguoTopSaleSkuQueryRepositoryImpl implements AppPopBiaoguoTopSaleSkuQueryRepository {

    @Autowired
    private AppPopBiaoguoTopSaleSkuMapper appPopBiaoguoTopSaleSkuMapper;


    @Override
    public PageInfo<AppPopBiaoguoTopSaleSkuEntity> getPage(AppPopBiaoguoTopSaleSkuQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AppPopBiaoguoTopSaleSkuEntity> entities = appPopBiaoguoTopSaleSkuMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AppPopBiaoguoTopSaleSkuEntity selectById(Long id) {
        return AppPopBiaoguoTopSaleSkuConverter.toAppPopBiaoguoTopSaleSkuEntity(appPopBiaoguoTopSaleSkuMapper.selectById(id));
    }


    @Override
    public List<AppPopBiaoguoTopSaleSkuEntity> selectByCondition(AppPopBiaoguoTopSaleSkuQueryParam param) {
        return AppPopBiaoguoTopSaleSkuConverter.toAppPopBiaoguoTopSaleSkuEntityList(appPopBiaoguoTopSaleSkuMapper.selectByCondition(param));
    }

    @Override
    public List<AppPopBiaoguoCategoryEntity> selectCategory2List(AppPopBiaoguoTopSaleSkuQueryParam queryParam) {
        return appPopBiaoguoTopSaleSkuMapper.selectCategory2List(queryParam);
    }

}