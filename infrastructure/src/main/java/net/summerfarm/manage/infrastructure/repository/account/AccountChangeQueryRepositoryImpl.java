package net.summerfarm.manage.infrastructure.repository.account;


import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.infrastructure.model.account.AccountChange;
import net.summerfarm.manage.infrastructure.mapper.account.AccountChangeMapper;
import net.summerfarm.manage.infrastructure.converter.account.AccountChangeConverter;
import net.summerfarm.manage.domain.account.repository.AccountChangeQueryRepository;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;
import net.summerfarm.manage.common.input.account.AccountChangeQueryInput;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:19
* @version 1.0
*
*/
@Repository
public class AccountChangeQueryRepositoryImpl implements AccountChangeQueryRepository {

    @Autowired
    private AccountChangeMapper accountChangeMapper;


    @Override
    public AccountChangeEntity selectById(Long id) {
        return AccountChangeConverter.toAccountChangeEntity(accountChangeMapper.selectById(id));
    }

    @Override
    public List<AccountChangeEntity> getQueryInput(MerchantQueryInput entity) {
        return AccountChangeConverter.toAccountChangeEntityList(accountChangeMapper.selectByCondition(entity));
    }




}