package net.summerfarm.manage.infrastructure.repository.trade;

import net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime;
import net.summerfarm.manage.infrastructure.mapper.trade.TimingOrderRefundTimeMapper;
import net.summerfarm.manage.infrastructure.converter.trade.TimingOrderRefundTimeConverter;
import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeCommandRepository;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.command.TimingOrderRefundTimeCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-22 14:45:54
* @version 1.0
*
*/
@Repository
public class TimingOrderRefundTimeCommandRepositoryImpl implements TimingOrderRefundTimeCommandRepository {

    @Autowired
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;
    @Override
    public TimingOrderRefundTimeEntity insertSelective(TimingOrderRefundTimeCommandParam param) {
        TimingOrderRefundTime timingOrderRefundTime = TimingOrderRefundTimeConverter.toTimingOrderRefundTime(param);
        timingOrderRefundTimeMapper.insertSelective(timingOrderRefundTime);
        return TimingOrderRefundTimeConverter.toTimingOrderRefundTimeEntity(timingOrderRefundTime);
    }

    @Override
    public int updateSelectiveById(TimingOrderRefundTimeCommandParam param){
        return timingOrderRefundTimeMapper.updateSelectiveById(TimingOrderRefundTimeConverter.toTimingOrderRefundTime(param));
    }


    @Override
    public int remove(Long id) {
        return timingOrderRefundTimeMapper.remove(id);
    }

    @Override
    public Boolean deleteTimeOrderRefund(String orderNo) {
        return timingOrderRefundTimeMapper.deleteTimeOrderRefund(orderNo) > 0;
    }
}