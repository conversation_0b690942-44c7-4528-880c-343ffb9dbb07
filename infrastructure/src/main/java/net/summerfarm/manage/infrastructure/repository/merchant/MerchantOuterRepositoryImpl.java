package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantOuterEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantOuterRepository;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantOuterMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class MerchantOuterRepositoryImpl implements MerchantOuterRepository {
    @Resource
    MerchantOuterMapper merchantOuterMapper;
    @Override
    public List<MerchantOuterEntity> selectByMid(List<Long> mids) {
        return merchantOuterMapper.selectByMIdList(mids);
    }
}
