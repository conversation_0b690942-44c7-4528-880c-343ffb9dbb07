package net.summerfarm.manage.infrastructure.repository.admin;


import net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission;
import net.summerfarm.manage.infrastructure.mapper.admin.AdminDataPermissionMapper;
import net.summerfarm.manage.infrastructure.converter.admin.AdminDataPermissionConverter;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-06-19 16:33:43
* @version 1.0
*
*/
@Repository
public class AdminDataPermissionQueryRepositoryImpl implements AdminDataPermissionQueryRepository {

    @Autowired
    private AdminDataPermissionMapper adminDataPermissionMapper;


    @Override
    public PageInfo<AdminDataPermissionEntity> getPage(AdminDataPermissionQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AdminDataPermissionEntity> entities = adminDataPermissionMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AdminDataPermissionEntity selectById(Long id) {
        return AdminDataPermissionConverter.toAdminDataPermissionEntity(adminDataPermissionMapper.selectById(id));
    }


    @Override
    public List<AdminDataPermissionEntity> selectByCondition(AdminDataPermissionQueryParam param) {
        return AdminDataPermissionConverter.toAdminDataPermissionEntityList(adminDataPermissionMapper.selectByCondition(param));
    }

}