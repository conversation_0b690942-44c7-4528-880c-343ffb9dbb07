package net.summerfarm.manage.infrastructure.mapper.major;

import net.summerfarm.manage.infrastructure.model.major.MajorPriceLog;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@Mapper
public interface MajorPriceLogMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MajorPriceLog record);

    int insertList(@Param("list")List<MajorPriceLog> list);


    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MajorPriceLog record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MajorPriceLog selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<MajorPriceLog> selectByCondition(MajorPriceLogQueryParam param);

    /**
     * @Describe: 分页查询历史调价记录，id倒叙
     * @param param
     * @return
     */
    List<MajorPriceLogEntity> getPage(MajorPriceLogQueryParam param);
}

