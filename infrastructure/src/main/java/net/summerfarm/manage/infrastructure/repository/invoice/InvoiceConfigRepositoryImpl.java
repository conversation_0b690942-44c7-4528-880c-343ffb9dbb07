package net.summerfarm.manage.infrastructure.repository.invoice;

import net.summerfarm.manage.common.input.invoiceConfig.InvoiceConfigVO;
import net.summerfarm.manage.domain.invoice.entity.InvoiceConfig;
import net.summerfarm.manage.domain.invoice.repository.InvoiceConfigRepository;
import net.summerfarm.manage.infrastructure.mapper.invocie.InvoiceConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class InvoiceConfigRepositoryImpl implements InvoiceConfigRepository {
    @Resource
    InvoiceConfigMapper invoiceConfigMapper;
    @Override
    public List<InvoiceConfig> selectByAdminIdsType(InvoiceConfigVO invoiceConfigVO) {
        return invoiceConfigMapper.selectByAdminIdsType(invoiceConfigVO);
    }


    @Override
    public List<InvoiceConfig> selectByMajorAdminId(Long adminId) {
        return invoiceConfigMapper.selectByMajorAdminId(adminId);
    }

    @Override
    public List<InvoiceConfig> selectByMajorByMids(List<Long> mIds) {
        return invoiceConfigMapper.selectByMajorByMids(mIds);
    }

}
