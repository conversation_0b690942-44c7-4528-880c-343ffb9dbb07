package net.summerfarm.manage.infrastructure.converter.order;

import net.summerfarm.manage.infrastructure.model.order.Orders;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.param.command.OrdersCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-01-18 15:49:06
 * @version 1.0
 *
 */
public class OrdersConverter {

    private OrdersConverter() {
        // 无需实现
    }




    public static List<OrdersEntity> toOrdersEntityList(List<Orders> ordersList) {
        if (ordersList == null) {
            return Collections.emptyList();
        }
        List<OrdersEntity> ordersEntityList = new ArrayList<>();
        for (Orders orders : ordersList) {
            ordersEntityList.add(toOrdersEntity(orders));
        }
        return ordersEntityList;
}


    public static OrdersEntity toOrdersEntity(Orders orders) {
        if (orders == null) {
             return null;
        }
        OrdersEntity ordersEntity = new OrdersEntity();
        ordersEntity.setOrderId(orders.getOrderId());
        ordersEntity.setOrderNo(orders.getOrderNo());
        ordersEntity.setMId(orders.getMId());
        ordersEntity.setOrderTime(orders.getOrderTime());
        ordersEntity.setType(orders.getType());
        ordersEntity.setStatus(orders.getStatus());
        ordersEntity.setDeliveryFee(orders.getDeliveryFee());
        ordersEntity.setTotalPrice(orders.getTotalPrice());
        ordersEntity.setRemark(orders.getRemark());
        ordersEntity.setConfirmTime(orders.getConfirmTime());
        ordersEntity.setAreaName(orders.getAreaName());
        ordersEntity.setOutTimes(orders.getOutTimes());
        ordersEntity.setDiscountType(orders.getDiscountType());
        ordersEntity.setOutTimesFee(orders.getOutTimesFee());
        ordersEntity.setAreaNo(orders.getAreaNo());
        ordersEntity.setMSize(orders.getMSize());
        ordersEntity.setDirect(orders.getDirect());
        ordersEntity.setSkuShow(orders.getSkuShow());
        ordersEntity.setRedPackAmount(orders.getRedPackAmount());
        ordersEntity.setCardRuleId(orders.getCardRuleId());
        ordersEntity.setAccountId(orders.getAccountId());
        ordersEntity.setOriginPrice(orders.getOriginPrice());
        ordersEntity.setOutStock(orders.getOutStock());
        ordersEntity.setDiscountCardId(orders.getDiscountCardId());
        ordersEntity.setOrderSaleType(orders.getOrderSaleType());
        ordersEntity.setReceivableStatus(orders.getReceivableStatus());
        ordersEntity.setAdminId(orders.getAdminId());
        ordersEntity.setInvoiceStatus(orders.getInvoiceStatus());
        ordersEntity.setFinancialInvoiceId(orders.getFinancialInvoiceId());
        ordersEntity.setUpdateTime(orders.getUpdateTime());
        ordersEntity.setOperateId(orders.getOperateId());
        ordersEntity.setOrderPayType(orders.getOrderPayType());
        return ordersEntity;
    }








    public static Orders toOrders(OrdersCommandParam param) {
        if (param == null) {
            return null;
        }
        Orders orders = new Orders();
        orders.setOrderId(param.getOrderId());
        orders.setOrderNo(param.getOrderNo());
        orders.setMId(param.getMId());
        orders.setOrderTime(param.getOrderTime());
        orders.setType(param.getType());
        orders.setStatus(param.getStatus());
        orders.setDeliveryFee(param.getDeliveryFee());
        orders.setTotalPrice(param.getTotalPrice());
        orders.setRemark(param.getRemark());
        orders.setConfirmTime(param.getConfirmTime());
        orders.setAreaName(param.getAreaName());
        orders.setOutTimes(param.getOutTimes());
        orders.setDiscountType(param.getDiscountType());
        orders.setOutTimesFee(param.getOutTimesFee());
        orders.setAreaNo(param.getAreaNo());
        orders.setMSize(param.getMSize());
        orders.setDirect(param.getDirect());
        orders.setSkuShow(param.getSkuShow());
        orders.setRedPackAmount(param.getRedPackAmount());
        orders.setCardRuleId(param.getCardRuleId());
        orders.setAccountId(param.getAccountId());
        orders.setOriginPrice(param.getOriginPrice());
        orders.setOutStock(param.getOutStock());
        orders.setDiscountCardId(param.getDiscountCardId());
        orders.setOrderSaleType(param.getOrderSaleType());
        orders.setReceivableStatus(param.getReceivableStatus());
        orders.setAdminId(param.getAdminId());
        orders.setInvoiceStatus(param.getInvoiceStatus());
        orders.setFinancialInvoiceId(param.getFinancialInvoiceId());
        orders.setUpdateTime(param.getUpdateTime());
        orders.setOperateId(param.getOperateId());
        orders.setOrderPayType(param.getOrderPayType());
        return orders;
    }
}
