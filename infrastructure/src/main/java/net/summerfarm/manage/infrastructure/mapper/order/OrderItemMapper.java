package net.summerfarm.manage.infrastructure.mapper.order;

import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.infrastructure.model.order.OrderItem;
import net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-18 15:49:07
 * @version 1.0
 *
 */
@Mapper
public interface OrderItemMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(OrderItem record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(OrderItem record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    OrderItem selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<OrderItem> selectByCondition(OrderItemQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<OrderItemEntity> getPage(OrderItemQueryParam param);

    /**
     * @Describe: 查询省心送订单数量
     * @param orderNo
     * @return
     */
    Integer selectTimingOrderQuantity(String orderNo);

    /**
     * @Describe: 查询订单具体商品信息
     * @param orderNo
     * @return
     */
    List<OrderItem> selectByOrderNo(String orderNo);
    List<OrderItem> selectByOrderNos(@Param("orderNos")List<String> orderNos);

    /**
     * @Describe: 通过订单号修改非空的数据
     * @param orderItem
     * @return
     */
    int updateSelectiveByOrderNo(OrderItem orderItem);



    List<OrderItemEntity> selectByMasterOrderNo(@Param("masterOrderNo")String masterOrderNo);

    List<OrderItem> selectBatchTimingOrderQuantity(@Param("timingOrderNos") List<String> timingOrderNos);

}

