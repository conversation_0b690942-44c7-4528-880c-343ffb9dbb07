package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryRecordCommandParam;
import net.summerfarm.manage.infrastructure.model.product.InventoryRecord;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-05-06 15:10:53
 * @version 1.0
 *
 */
public class InventoryRecordConverter {

    private InventoryRecordConverter() {
        // 无需实现
    }




    public static List<InventoryRecordEntity> toInventoryRecordEntityList(List<InventoryRecord> inventoryRecordList) {
        if (inventoryRecordList == null) {
            return Collections.emptyList();
        }
        List<InventoryRecordEntity> inventoryRecordEntityList = new ArrayList<>();
        for (InventoryRecord inventoryRecord : inventoryRecordList) {
            inventoryRecordEntityList.add(toInventoryRecordEntity(inventoryRecord));
        }
        return inventoryRecordEntityList;
}


    public static InventoryRecordEntity toInventoryRecordEntity(InventoryRecord inventoryRecord) {
        if (inventoryRecord == null) {
             return null;
        }
        InventoryRecordEntity inventoryRecordEntity = new InventoryRecordEntity();
        inventoryRecordEntity.setId(inventoryRecord.getId());
        inventoryRecordEntity.setSku(inventoryRecord.getSku());
        inventoryRecordEntity.setChangeField(inventoryRecord.getChangeField());
        inventoryRecordEntity.setOldValue(inventoryRecord.getOldValue());
        inventoryRecordEntity.setNewValue(inventoryRecord.getNewValue());
        inventoryRecordEntity.setRecorder(inventoryRecord.getRecorder());
        inventoryRecordEntity.setAddtime(inventoryRecord.getAddtime());
        return inventoryRecordEntity;
    }








    public static InventoryRecord toInventoryRecord(InventoryRecordCommandParam param) {
        if (param == null) {
            return null;
        }
        InventoryRecord inventoryRecord = new InventoryRecord();
        inventoryRecord.setId(param.getId());
        inventoryRecord.setSku(param.getSku());
        inventoryRecord.setChangeField(param.getChangeField());
        inventoryRecord.setOldValue(param.getOldValue());
        inventoryRecord.setNewValue(param.getNewValue());
        inventoryRecord.setRecorder(param.getRecorder());
        inventoryRecord.setAddtime(param.getAddtime());
        return inventoryRecord;
    }

    public static List<InventoryRecord> toInventoryRecords(List<InventoryRecordCommandParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.emptyList();
        }

        List<InventoryRecord> inventoryRecordList = new ArrayList<>(params.size());
        for (InventoryRecordCommandParam param : params) {
            inventoryRecordList.add(toInventoryRecord(param));
        }
        return inventoryRecordList;
    }
}
