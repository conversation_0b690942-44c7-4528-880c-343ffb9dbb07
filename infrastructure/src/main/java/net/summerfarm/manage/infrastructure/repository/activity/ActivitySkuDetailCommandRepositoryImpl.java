package net.summerfarm.manage.infrastructure.repository.activity;

import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail;
import net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuDetailMapper;
import net.summerfarm.manage.infrastructure.converter.activity.ActivitySkuDetailConverter;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuDetailCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-04-09 15:06:01
* @version 1.0
*
*/
@Repository
public class ActivitySkuDetailCommandRepositoryImpl implements ActivitySkuDetailCommandRepository {

    @Autowired
    private ActivitySkuDetailMapper activitySkuDetailMapper;
    @Override
    public ActivitySkuDetailEntity insertSelective(ActivitySkuDetailCommandParam param) {
        ActivitySkuDetail activitySkuDetail = ActivitySkuDetailConverter.toActivitySkuDetail(param);
        activitySkuDetailMapper.insertSelective(activitySkuDetail);
        return ActivitySkuDetailConverter.toActivitySkuDetailEntity(activitySkuDetail);
    }

    @Override
    public int batchInsertSelective(List<ActivitySkuDetailCommandParam> params) {
        if (params == null || params.isEmpty()) {
            return 0;
        }

        List<ActivitySkuDetail> activitySkuDetails = params.stream()
                .map(ActivitySkuDetailConverter::toActivitySkuDetail)
                .collect(java.util.stream.Collectors.toList());

        return activitySkuDetailMapper.batchInsertSelective(activitySkuDetails);
    }

    @Override
    public int updateSelectiveById(ActivitySkuDetailCommandParam param){
        return activitySkuDetailMapper.updateSelectiveById(ActivitySkuDetailConverter.toActivitySkuDetail(param));
    }


    @Override
    public int remove(Long id) {
        return activitySkuDetailMapper.remove(id);
    }
}