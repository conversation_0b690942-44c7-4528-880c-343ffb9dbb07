package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.InventoryBind;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryBindMapper;
import net.summerfarm.manage.infrastructure.converter.product.InventoryBindConverter;
import net.summerfarm.manage.domain.product.repository.InventoryBindCommandRepository;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryBindCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-06 13:48:40
* @version 1.0
*
*/
@Repository
public class InventoryBindCommandRepositoryImpl implements InventoryBindCommandRepository {

    @Autowired
    private InventoryBindMapper inventoryBindMapper;
    @Override
    public InventoryBindEntity insertSelective(InventoryBindCommandParam param) {
        InventoryBind inventoryBind = InventoryBindConverter.toInventoryBind(param);
        inventoryBindMapper.insertSelective(inventoryBind);
        return InventoryBindConverter.toInventoryBindEntity(inventoryBind);
    }

    @Override
    public int updateSelectiveById(InventoryBindCommandParam param){
        return inventoryBindMapper.updateSelectiveById(InventoryBindConverter.toInventoryBind(param));
    }


    @Override
    public int remove(Long id) {
        return inventoryBindMapper.remove(id);
    }
}