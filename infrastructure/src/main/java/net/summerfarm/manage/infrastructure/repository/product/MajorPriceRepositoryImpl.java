package net.summerfarm.manage.infrastructure.repository.product;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import javassist.runtime.DotClass;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.major.dto.QuotationExcelDto;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import net.summerfarm.manage.domain.major.valueobject.MajorPriceValueObject;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.entity.MajorPriceLowRemainder;
import net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam;
import net.summerfarm.manage.domain.product.param.query.MajorPriceQueryParam;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.summerfarm.manage.infrastructure.converter.major.MajorPriceConverter;
import net.summerfarm.manage.infrastructure.mapper.product.MajorPriceMapper;
import net.summerfarm.manage.infrastructure.model.product.MajorPrice;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Slf4j
@Component
public class MajorPriceRepositoryImpl implements MajorPriceQueryRepository {

    @Resource
    private MajorPriceMapper majorPriceMapper;

    public List<MajorPriceEntity> queryListMajorPrice(Integer adminId, String sku, List<Integer> areaNos) {
        List<MajorPriceEntity> entityList = majorPriceMapper.queryListMajorPrice(adminId,
                sku, areaNos);
        return entityList;
    }
    public List<MajorPriceEntity> queryListMajorPriceWithoutTime(Integer direct,Long adminId, Set<String> skus, Set<Integer> areaNos) {
        List<MajorPriceEntity> entityList = majorPriceMapper.queryListMajorPriceWithoutTime(direct,adminId, skus, areaNos);
        return entityList;
    }

    @Override
    public List<MajorPriceEntity> queryListMajorPriceByIds(List<Long> ids) {
        List<MajorPriceEntity> entityList = majorPriceMapper.queryListMajorPriceByIds(ids);
        return entityList;
    }
    @Override
    public MajorPriceEntity selectMajorPrice(Long adminId, Integer direct, Integer areaNo, String sku) {
        MajorPriceEntity entity = majorPriceMapper.selectMajorPrice(adminId,direct,areaNo,sku);
        return entity;
    }

    @Override
    public void majorPriceMallShowBatchUpdate(Integer mallShow, Integer direct, Integer adminId) {
        MajorPriceQueryParam param = new MajorPriceQueryParam();
        param.setAdminId (adminId);
        param.setDirect (direct);
        param.setMallShow (mallShow == 0 ? 1 : 0);
        param.setInvalidTime (LocalDateTime.now ());
        param.setValidTime (LocalDateTime.now ());
        List<MajorPrice> majorPrices = majorPriceMapper.selectByCondition (param);
        if(CollectionUtil.isNotEmpty (majorPrices)) {
            majorPriceMapper.updateMallShowByIds (majorPrices.stream ().map (MajorPrice::getId).collect (Collectors.toList ()),mallShow);
        }
    }

    @Override
    public MajorPriceEntity selectLastCommitMajorPrice(Long adminId) {
        return majorPriceMapper.selectLastCommitMajorPrice(adminId);
    }

    @Override
    public PageInfo<MajorPriceFlatObject> selectMajorPricePage(MajorPricePageQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        if (Objects.isNull(pageIndex) || Objects.isNull(pageSize)) {
            return new PageInfo<>(Lists.newArrayList());
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<MajorPriceFlatObject> majorPriceList = majorPriceMapper.selectMajorPriceList(param);
        if(CollectionUtil.isEmpty(majorPriceList)){
            return new PageInfo<>(Lists.newArrayList());
        }

        List<MajorPriceValueObject> collect = majorPriceList.stream().map(MajorPriceConverter.INSTANCE::toValueObject).collect(Collectors.toList());
        param.setLargeNoAndSkuList(collect);
        log.info("开始补充城市信息。param:{}", JSON.toJSONString(param));
        List<MajorPriceItemFlatObject> cityList = majorPriceMapper.selectMajorPriceCityList(param);
        if(CollectionUtil.isEmpty(cityList)){
            return PageInfo.of(majorPriceList);
        }
        // 补充城市信息
        Map<String, List<MajorPriceItemFlatObject>> groupMap = cityList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getLargeAreaNo() + "|" + item.getSku()
                ));
        majorPriceList.forEach(aItem -> {
            String compositeKey = aItem.getLargeAreaNo() + "|" + aItem.getSku();
            List<MajorPriceItemFlatObject> items = groupMap.getOrDefault(compositeKey, new ArrayList<>());
            aItem.setMajorPrices(items);
        });
        return PageInfo.of(majorPriceList);
    }

    @Override
    public PageInfo<MajorPriceItemFlatObject> selectMajorPriceCityPage (MajorPricePageQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        if (Objects.isNull(pageIndex) || Objects.isNull(pageSize)) {
            return new PageInfo<>(Lists.newArrayList());
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<MajorPriceItemFlatObject> majorPriceList = majorPriceMapper.selectMajorPriceCityList(param);
        if(CollectionUtil.isEmpty(majorPriceList)){
            return new PageInfo<>(Lists.newArrayList());
        }
        return PageInfo.of(majorPriceList);
    }

    @Override
    public List<QuotationExcelDto> selectMajorPriceDownloadList(MajorPricePageQueryParam majorPricePageQueryParam) {
        List<MajorPriceItemFlatObject> objects = majorPriceMapper.selectMajorPriceDownloadList(majorPricePageQueryParam);
        if(CollectionUtil.isEmpty(objects)) {
            return Collections.emptyList();
        }

        Map<String, QuotationExcelDto> map = new HashMap<>();
        for (MajorPriceItemFlatObject object : objects) {
            BigDecimal price = PriceCalculator.calculateMajorPriceByType(object.getPrice(), object.getSalePrice(), object.getPriceAdjustmentValue(), object.getPriceType());
            object.setPrice(price);

            String compositeKey = object.getLargeAreaNo() + "|" + object.getSku();
            QuotationExcelDto quotationExcelDto = map.get(compositeKey);
            if(quotationExcelDto == null) {
                quotationExcelDto = MajorPriceConverter.INSTANCE.toQuotationExcelDto(object);
                map.put(compositeKey, quotationExcelDto);
            } else {
                if(price != null) {
                    quotationExcelDto.setMinPrice(NumberUtil.min(quotationExcelDto.getMinPrice(), price));
                    quotationExcelDto.setMaxPrice(NumberUtil.max(quotationExcelDto.getMaxPrice(), price));
                }
            }
        }
        ArrayList<QuotationExcelDto> quotationExcelDtos = new ArrayList<>(map.values());
        quotationExcelDtos.forEach(dto -> {
            if(NumberUtil.equals(dto.getMinPrice(), dto.getMaxPrice())){
                dto.setPrice(dto.getMinPrice().toString());
            } else {
                dto.setPrice(dto.getMinPrice() + "-" + dto.getMaxPrice());
            }
            dto.setValidPeriod(DateUtil.format(dto.getValidTime(), "yyyy.MM.dd") + "-" + DateUtil.format(dto.getInvalidTime(), "yyyy.MM.dd"));
        });
        return quotationExcelDtos;
    }

    @Override
    public List<MajorPriceLowRemainder> selectLowPriceRemainderSku(Integer adminId, Integer areaNo, String sku) {
        return majorPriceMapper.selectLowPriceRemainderSku(adminId, areaNo ,sku);
    }

}
