package net.summerfarm.manage.infrastructure.repository.payment;

import net.summerfarm.manage.infrastructure.model.payment.Payment;
import net.summerfarm.manage.infrastructure.mapper.payment.PaymentMapper;
import net.summerfarm.manage.infrastructure.converter.payment.PaymentConverter;
import net.summerfarm.manage.domain.payment.repository.PaymentCommandRepository;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.PaymentCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-12-23 17:01:31
* @version 1.0
*
*/
@Repository
public class PaymentCommandRepositoryImpl implements PaymentCommandRepository {

    @Autowired
    private PaymentMapper paymentMapper;
    @Override
    public PaymentEntity insertSelective(PaymentCommandParam param) {
        Payment payment = PaymentConverter.toPayment(param);
        paymentMapper.insertSelective(payment);
        return PaymentConverter.toPaymentEntity(payment);
    }

    @Override
    public int updateSelectiveById(PaymentCommandParam param){
        return paymentMapper.updateSelectiveById(PaymentConverter.toPayment(param));
    }


    @Override
    public int remove(Long paymentId) {
        return paymentMapper.remove(paymentId);
    }
}