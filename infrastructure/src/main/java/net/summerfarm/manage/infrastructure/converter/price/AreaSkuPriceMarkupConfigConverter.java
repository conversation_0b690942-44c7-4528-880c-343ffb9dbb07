package net.summerfarm.manage.infrastructure.converter.price;

import net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */

@Mapper
public interface AreaSkuPriceMarkupConfigConverter {

    AreaSkuPriceMarkupConfigConverter INSTANCE = Mappers.getMapper(AreaSkuPriceMarkupConfigConverter.class);


    List<AreaSkuPriceMarkupConfigEntity> toAreaSkuPriceMarkupConfigEntityList(List<AreaSkuPriceMarkupConfig> areaSkuPriceMarkupConfigList);


    AreaSkuPriceMarkupConfigEntity toAreaSkuPriceMarkupConfigEntity(AreaSkuPriceMarkupConfig areaSkuPriceMarkupConfig);


    AreaSkuPriceMarkupConfig toAreaSkuPriceMarkupConfig(AreaSkuPriceMarkupConfigCommandParam param);

    List<AreaSkuPriceMarkupConfig> toAreaSkuPriceMarkupConfigList(List<AreaSkuPriceMarkupConfigCommandParam> paramList);

}
