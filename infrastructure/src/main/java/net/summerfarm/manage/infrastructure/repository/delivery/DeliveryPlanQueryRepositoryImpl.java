package net.summerfarm.manage.infrastructure.repository.delivery;

import net.summerfarm.manage.domain.delivery.entity.DeliveryPlanEntity;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.summerfarm.manage.infrastructure.mapper.delivery.DeliveryPlanMapper;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName DeliveryPlanQueryRepositoryImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 11:25 2024/1/18
 * @Version 1.0
 **/
@Repository
public class DeliveryPlanQueryRepositoryImpl implements DeliveryPlanQueryRepository {

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Override
    public List<DeliveryPlanFlatObject> getAutoConfirmOrder(LocalDate now, String oldOrderNo, int pageIndex, int pageSize) {
        return deliveryPlanMapper.getAutoConfirmOrder(now, oldOrderNo, pageIndex, pageSize);
    }

    @Override
    public Integer getDeliveryPlanQuantity(String orderNo) {
        return deliveryPlanMapper.getDeliveryPlanQuantity(orderNo);
    }

    @Override
    public List<DeliveryPlanFlatObject> noticeLists(LocalDate plusDays, String orderNo, int pageStart, int pageSize) {
        return deliveryPlanMapper.noticeLists(plusDays, orderNo, pageStart, pageSize);
    }

    @Override
    public List<DeliveryPlanFlatObject> timingNoticeLists(LocalDate plusDays, String orderNo, int pageStart, int pageSize) {
        return deliveryPlanMapper.timingNoticeLists(plusDays, orderNo, pageStart, pageSize);
    }

    @Override
    public List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(LocalDate startDate, LocalDate endDate, Integer storeNo) {
        if(startDate == null || endDate == null || storeNo == null){
            throw new BizException("开始时间、结束时间、城配仓编号均不能为空");
        }
        List<NoFreezeProxySaleNoWareNoSkuFlatObject> resultList = deliveryPlanMapper.queryTimingOrderNoFreezeProxySaleNoWarehouse(startDate, endDate, storeNo);
        //过滤数量存在但是SKU和城配仓编号为空的数据
        return resultList.stream().filter(item -> item.getQuantity() != null && item.getStoreNo() != null).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryPlanFlatObject> getWaitingDeliveryPlanQuantity(List<String> normalOrderNos) {
        return deliveryPlanMapper.getWaitingDeliveryPlanQuantity(normalOrderNos);
    }

    @Override
    public List<OrderDeliveryPlanFlatObject> queryValidOrderDeliveryPlanDetail(LocalDate deliveryTime, List<String> orderNoList) {
        return deliveryPlanMapper.queryValidOrderDeliveryPlanDetail(deliveryTime,orderNoList);
    }

    @Override
    public List<DeliveryPlanEntity> getDeliveryPlanByOrderNo(String orderNo) {
        return deliveryPlanMapper.getDeliveryPlanByOrderNo(orderNo);
    }

    @Override
    public Integer getDeliveryPlanQuantityById(Integer id) {
        return deliveryPlanMapper.getDeliveryPlanQuantityById(id);
    }
}
