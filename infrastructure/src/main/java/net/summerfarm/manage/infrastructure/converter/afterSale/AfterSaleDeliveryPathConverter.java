package net.summerfarm.manage.infrastructure.converter.afterSale;

import net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.param.command.AfterSaleDeliveryPathCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-12-31 14:32:57
 * @version 1.0
 *
 */
public class AfterSaleDeliveryPathConverter {

    private AfterSaleDeliveryPathConverter() {
        // 无需实现
    }




    public static List<AfterSaleDeliveryPathEntity> toAfterSaleDeliveryPathEntityList(List<AfterSaleDeliveryPath> afterSaleDeliveryPathList) {
        if (afterSaleDeliveryPathList == null) {
            return Collections.emptyList();
        }
        List<AfterSaleDeliveryPathEntity> afterSaleDeliveryPathEntityList = new ArrayList<>();
        for (AfterSaleDeliveryPath afterSaleDeliveryPath : afterSaleDeliveryPathList) {
            afterSaleDeliveryPathEntityList.add(toAfterSaleDeliveryPathEntity(afterSaleDeliveryPath));
        }
        return afterSaleDeliveryPathEntityList;
}


    public static AfterSaleDeliveryPathEntity toAfterSaleDeliveryPathEntity(AfterSaleDeliveryPath afterSaleDeliveryPath) {
        if (afterSaleDeliveryPath == null) {
             return null;
        }
        AfterSaleDeliveryPathEntity afterSaleDeliveryPathEntity = new AfterSaleDeliveryPathEntity();
        afterSaleDeliveryPathEntity.setId(afterSaleDeliveryPath.getId());
        afterSaleDeliveryPathEntity.setGmtCreate(afterSaleDeliveryPath.getGmtCreate());
        afterSaleDeliveryPathEntity.setGmtModified(afterSaleDeliveryPath.getGmtModified());
        afterSaleDeliveryPathEntity.setMId(afterSaleDeliveryPath.getMId());
        afterSaleDeliveryPathEntity.setDeliveryTime(afterSaleDeliveryPath.getDeliveryTime());
        afterSaleDeliveryPathEntity.setConcatId(afterSaleDeliveryPath.getConcatId());
        afterSaleDeliveryPathEntity.setAfterSaleNo(afterSaleDeliveryPath.getAfterSaleNo());
        afterSaleDeliveryPathEntity.setType(afterSaleDeliveryPath.getType());
        afterSaleDeliveryPathEntity.setOutStoreNo(afterSaleDeliveryPath.getOutStoreNo());
        afterSaleDeliveryPathEntity.setStatus(afterSaleDeliveryPath.getStatus());
        return afterSaleDeliveryPathEntity;
    }








    public static AfterSaleDeliveryPath toAfterSaleDeliveryPath(AfterSaleDeliveryPathCommandParam param) {
        if (param == null) {
            return null;
        }
        AfterSaleDeliveryPath afterSaleDeliveryPath = new AfterSaleDeliveryPath();
        afterSaleDeliveryPath.setId(param.getId());
        afterSaleDeliveryPath.setGmtCreate(param.getGmtCreate());
        afterSaleDeliveryPath.setGmtModified(param.getGmtModified());
        afterSaleDeliveryPath.setMId(param.getMId());
        afterSaleDeliveryPath.setDeliveryTime(param.getDeliveryTime());
        afterSaleDeliveryPath.setConcatId(param.getConcatId());
        afterSaleDeliveryPath.setAfterSaleNo(param.getAfterSaleNo());
        afterSaleDeliveryPath.setType(param.getType());
        afterSaleDeliveryPath.setOutStoreNo(param.getOutStoreNo());
        afterSaleDeliveryPath.setStatus(param.getStatus());
        return afterSaleDeliveryPath;
    }
}
