package net.summerfarm.manage.infrastructure.mapper.activity;

import net.summerfarm.manage.domain.activity.param.query.ActivityScopeQueryParam;
import net.summerfarm.manage.domain.activity.valueObject.ActivityItemScopeValueObject;
import net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo;
import net.summerfarm.manage.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-04-09 15:06:01
 * @version 1.0
 *
 */
@Mapper
public interface ActivityBasicInfoMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ActivityBasicInfo record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ActivityBasicInfo record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ActivityBasicInfo selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<ActivityBasicInfo> selectByCondition(ActivityBasicInfoQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ActivityBasicInfoEntity> getPage(ActivityBasicInfoQueryParam param);


    List<ActivityItemScopeValueObject> listByScope(@Param("list") List<ActivityScopeQueryParam> list, @Param("type") Integer type, @Param("activityStatus") Integer activityStatus);
}

