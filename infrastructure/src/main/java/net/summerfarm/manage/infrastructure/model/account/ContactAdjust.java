package net.summerfarm.manage.infrastructure.model.account;

import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
@Data
public class ContactAdjust {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	
	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 用户id
	 */
	private Long mId;

	/**
	 * 地址表id
	 */
	private Long contactId;

	/**
	 * 新poi
	 */
	private String newPoi;

	/**
	 * 状态 0 待审核, 1 审核通过 ,2 拒绝重新交, 3 审核失败
	 */
	private Integer status;

	/**
	 * 省
	 */
	private String newProvince;

	/**
	 * 市
	 */
	private String newCity;

	/**
	 * 区域
	 */
	private String newArea;

	/**
	 * 详细地址
	 */
	private String newAddress;

	/**
	 * 门牌号
	 */
	private String newHouseNumber;



}