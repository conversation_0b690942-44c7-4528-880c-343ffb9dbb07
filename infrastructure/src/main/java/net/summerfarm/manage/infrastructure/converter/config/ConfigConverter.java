package net.summerfarm.manage.infrastructure.converter.config;

import net.summerfarm.manage.infrastructure.model.config.Config;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.command.ConfigCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-06-18 23:48:54
 * @version 1.0
 *
 */
public class ConfigConverter {

    private ConfigConverter() {
        // 无需实现
    }




    public static List<ConfigEntity> toConfigEntityList(List<Config> configList) {
        if (configList == null) {
            return Collections.emptyList();
        }
        List<ConfigEntity> configEntityList = new ArrayList<>();
        for (Config config : configList) {
            configEntityList.add(toConfigEntity(config));
        }
        return configEntityList;
}


    public static ConfigEntity toConfigEntity(Config config) {
        if (config == null) {
             return null;
        }
        ConfigEntity configEntity = new ConfigEntity();
        configEntity.setId(config.getId());
        configEntity.setKey(config.getKey());
        configEntity.setValue(config.getValue());
        configEntity.setRemark(config.getRemark());
        return configEntity;
    }








    public static Config toConfig(ConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        Config config = new Config();
        config.setId(param.getId());
        config.setKey(param.getKey());
        config.setValue(param.getValue());
        config.setRemark(param.getRemark());
        return config;
    }
}
