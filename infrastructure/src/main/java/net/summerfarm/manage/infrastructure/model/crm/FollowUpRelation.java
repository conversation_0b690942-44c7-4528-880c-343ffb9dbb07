package net.summerfarm.manage.infrastructure.model.crm;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-20 15:02:32
 * @version 1.0
 *
 */
@Data
public class FollowUpRelation {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 商户ID
	 */
	private Long mId;

	/**
	 * 跟进人
	 */
	private Integer adminId;

	/**
	 * 管理员名称
	 */
	private String adminName;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 重新指派标志，0没有，1已重新指派跟进人
	 */
	private Boolean reassign;

	/**
	 * 最近跟进时间
	 */
	private LocalDateTime lastFollowUpTime;

	/**
	 * 重新指派时间
	 */
	private LocalDateTime reassignTime;

	/**
	 * 释放或跟进原因
	 */
	private String reason;

	/**
	 * 新购买标签,无(0)新购买(1)由定时任务处理取消新购买标签(2)
	 */
	private Integer followType;

	/**
	 * 自动释放倒计时
	 */
	private Integer dangerDay;

	/**
	 * 省心送标签:无(0)省心送(1)
	 */
	private Integer timingFollowType;

	/**
	 * 关注人id
	 */
	private Long careBdId;
	/**
	 * 门店ids
	 */
	private List<Long> mIds;



}