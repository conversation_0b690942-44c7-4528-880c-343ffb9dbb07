package net.summerfarm.manage.infrastructure.converter;

import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping;

/**
 * 定制需求sku关联转换器
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public class CustomizationRequestSkuMappingConverter {

    /**
     * 实体转模型
     */
    public static CustomizationRequestSkuMapping entityToModel(CustomizationRequestSkuMappingEntity entity) {
        if (entity == null) {
            return null;
        }
        
        CustomizationRequestSkuMapping model = new CustomizationRequestSkuMapping();
        model.setId(entity.getId());
        model.setCustomizationRequestId(entity.getCustomizationRequestId());
        model.setSku(entity.getSku());
        model.setSourceSku(entity.getSourceSku());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        
        return model;
    }

    /**
     * 模型转实体
     */
    public static CustomizationRequestSkuMappingEntity modelToEntity(CustomizationRequestSkuMapping model) {
        if (model == null) {
            return null;
        }
        
        CustomizationRequestSkuMappingEntity entity = new CustomizationRequestSkuMappingEntity();
        entity.setId(model.getId());
        entity.setCustomizationRequestId(model.getCustomizationRequestId());
//        entity.setProductName(model.getProductName());
        entity.setSku(model.getSku());
        entity.setSourceSku(model.getSourceSku());
        entity.setCreateTime(model.getCreateTime());
        entity.setUpdateTime(model.getUpdateTime());
        
        return entity;
    }
}
