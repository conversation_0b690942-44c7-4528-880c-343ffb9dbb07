package net.summerfarm.manage.infrastructure.mapper.config;

import net.summerfarm.manage.infrastructure.model.config.Config;
import net.summerfarm.manage.domain.config.param.query.ConfigQueryParam;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-06-18 23:48:54
 * @version 1.0
 *
 */
@Mapper
public interface ConfigMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(Config record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(Config record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    Config selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<Config> selectByCondition(ConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ConfigEntity> getPage(ConfigQueryParam param);
}

