package net.summerfarm.manage.infrastructure.repository.order;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.manage.common.enums.AfterSaleOrderStatusEnum;
import net.summerfarm.manage.common.enums.CommonStatus;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import net.summerfarm.manage.domain.order.flatObject.AfterSaleOrderFlatObject;
import net.summerfarm.manage.domain.order.param.query.AfterSaleOrderQueryParam;
import net.summerfarm.manage.domain.order.repository.AfterSaleOrderQueryRepository;
import net.summerfarm.manage.infrastructure.converter.order.AfterSaleOrderConverter;
import net.summerfarm.manage.infrastructure.mapper.order.AfterSaleOrderMapper;
import net.summerfarm.manage.infrastructure.mapper.order.AfterSaleProofMapper;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemMapper;
import net.summerfarm.manage.infrastructure.mapper.order.OrdersMapper;
import net.summerfarm.manage.infrastructure.model.order.OrderItem;
import net.summerfarm.manage.infrastructure.model.order.Orders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
*
* <AUTHOR>
* @date 2024-01-18 16:06:21
* @version 1.0
*
*/
@Repository
public class AfterSaleOrderQueryRepositoryImpl implements AfterSaleOrderQueryRepository {

    @Autowired
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;


    @Override
    public PageInfo<AfterSaleOrderEntity> getPage(AfterSaleOrderQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AfterSaleOrderEntity> entities = afterSaleOrderMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AfterSaleOrderEntity selectById(Long id) {
        return AfterSaleOrderConverter.toAfterSaleOrderEntity(afterSaleOrderMapper.selectById(id));
    }


    @Override
    public List<AfterSaleOrderEntity> selectByCondition(AfterSaleOrderQueryParam param) {
        return AfterSaleOrderConverter.toAfterSaleOrderEntityList(afterSaleOrderMapper.selectByCondition(param));
    }

    @Override
    public Integer getAfterSaleSuccessQuantity(String orderNo) {
        //订单主表信息
        Orders orders = ordersMapper.selectByOrderyNo(orderNo);

        //订单明细表信息
        List<OrderItem> orderItems = orderItemMapper.selectByOrderNo(orderNo);
        OrderItem orderItem = orderItems.get(0);
        String sku = orderItem.getSku();
        Integer suitId = orderItem.getSuitId();

        //查询售后信息
        AfterSaleOrderQueryParam select = new AfterSaleOrderQueryParam();
        select.setOrderNo(orderNo);
        select.setSku(sku);
        select.setSuitId(suitId);
        select.setType(CommonStatus.NO.getCode());
        select.setStatus(AfterSaleOrderStatusEnum.SUCCESS.getStatus());
        List<AfterSaleOrderFlatObject> saleOrderFlatObjects = afterSaleOrderMapper.selectByOrderNo(select);

        //计算售后数量
        Integer afterSaleQuantity = 0;
        if (!CollectionUtils.isEmpty(saleOrderFlatObjects) && Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())) {
            for (AfterSaleOrderFlatObject afterSaleOrder : saleOrderFlatObjects) {
                // 审批、审核失败，补发、换货不计算在内
                if (Objects.equals(afterSaleOrder.getHandleType(), AfterSaleHandleType.EXCHANGE_GOODS.getType())
                        || Objects.equals(afterSaleOrder.getHandleType(), AfterSaleHandleType.DELIVERY_GOODS.getType())) {
                    continue;
                }

                //已到货售后不进行计算
                if (Objects.equals(afterSaleOrder.getDeliveryed(), CommonStatus.YES.getCode())) {
                    continue;
                }
                afterSaleQuantity += afterSaleOrder.getQuantity();
            }
        }
        return afterSaleQuantity;
    }


    @Override
    public List<AfterSaleOrderFlatObject> selectByOrderNo(AfterSaleOrderQueryParam param) {
        return afterSaleOrderMapper.selectByOrderNo(param);
    }

    @Override
    public List<AfterSaleOrderEntity> getAfterSaleInfoByOrderNos(List<String> normalOrderNos) {
        return AfterSaleOrderConverter.toAfterSaleOrderEntityList(afterSaleOrderMapper.getAfterSaleInfoByOrderNos(normalOrderNos));
    }

    @Override
    public List<AfterSaleOrderEntity> getNotDeliveryAfterSaleInfoByOrderNo(String orderNo) {
        return afterSaleOrderMapper.getNotDeliveryAfterSaleInfoByOrderNo(orderNo);
    }
}