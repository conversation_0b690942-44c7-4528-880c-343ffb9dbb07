package net.summerfarm.manage.infrastructure.repository.activity;


import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice;
import net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuPriceMapper;
import net.summerfarm.manage.infrastructure.converter.activity.ActivitySkuPriceConverter;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-04-09 15:06:01
* @version 1.0
*
*/
@Repository
public class ActivitySkuPriceQueryRepositoryImpl implements ActivitySkuPriceQueryRepository {

    @Autowired
    private ActivitySkuPriceMapper activitySkuPriceMapper;


    @Override
    public PageInfo<ActivitySkuPriceEntity> getPage(ActivitySkuPriceQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivitySkuPriceEntity> entities = activitySkuPriceMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivitySkuPriceEntity selectById(Long id) {
        return ActivitySkuPriceConverter.toActivitySkuPriceEntity(activitySkuPriceMapper.selectById(id));
    }


    @Override
    public List<ActivitySkuPriceEntity> selectByCondition(ActivitySkuPriceQueryParam param) {
        return ActivitySkuPriceConverter.toActivitySkuPriceEntityList(activitySkuPriceMapper.selectByCondition(param));
    }

    @Override
    public ActivitySkuPriceEntity selectByDetailId(Long skuDetailId, String sku, Integer areaNo) {
        return ActivitySkuPriceConverter.toActivitySkuPriceEntity(activitySkuPriceMapper.selectByDetailId(skuDetailId, sku, areaNo));
    }

}