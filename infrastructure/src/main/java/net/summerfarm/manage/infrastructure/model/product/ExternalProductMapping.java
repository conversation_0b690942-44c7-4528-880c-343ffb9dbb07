package net.summerfarm.manage.infrastructure.model.product;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Data
public class ExternalProductMapping {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 映射类型 1-sku 2-类目
	 */
	private Integer type;

	/**
	 * 鲜沐内部值
	 */
	private String internalValue;

	/**
	 * 外部平台值
	 */
	private String externalValue;



}