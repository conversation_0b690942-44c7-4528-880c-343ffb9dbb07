package net.summerfarm.manage.infrastructure.repository.product;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.GoodsLocationEntity;
import net.summerfarm.manage.domain.product.param.query.GoodsLocationQueryParam;
import net.summerfarm.manage.domain.product.repository.GoodsLocationQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.GoodsLocationConverter;
import net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:36:00
* @version 1.0
*
*/
@Repository
public class GoodsLocationQueryRepositoryImpl implements GoodsLocationQueryRepository {

    @Autowired
    private GoodsLocationMapper goodsLocationMapper;


    @Override
    public PageInfo<GoodsLocationEntity> getPage(GoodsLocationQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<GoodsLocationEntity> entities = goodsLocationMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public GoodsLocationEntity selectById(Long id) {
        return GoodsLocationConverter.toGoodsLocationEntity(goodsLocationMapper.selectById(id));
    }


    @Override
    public List<GoodsLocationEntity> selectByCondition(GoodsLocationQueryParam param) {
        return GoodsLocationConverter.toGoodsLocationEntityList(goodsLocationMapper.selectByCondition(param));
    }

    @Override
    public GoodsLocationEntity selectByGlNo(String glNo) {
        return GoodsLocationConverter.toGoodsLocationEntity(goodsLocationMapper.selectByGlNo(glNo, null));
    }
}