package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.InventoryBind;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryBindCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-06 13:48:40
 * @version 1.0
 *
 */
public class InventoryBindConverter {

    private InventoryBindConverter() {
        // 无需实现
    }




    public static List<InventoryBindEntity> toInventoryBindEntityList(List<InventoryBind> inventoryBindList) {
        if (inventoryBindList == null) {
            return Collections.emptyList();
        }
        List<InventoryBindEntity> inventoryBindEntityList = new ArrayList<>();
        for (InventoryBind inventoryBind : inventoryBindList) {
            inventoryBindEntityList.add(toInventoryBindEntity(inventoryBind));
        }
        return inventoryBindEntityList;
}


    public static InventoryBindEntity toInventoryBindEntity(InventoryBind inventoryBind) {
        if (inventoryBind == null) {
             return null;
        }
        InventoryBindEntity inventoryBindEntity = new InventoryBindEntity();
        inventoryBindEntity.setId(inventoryBind.getId());
        inventoryBindEntity.setPdId(inventoryBind.getPdId());
        inventoryBindEntity.setSku(inventoryBind.getSku());
        inventoryBindEntity.setBindSku(inventoryBind.getBindSku());
        inventoryBindEntity.setCreateTime(inventoryBind.getCreateTime());
        inventoryBindEntity.setUpdateTime(inventoryBind.getUpdateTime());
        return inventoryBindEntity;
    }








    public static InventoryBind toInventoryBind(InventoryBindCommandParam param) {
        if (param == null) {
            return null;
        }
        InventoryBind inventoryBind = new InventoryBind();
        inventoryBind.setId(param.getId());
        inventoryBind.setPdId(param.getPdId());
        inventoryBind.setSku(param.getSku());
        inventoryBind.setBindSku(param.getBindSku());
        inventoryBind.setCreateTime(param.getCreateTime());
        inventoryBind.setUpdateTime(param.getUpdateTime());
        return inventoryBind;
    }
}
