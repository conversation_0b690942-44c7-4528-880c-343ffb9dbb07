package net.summerfarm.manage.infrastructure.repository.merchant;


import net.summerfarm.manage.infrastructure.model.merchant.MerchantCancel;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantCancelMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantCancelConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantCancelQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2023-12-27 14:01:49
* @version 1.0
*
*/
@Repository
public class MerchantCancelQueryRepositoryImpl implements MerchantCancelQueryRepository {

    @Autowired
    private MerchantCancelMapper merchantCancelMapper;


    @Override
    public PageInfo<MerchantCancelEntity> getPage(MerchantCancelQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantCancelEntity> entities = merchantCancelMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MerchantCancelEntity selectById(Long id) {
        return MerchantCancelConverter.toMerchantCancelEntity(merchantCancelMapper.selectById(id));
    }


    @Override
    public List<MerchantCancelEntity> selectByCondition(MerchantCancelQueryParam param) {
        return MerchantCancelConverter.toMerchantCancelEntityList(merchantCancelMapper.selectByCondition(param));
    }

}