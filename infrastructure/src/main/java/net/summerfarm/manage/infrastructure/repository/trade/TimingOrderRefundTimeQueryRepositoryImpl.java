package net.summerfarm.manage.infrastructure.repository.trade;


import net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime;
import net.summerfarm.manage.infrastructure.mapper.trade.TimingOrderRefundTimeMapper;
import net.summerfarm.manage.infrastructure.converter.trade.TimingOrderRefundTimeConverter;
import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeQueryRepository;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.query.TimingOrderRefundTimeQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-01-22 14:45:54
* @version 1.0
*
*/
@Repository
public class TimingOrderRefundTimeQueryRepositoryImpl implements TimingOrderRefundTimeQueryRepository {

    @Autowired
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;


    @Override
    public PageInfo<TimingOrderRefundTimeEntity> getPage(TimingOrderRefundTimeQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TimingOrderRefundTimeEntity> entities = timingOrderRefundTimeMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TimingOrderRefundTimeEntity selectById(Long id) {
        return TimingOrderRefundTimeConverter.toTimingOrderRefundTimeEntity(timingOrderRefundTimeMapper.selectById(id));
    }


    @Override
    public List<TimingOrderRefundTimeEntity> selectByCondition(TimingOrderRefundTimeQueryParam param) {
        return TimingOrderRefundTimeConverter.toTimingOrderRefundTimeEntityList(timingOrderRefundTimeMapper.selectByCondition(param));
    }

}