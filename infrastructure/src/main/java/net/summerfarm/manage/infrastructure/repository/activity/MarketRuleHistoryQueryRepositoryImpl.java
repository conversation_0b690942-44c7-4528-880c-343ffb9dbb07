package net.summerfarm.manage.infrastructure.repository.activity;


import net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory;
import net.summerfarm.manage.infrastructure.mapper.activity.MarketRuleHistoryMapper;
import net.summerfarm.manage.infrastructure.converter.activity.MarketRuleHistoryConverter;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryQueryRepository;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-31 17:39:38
* @version 1.0
*
*/
@Repository
public class MarketRuleHistoryQueryRepositoryImpl implements MarketRuleHistoryQueryRepository {

    @Autowired
    private MarketRuleHistoryMapper marketRuleHistoryMapper;


    @Override
    public PageInfo<MarketRuleHistoryEntity> getPage(MarketRuleHistoryQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MarketRuleHistoryEntity> entities = marketRuleHistoryMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MarketRuleHistoryEntity selectById(Long id) {
        return MarketRuleHistoryConverter.toMarketRuleHistoryEntity(marketRuleHistoryMapper.selectById(id));
    }


    @Override
    public List<MarketRuleHistoryEntity> selectByCondition(MarketRuleHistoryQueryParam param) {
        return MarketRuleHistoryConverter.toMarketRuleHistoryEntityList(marketRuleHistoryMapper.selectByCondition(param));
    }

}