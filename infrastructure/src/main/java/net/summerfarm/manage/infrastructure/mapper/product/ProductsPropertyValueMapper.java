package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import net.summerfarm.manage.infrastructure.model.product.ProductsPropertyValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
@Mapper
public interface ProductsPropertyValueMapper {

    List<ProductsPropertyValueEntity> listByPdIds(@Param("pdIds") List<Long> pdIds, @Param("propertyIds") List<Integer> propertyIds);

    List<ProductsPropertyValueEntity> listByConditions(ProductsPropertyValueQueryParam params);

    List<ProductsPropertyValueEntity> selectSaleValueBySku(String sku);

    List<ProductsPropertyValueEntity> selectSaleValueBySkuList(@Param("skuList") List<String> skuList);

    /**
     * 不过滤 products_property status
     * @param skuList
     * @param propertyIds
     * @return
     */
    List<ProductsPropertyValueEntity> selectSaleValueBySkuListAndPropertyIds(@Param("skuList") Set<String> skuList, @Param("propertyIds") List<Integer> propertyIds);

    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ProductsPropertyValue record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ProductsPropertyValue record);

    List<ProductsPropertyValueEntity> listBySkuAndPdid(@Param("sku") String sku, @Param("pdId") Long pdId);

    int deleteByPdId(Long pdId);

    List<String> selectPdNoByKeyValue(@Param("categoryId") Integer categoryId, @Param("createType") Integer createType, @Param("productsPropertyId") Integer productsPropertyId, @Param("productsPropertyValue") String productsPropertyValue);

    List<String> selectSkusByKeyValue(@Param("name") String name, @Param("status") Integer status, @Param("type") Integer type, @Param("productsPropertyValues") List<String> productsPropertyValues);

    void deleteByPdIdAndPropertyIds(@Param("pdId") Long pdId, @Param("productsPropertyIds") Set<Integer> productsPropertyIds);

    List<ProductsPropertyValueEntity> selectByPdId(@Param("pdId") Long pdId);
}
