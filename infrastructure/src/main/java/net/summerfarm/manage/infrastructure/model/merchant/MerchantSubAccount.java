package net.summerfarm.manage.infrastructure.model.merchant;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-09-19 13:44:23
 * @version 1.0
 *
 */
@Data
public class MerchantSubAccount {
	/**
	 * 主键、自增
	 */
	private Long accountId;

	/**
	 * 店铺id
	 */
	private Long mId;

	/**
	 * 账号类型：0、母账号 1、子账号
	 */
	private Integer type;

	/**
	 * 联系人
	 */
	private String contact;

	/**
	 * 手机
	 */
	private String phone;

	/**
	 * unionid
	 */
	private String unionid;

	/**
	 * 微信用户id
	 */
	private String openid;

	/**
	 * 小程序openid
	 */
	private String mpOpenid;

	/**
	 * 弹窗标识
	 */
	private Integer popView;

	/**
	 * 合并后首次登录标识：0、首次 1、非首次
	 */
	private Integer firstPopView;

	/**
	 * 可提现金额
	 */
	private Double cashAmount;

	/**
	 * cash_amount更新时间
	 */
	private LocalDateTime cashUpdateTime;

	/**
	 * 登录时间
	 */
	private LocalDateTime loginTime;

	/**
	 * 上次下单时间
	 */
	private LocalDateTime lastOrderTime;

	/**
	 * 审核是否通过（审核不通过直接删除）：0、待审核 1、审核通过  2、注销
	 */
	private Integer status;

	/**
	 * 删除标识：0、已删除 1、未删除
	 */
	private Integer deleteFlag;

	/**
	 * 合并前账号信息
	 */
	private String mInfo;

	/**
	 * 注册时间
	 */
	private LocalDateTime registerTime;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 审核人
	 */
	private Integer auditUser;

	/**
	 * 
	 */
	private LocalDateTime updateTime;



}