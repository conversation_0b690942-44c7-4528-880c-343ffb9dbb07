package net.summerfarm.manage.infrastructure.repository.order;


import net.summerfarm.manage.infrastructure.model.order.AfterSaleProof;
import net.summerfarm.manage.infrastructure.mapper.order.AfterSaleProofMapper;
import net.summerfarm.manage.infrastructure.converter.order.AfterSaleProofConverter;
import net.summerfarm.manage.domain.order.repository.AfterSaleProofQueryRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.query.AfterSaleProofQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-01-18 16:27:13
* @version 1.0
*
*/
@Repository
public class AfterSaleProofQueryRepositoryImpl implements AfterSaleProofQueryRepository {

    @Autowired
    private AfterSaleProofMapper afterSaleProofMapper;


    @Override
    public PageInfo<AfterSaleProofEntity> getPage(AfterSaleProofQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AfterSaleProofEntity> entities = afterSaleProofMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AfterSaleProofEntity selectById(Long id) {
        return AfterSaleProofConverter.toAfterSaleProofEntity(afterSaleProofMapper.selectById(id));
    }


    @Override
    public List<AfterSaleProofEntity> selectByCondition(AfterSaleProofQueryParam param) {
        return AfterSaleProofConverter.toAfterSaleProofEntityList(afterSaleProofMapper.selectByCondition(param));
    }

}