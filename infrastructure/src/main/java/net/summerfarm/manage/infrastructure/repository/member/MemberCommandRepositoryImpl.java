package net.summerfarm.manage.infrastructure.repository.member;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.member.MemberCommandRepository;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantMapper;
import net.summerfarm.manage.infrastructure.model.member.MemberGrade;
import net.summerfarm.manage.infrastructure.model.merchant.Merchant;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/1  14:59
 */
@Slf4j
@Repository
public class MemberCommandRepositoryImpl implements MemberCommandRepository {
    @Resource
    MerchantMapper merchantMapper;

    @Override
    public Boolean calculateMemberGrade(Integer areaNo, String memberRule) {
        //会员规则空时，重置运营服务区所有会员等级信息
        if (memberRule == null || memberRule.isEmpty()) {
            log.info("重置运营服务区所有会员等级信息，运营服务区：{}", areaNo);
            merchantMapper.resetAllMemberGrade(areaNo);
            return true;
        }
        List<MemberGrade> memberGradeList = JSON.parseArray(memberRule, MemberGrade.class);
        if (CollectionUtils.isEmpty(memberGradeList)) {
            log.info("重置运营服务区所有会员等级信息，运营服务区：{}", areaNo);
            merchantMapper.resetAllMemberGrade(areaNo);
            return true;
        }

        //按等级降序排列
        memberGradeList = memberGradeList.stream().sorted((o1,o2) -> o2.getGrade().compareTo(o1.getGrade())).collect(Collectors.toList());

        //分页查询用户信息，计算完会员等级后，更新用户信息
        int pageIndex = 1;
        int pageSize = 500;
        while (true) {
            int pageStart = pageSize * (pageIndex - 1);
            List<Merchant> merchantList = merchantMapper.selectMemberIntegralPage(pageStart, pageSize, areaNo);
            //计算会员等级
            for (Merchant merchant : merchantList) {
                if (merchant.getMemberIntegral() == null) {
                    log.info("MemberCommandRepository[]calculateMemberGrade[]memberIntegral is null! merchant:{}", JSON.toJSONString(merchant));
                    continue;
                }

                try {
                    Integer grade = 0;
                    for (MemberGrade memberGrade: memberGradeList){
                        if (merchant.getMemberIntegral().compareTo(memberGrade.getThreshold()) >= 0){
                            grade = memberGrade.getGrade();
                            break;
                        }
                    }

                    Merchant updateMerchant = new Merchant();
                    updateMerchant.setMId(merchant.getMId());
                    updateMerchant.setMemberIntegral(BigDecimal.ZERO);
                    updateMerchant.setGrade(grade);
                    merchantMapper.updateMemberGrade(merchant);
                } catch (Exception e) {
                    log.warn("MemberCommandRepository[]calculateMemberGrade[]updateMemberGrade fail! merchant:{}, cause:{}",
                            JSON.toJSONString(merchant), JSON.toJSONString(e));
                    continue;
                }
            }
            if(merchantList.size() < pageSize){
                break;
            }
            pageIndex++;
        }


        return true;
    }
}
