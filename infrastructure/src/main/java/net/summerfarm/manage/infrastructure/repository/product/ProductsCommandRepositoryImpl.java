package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.Products;
import net.summerfarm.manage.infrastructure.mapper.product.ProductsMapper;
import net.summerfarm.manage.infrastructure.converter.product.ProductsConverter;
import net.summerfarm.manage.domain.product.repository.ProductsCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-07 11:31:28
* @version 1.0
*
*/
@Repository
public class ProductsCommandRepositoryImpl implements ProductsCommandRepository {

    @Autowired
    private ProductsMapper productsMapper;
    @Override
    public ProductsEntity insertSelective(ProductsCommandParam param) {
        Products products = ProductsConverter.toProducts(param);
        productsMapper.insertSelective(products);
        return ProductsConverter.toProductsEntity(products);
    }

    @Override
    public int updateSelectiveById(ProductsCommandParam param){
        return productsMapper.updateSelectiveById(ProductsConverter.toProducts(param));
    }


    @Override
    public int remove(Long pdId) {
        return productsMapper.remove(pdId);
    }
}