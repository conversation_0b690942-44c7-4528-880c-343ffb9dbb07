package net.summerfarm.manage.infrastructure.model.permission;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Package: net.summerfarm.model.domain
 * @Description: 数据权限
 * @author: <EMAIL>
 * @Date: 2018/9/29
 */
@Data
public class AdminDataPermission implements Serializable {
    private Integer id;

    private Integer adminId;

    private String permissionValue;

    private String permissionName;

    private String type;

    private LocalDateTime addtime;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;


    public AdminDataPermission(){}

}
