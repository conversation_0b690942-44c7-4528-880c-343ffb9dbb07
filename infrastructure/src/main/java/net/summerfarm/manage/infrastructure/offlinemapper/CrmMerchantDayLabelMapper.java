package net.summerfarm.manage.infrastructure.offlinemapper;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CrmMerchantDayLabelMapper {

    /**
     * 根据商户id查询商户标签
     * @param mId 商户id
     * @param dayTag 数据所在日期标识
     * @return 商户标签
     */
    List<String> selectByPrimaryKey(@Param("mId") Long mId, @Param("dayTag") Integer dayTag);

    /**
     * 根据标签查询商户信息
     * @param selectKeys 查询条件
     * @param dayTag 时间标记
     * @return 商户ids
     */
    List<Long> selectMidListByInput(@Param("selectKeys") MerchantQueryInput selectKeys, @Param("dayTag") Integer dayTag);


}