package net.summerfarm.manage.infrastructure.repository.product;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.product.entity.CategoryAllPathEntity;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.CategoryLevelEntity;
import net.summerfarm.manage.domain.product.repository.CategoryQueryRepository;
import net.summerfarm.manage.infrastructure.mapper.product.CategoryMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2024/1/30
 */
@Slf4j
@Component
public class CategoryQueryRepositoryImpl implements CategoryQueryRepository {

    @Resource
    private CategoryMapper categoryMapper;

    @Override
    public List<CategoryLevelEntity> listCategoryLevel(List<Long> categoryIds) {
        List<CategoryLevelEntity> list = categoryMapper.listCategoryLevel(categoryIds);
        return list;
    }

    @Override
    public CategoryEntity selectInfoByPdId(Long pdId) {
        return categoryMapper.selectInfoByPdId(pdId);
    }

    @Override
    public List<Integer> getSubLevelCategoryIds(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return null;
        }

        //查询所有的类目信息
        List<CategoryEntity> categories = categoryMapper.selectTreeNodes();
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }

        //根据parentId不为空进行分组 排除了所有的一级类目 剩下的可能为二级和三级类目
        Map<Integer, List<CategoryEntity>> categoryMap = categories.stream().filter(category -> Objects.nonNull(category.getParentId()))
                .collect(Collectors.groupingBy(CategoryEntity::getParentId));

        //保存子级类目信息
        List<Integer> sublevelCategoryIds = new ArrayList<>();
        categoryIds.stream().forEach(categoryId -> {
            List<CategoryEntity> childrenIds = categoryMap.get(categoryId);

            //假如childrenIds当前集合为空说明为当前categoryId为第三级
            if (CollectionUtils.isEmpty(childrenIds)) {
                sublevelCategoryIds.add(categoryId);
            } else {

                //不为空说明categoryId为二级或者一级 获取childrenIds的Id
                List<Integer> parentIdList = childrenIds.stream().map(CategoryEntity::getId).collect(Collectors.toList());
                parentIdList.stream().forEach(id -> {

                    //根据当前id:一级类目或者二级类目查询
                    List<CategoryEntity> categoryList = categoryMap.get(id);
                    if (CollectionUtils.isEmpty(categoryList)) {
                        //假如为空说明categoryId为二级类目
                        sublevelCategoryIds.add(id);
                    } else {
                        //不为空说明categoryId为一级
                        List<Integer> collect = categoryList.stream().map(CategoryEntity::getId).collect(Collectors.toList());
                        sublevelCategoryIds.addAll(collect);
                    }
                });
            }
        });
        return sublevelCategoryIds;
    }

    @Override
    public List<CategoryAllPathEntity> selectCategoryAllPath(List<Long> categoryIdList) {
        if (CollectionUtil.isEmpty(categoryIdList)) {
            return Lists.newArrayList();
        }
        return categoryMapper.selectCategoryAllPath(categoryIdList);
    }

    @Override
    public List<Long> selectCategoryIdsByFrontId(Long frontCategoryId) {
        return categoryMapper.selectCategoryIdsByFrontId(frontCategoryId);
    }

    @Override
    public CategoryEntity selectByCategoryId(Long categoryId) {
        if (Objects.isNull(categoryId)) {
            return null;
        }
        return categoryMapper.selectById(categoryId);
    }
}
