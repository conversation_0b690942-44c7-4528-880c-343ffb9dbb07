package net.summerfarm.manage.infrastructure.converter.payment;

import net.summerfarm.manage.infrastructure.model.payment.MasterPayment;
import net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.MasterPaymentCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-10-11 14:22:49
 * @version 1.0
 *
 */
public class MasterPaymentConverter {

    private MasterPaymentConverter() {
        // 无需实现
    }




    public static List<MasterPaymentEntity> toMasterPaymentEntityList(List<MasterPayment> masterPaymentList) {
        if (masterPaymentList == null) {
            return Collections.emptyList();
        }
        List<MasterPaymentEntity> masterPaymentEntityList = new ArrayList<>();
        for (MasterPayment masterPayment : masterPaymentList) {
            masterPaymentEntityList.add(toMasterPaymentEntity(masterPayment));
        }
        return masterPaymentEntityList;
}


    public static MasterPaymentEntity toMasterPaymentEntity(MasterPayment masterPayment) {
        if (masterPayment == null) {
             return null;
        }
        MasterPaymentEntity masterPaymentEntity = new MasterPaymentEntity();
        masterPaymentEntity.setId(masterPayment.getId());
        masterPaymentEntity.setPayType(masterPayment.getPayType());
        masterPaymentEntity.setMasterOrderNo(masterPayment.getMasterOrderNo());
        masterPaymentEntity.setTransactionNumber(masterPayment.getTransactionNumber());
        masterPaymentEntity.setMoney(masterPayment.getMoney());
        masterPaymentEntity.setEndTime(masterPayment.getEndTime());
        masterPaymentEntity.setTradeType(masterPayment.getTradeType());
        masterPaymentEntity.setBankType(masterPayment.getBankType());
        masterPaymentEntity.setStatus(masterPayment.getStatus());
        masterPaymentEntity.setErrCode(masterPayment.getErrCode());
        masterPaymentEntity.setErrCodeDes(masterPayment.getErrCodeDes());
        masterPaymentEntity.setCompanyAccountId(masterPayment.getCompanyAccountId());
        masterPaymentEntity.setScanCode(masterPayment.getScanCode());
        masterPaymentEntity.setAccountInfo(masterPayment.getAccountInfo());
        masterPaymentEntity.setBocPayType(masterPayment.getBocPayType());
        masterPaymentEntity.setOnlinePayEndTime(masterPayment.getOnlinePayEndTime());
        masterPaymentEntity.setCreateTime(masterPayment.getCreateTime());
        masterPaymentEntity.setUpdateTime(masterPayment.getUpdateTime());
        return masterPaymentEntity;
    }








    public static MasterPayment toMasterPayment(MasterPaymentCommandParam param) {
        if (param == null) {
            return null;
        }
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setId(param.getId());
        masterPayment.setPayType(param.getPayType());
        masterPayment.setMasterOrderNo(param.getMasterOrderNo());
        masterPayment.setTransactionNumber(param.getTransactionNumber());
        masterPayment.setMoney(param.getMoney());
        masterPayment.setEndTime(param.getEndTime());
        masterPayment.setTradeType(param.getTradeType());
        masterPayment.setBankType(param.getBankType());
        masterPayment.setStatus(param.getStatus());
        masterPayment.setErrCode(param.getErrCode());
        masterPayment.setErrCodeDes(param.getErrCodeDes());
        masterPayment.setCompanyAccountId(param.getCompanyAccountId());
        masterPayment.setScanCode(param.getScanCode());
        masterPayment.setAccountInfo(param.getAccountInfo());
        masterPayment.setBocPayType(param.getBocPayType());
        masterPayment.setOnlinePayEndTime(param.getOnlinePayEndTime());
        masterPayment.setCreateTime(param.getCreateTime());
        masterPayment.setUpdateTime(param.getUpdateTime());
        return masterPayment;
    }
}
