package net.summerfarm.manage.infrastructure.mapper.customization;

import net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定制需求sku关联表 Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface CustomizationRequestSkuMappingMapper {

    /**
     * 新增定制需求sku关联
     * 
     * @param customizationRequestSkuMapping 定制需求sku关联信息
     * @return 影响行数
     */
    int insert(CustomizationRequestSkuMapping customizationRequestSkuMapping);

    /**
     * 批量新增定制需求sku关联
     * 
     * @param list 定制需求sku关联信息列表
     * @return 影响行数
     */
    int batchInsert(List<CustomizationRequestSkuMapping> list);

    /**
     * 根据ID删除定制需求sku关联
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据定制需求ID删除关联的sku
     * 
     * @param customizationRequestId 定制需求ID
     * @return 影响行数
     */
    int deleteByCustomizationRequestId(@Param("customizationRequestId") Long customizationRequestId);

    /**
     * 批量删除定制需求sku关联
     * 
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 更新定制需求sku关联
     * 
     * @param customizationRequestSkuMapping 定制需求sku关联信息
     * @return 影响行数
     */
    int updateById(CustomizationRequestSkuMapping customizationRequestSkuMapping);

    /**
     * 根据ID查询定制需求sku关联
     * 
     * @param id 主键ID
     * @return 定制需求sku关联信息
     */
    CustomizationRequestSkuMapping selectById(@Param("id") Long id);

    /**
     * 根据定制需求ID查询关联的sku列表
     * 
     * @param customizationRequestId 定制需求ID
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMapping> selectByCustomizationRequestId(@Param("customizationRequestId") Long customizationRequestId);

    /**
     * 根据sku查询定制需求sku关联
     * 
     * @param sku sku
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMapping> selectBySku(@Param("sku") String sku);

    /**
     * 根据模版sku查询定制需求sku关联
     * 
     * @param sourceSku 模版sku
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMapping> selectBySourceSku(@Param("sourceSku") String sourceSku);

    /**
     * 分页查询定制需求sku关联列表
     * 
     * @param customizationRequestSkuMapping 查询条件
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMapping> selectList(CustomizationRequestSkuMapping customizationRequestSkuMapping);

    /**
     * 查询定制需求sku关联总数
     *
     * @param customizationRequestSkuMapping 查询条件
     * @return 总数
     */
    long selectCount(CustomizationRequestSkuMapping customizationRequestSkuMapping);

    /**
     * 查询创建时间超过指定时间的记录
     *
     * @param beforeTime 指定时间
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMapping> selectByCreateTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);
}
