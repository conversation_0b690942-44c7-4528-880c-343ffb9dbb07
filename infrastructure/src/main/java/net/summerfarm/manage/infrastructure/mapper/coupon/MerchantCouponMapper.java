package net.summerfarm.manage.infrastructure.mapper.coupon;

import net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon;
import net.summerfarm.manage.domain.coupon.param.query.MerchantCouponQueryParam;
import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-31 15:37:46
 * @version 1.0
 *
 */
@Mapper
public interface MerchantCouponMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MerchantCoupon record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MerchantCoupon record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MerchantCoupon selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<MerchantCoupon> selectByCondition(MerchantCouponQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<MerchantCouponEntity> getPage(MerchantCouponQueryParam param);

    /**
     * @Describe: 通过指定条件删除用户卡券信息
     * @param record
     * @return
     */
    int deleteMerchantCoupon(MerchantCoupon toMerchantCoupon);
}

