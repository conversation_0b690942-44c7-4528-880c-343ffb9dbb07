package net.summerfarm.manage.infrastructure.repository.merchant;


import net.summerfarm.manage.infrastructure.model.merchant.MerchantAccountTransfer;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantAccountTransferMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantAccountTransferConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-01-10 14:07:22
* @version 1.0
*
*/
@Repository
public class MerchantAccountTransferQueryRepositoryImpl implements MerchantAccountTransferQueryRepository {

    @Autowired
    private MerchantAccountTransferMapper merchantAccountTransferMapper;


    @Override
    public PageInfo<MerchantAccountTransferEntity> getPage(MerchantAccountTransferQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantAccountTransferEntity> entities = merchantAccountTransferMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MerchantAccountTransferEntity selectById(Long id) {
        return MerchantAccountTransferConverter.toMerchantAccountTransferEntity(merchantAccountTransferMapper.selectByPrimaryKey(id));
    }


}