package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.ProductLabelValue;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductLabelValueCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-07 14:12:46
 * @version 1.0
 *
 */
public class ProductLabelValueConverter {

    private ProductLabelValueConverter() {
        // 无需实现
    }




    public static List<ProductLabelValueEntity> toProductLabelValueEntityList(List<ProductLabelValue> productLabelValueList) {
        if (productLabelValueList == null) {
            return Collections.emptyList();
        }
        List<ProductLabelValueEntity> productLabelValueEntityList = new ArrayList<>();
        for (ProductLabelValue productLabelValue : productLabelValueList) {
            productLabelValueEntityList.add(toProductLabelValueEntity(productLabelValue));
        }
        return productLabelValueEntityList;
}


    public static ProductLabelValueEntity toProductLabelValueEntity(ProductLabelValue productLabelValue) {
        if (productLabelValue == null) {
             return null;
        }
        ProductLabelValueEntity productLabelValueEntity = new ProductLabelValueEntity();
        productLabelValueEntity.setId(productLabelValue.getId());
        productLabelValueEntity.setSku(productLabelValue.getSku());
        productLabelValueEntity.setLabelId(productLabelValue.getLabelId());
        productLabelValueEntity.setLabelValue(productLabelValue.getLabelValue());
        productLabelValueEntity.setCreatTime(productLabelValue.getCreatTime());
        productLabelValueEntity.setUpdateTime(productLabelValue.getUpdateTime());
        return productLabelValueEntity;
    }








    public static ProductLabelValue toProductLabelValue(ProductLabelValueCommandParam param) {
        if (param == null) {
            return null;
        }
        ProductLabelValue productLabelValue = new ProductLabelValue();
        productLabelValue.setId(param.getId());
        productLabelValue.setSku(param.getSku());
        productLabelValue.setLabelId(param.getLabelId());
        productLabelValue.setLabelValue(param.getLabelValue());
        productLabelValue.setCreatTime(param.getCreatTime());
        productLabelValue.setUpdateTime(param.getUpdateTime());
        return productLabelValue;
    }
}
