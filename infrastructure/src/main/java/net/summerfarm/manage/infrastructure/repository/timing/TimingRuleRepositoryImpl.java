package net.summerfarm.manage.infrastructure.repository.timing;

import net.summerfarm.manage.domain.timing.entity.TimingRuleEntity;
import net.summerfarm.manage.domain.timing.repository.TimingRuleRepository;
import net.summerfarm.manage.infrastructure.converter.TimingRuleConverter;
import net.summerfarm.manage.infrastructure.mapper.timing.TimingRuleMapper;
import net.summerfarm.manage.infrastructure.model.timing.TimingRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 定期送规则仓储实现
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Repository
public class TimingRuleRepositoryImpl implements TimingRuleRepository {

    @Autowired
    private TimingRuleMapper timingRuleMapper;

    @Override
    public void saveBatch(List<TimingRuleEntity> collect) {
        List<TimingRule> timingRuleList = collect.stream ().map (TimingRuleConverter::entityToModel).collect (Collectors.toList ());
        timingRuleMapper.saveBatch (timingRuleList);
    }

    @Override
    public List<TimingRuleEntity> findByAreaAndSkus(Integer areaNo, Set<String> srcSkus) {
        List<TimingRule> models = timingRuleMapper.selectByAreaAndSkus(areaNo, srcSkus);
        return models.stream()
                .map(TimingRuleConverter::modelToEntity)
                .collect(Collectors.toList());
    }
}
