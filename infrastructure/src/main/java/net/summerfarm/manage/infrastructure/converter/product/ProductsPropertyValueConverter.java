package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.ProductsPropertyValue;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-06 16:02:28
 * @version 1.0
 *
 */
public class ProductsPropertyValueConverter {

    private ProductsPropertyValueConverter() {
        // 无需实现
    }




    public static List<ProductsPropertyValueEntity> toProductsPropertyValueEntityList(List<ProductsPropertyValue> productsPropertyValueList) {
        if (productsPropertyValueList == null) {
            return Collections.emptyList();
        }
        List<ProductsPropertyValueEntity> productsPropertyValueEntityList = new ArrayList<>();
        for (ProductsPropertyValue productsPropertyValue : productsPropertyValueList) {
            productsPropertyValueEntityList.add(toProductsPropertyValueEntity(productsPropertyValue));
        }
        return productsPropertyValueEntityList;
}


    public static ProductsPropertyValueEntity toProductsPropertyValueEntity(ProductsPropertyValue productsPropertyValue) {
        if (productsPropertyValue == null) {
             return null;
        }
        ProductsPropertyValueEntity productsPropertyValueEntity = new ProductsPropertyValueEntity();
        productsPropertyValueEntity.setId(productsPropertyValue.getId());
        productsPropertyValueEntity.setPdId(productsPropertyValue.getPdId());
        productsPropertyValueEntity.setSku(productsPropertyValue.getSku());
        productsPropertyValueEntity.setProductsPropertyId(productsPropertyValue.getProductsPropertyId());
        productsPropertyValueEntity.setProductsPropertyValue(productsPropertyValue.getProductsPropertyValue());
        productsPropertyValueEntity.setCreator(productsPropertyValue.getCreator());
        productsPropertyValueEntity.setCreateTime(productsPropertyValue.getCreateTime());
        productsPropertyValueEntity.setUpdateTime(productsPropertyValue.getUpdateTime());
        return productsPropertyValueEntity;
    }








    public static ProductsPropertyValue toProductsPropertyValue(ProductsPropertyValueCommandParam param) {
        if (param == null) {
            return null;
        }
        ProductsPropertyValue productsPropertyValue = new ProductsPropertyValue();
        productsPropertyValue.setId(param.getId());
        productsPropertyValue.setPdId(param.getPdId());
        productsPropertyValue.setSku(param.getSku());
        productsPropertyValue.setProductsPropertyId(param.getProductsPropertyId());
        productsPropertyValue.setProductsPropertyValue(param.getProductsPropertyValue());
        productsPropertyValue.setCreator(param.getCreator());
        productsPropertyValue.setCreateTime(param.getCreateTime());
        productsPropertyValue.setUpdateTime(param.getUpdateTime());
        return productsPropertyValue;
    }
}
