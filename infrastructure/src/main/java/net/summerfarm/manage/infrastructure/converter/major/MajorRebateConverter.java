package net.summerfarm.manage.infrastructure.converter.major;

import net.summerfarm.manage.infrastructure.model.major.MajorRebate;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.param.command.MajorRebateCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-02-27 15:22:25
 * @version 1.0
 *
 */
public class MajorRebateConverter {

    private MajorRebateConverter() {
        // 无需实现
    }




    public static List<MajorRebateEntity> toMajorRebateEntityList(List<MajorRebate> majorRebateList) {
        if (majorRebateList == null) {
            return Collections.emptyList();
        }
        List<MajorRebateEntity> majorRebateEntityList = new ArrayList<>();
        for (MajorRebate majorRebate : majorRebateList) {
            majorRebateEntityList.add(toMajorRebateEntity(majorRebate));
        }
        return majorRebateEntityList;
}


    public static MajorRebateEntity toMajorRebateEntity(MajorRebate majorRebate) {
        if (majorRebate == null) {
             return null;
        }
        MajorRebateEntity majorRebateEntity = new MajorRebateEntity();
        majorRebateEntity.setId(majorRebate.getId());
        majorRebateEntity.setSku(majorRebate.getSku());
        majorRebateEntity.setName(majorRebate.getName());
        majorRebateEntity.setWeight(majorRebate.getWeight());
        majorRebateEntity.setType(majorRebate.getType());
        majorRebateEntity.setNumber(majorRebate.getNumber());
        majorRebateEntity.setAdminId(majorRebate.getAdminId());
        majorRebateEntity.setAreaNo(majorRebate.getAreaNo());
        majorRebateEntity.setAreaName(majorRebate.getAreaName());
        majorRebateEntity.setCate(majorRebate.getCate());
        majorRebateEntity.setStatus(majorRebate.getStatus());
        majorRebateEntity.setAddTime(majorRebate.getAddTime());
        majorRebateEntity.setUpdateTime(majorRebate.getUpdateTime());
        return majorRebateEntity;
    }








    public static MajorRebate toMajorRebate(MajorRebateCommandParam param) {
        if (param == null) {
            return null;
        }
        MajorRebate majorRebate = new MajorRebate();
        majorRebate.setId(param.getId());
        majorRebate.setSku(param.getSku());
        majorRebate.setName(param.getName());
        majorRebate.setWeight(param.getWeight());
        majorRebate.setType(param.getType());
        majorRebate.setNumber(param.getNumber());
        majorRebate.setAdminId(param.getAdminId());
        majorRebate.setAreaNo(param.getAreaNo());
        majorRebate.setAreaName(param.getAreaName());
        majorRebate.setCate(param.getCate());
        majorRebate.setStatus(param.getStatus());
        majorRebate.setAddTime(param.getAddTime());
        majorRebate.setUpdateTime(param.getUpdateTime());
        return majorRebate;
    }
}
