package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantFrequentlyBuyingSkuNotificationConfigMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantFrequentlyBuyingSkuNotificationConfigConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuNotificationConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
@Repository
public class MerchantFrequentlyBuyingSkuNotificationConfigCommandRepositoryImpl implements MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository {

    @Autowired
    private MerchantFrequentlyBuyingSkuNotificationConfigMapper merchantFrequentlyBuyingSkuNotificationConfigMapper;
    @Override
    public MerchantFrequentlyBuyingSkuNotificationConfigEntity insertSelective(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param) {
        MerchantFrequentlyBuyingSkuNotificationConfig merchantFrequentlyBuyingSkuNotificationConfig = MerchantFrequentlyBuyingSkuNotificationConfigConverter.toMerchantFrequentlyBuyingSkuNotificationConfig(param);
        merchantFrequentlyBuyingSkuNotificationConfigMapper.insertSelective(merchantFrequentlyBuyingSkuNotificationConfig);
        return MerchantFrequentlyBuyingSkuNotificationConfigConverter.toMerchantFrequentlyBuyingSkuNotificationConfigEntity(merchantFrequentlyBuyingSkuNotificationConfig);
    }

    @Override
    public int updateSelectiveById(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param){
        return merchantFrequentlyBuyingSkuNotificationConfigMapper.updateSelectiveById(MerchantFrequentlyBuyingSkuNotificationConfigConverter.toMerchantFrequentlyBuyingSkuNotificationConfig(param));
    }


    @Override
    public int remove(Long id) {
        return merchantFrequentlyBuyingSkuNotificationConfigMapper.remove(id);
    }
}