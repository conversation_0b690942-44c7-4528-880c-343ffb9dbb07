package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;
import net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
public class ExternalProductMappingConverter {

    private ExternalProductMappingConverter() {
        // 无需实现
    }




    public static List<ExternalProductMappingEntity> toExternalProductMappingEntityList(List<ExternalProductMapping> externalProductMappingList) {
        if (externalProductMappingList == null) {
            return Collections.emptyList();
        }
        List<ExternalProductMappingEntity> externalProductMappingEntityList = new ArrayList<>();
        for (ExternalProductMapping externalProductMapping : externalProductMappingList) {
            externalProductMappingEntityList.add(toExternalProductMappingEntity(externalProductMapping));
        }
        return externalProductMappingEntityList;
}


    public static ExternalProductMappingEntity toExternalProductMappingEntity(ExternalProductMapping externalProductMapping) {
        if (externalProductMapping == null) {
             return null;
        }
        ExternalProductMappingEntity externalProductMappingEntity = new ExternalProductMappingEntity();
        externalProductMappingEntity.setId(externalProductMapping.getId());
        externalProductMappingEntity.setCreateTime(externalProductMapping.getCreateTime());
        externalProductMappingEntity.setUpdateTime(externalProductMapping.getUpdateTime());
        externalProductMappingEntity.setType(externalProductMapping.getType());
        externalProductMappingEntity.setInternalValue(externalProductMapping.getInternalValue());
        externalProductMappingEntity.setExternalValue(externalProductMapping.getExternalValue());
        return externalProductMappingEntity;
    }


    public static ExternalProductMapping toExternalProductMapping(ExternalProductMappingCommandParam param) {
        if (param == null) {
            return null;
        }
        ExternalProductMapping externalProductMapping = new ExternalProductMapping();
        externalProductMapping.setId(param.getId());
        externalProductMapping.setCreateTime(param.getCreateTime());
        externalProductMapping.setUpdateTime(param.getUpdateTime());
        externalProductMapping.setType(param.getType());
        externalProductMapping.setInternalValue(param.getInternalValue());
        externalProductMapping.setExternalValue(param.getExternalValue());
        return externalProductMapping;
    }
}
