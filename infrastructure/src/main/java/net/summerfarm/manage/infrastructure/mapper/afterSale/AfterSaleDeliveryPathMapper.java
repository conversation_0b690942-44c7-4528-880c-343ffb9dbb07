package net.summerfarm.manage.infrastructure.mapper.afterSale;

import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath;
import net.summerfarm.manage.domain.afterSale.param.query.AfterSaleDeliveryPathQueryParam;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-31 14:32:57
 * @version 1.0
 *
 */
@Mapper
public interface AfterSaleDeliveryPathMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AfterSaleDeliveryPath record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AfterSaleDeliveryPath record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AfterSaleDeliveryPath selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AfterSaleDeliveryPath> selectByCondition(AfterSaleDeliveryPathQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<AfterSaleDeliveryPathEntity> getPage(AfterSaleDeliveryPathQueryParam param);

    /**
     * 查询售后配送计划详情信息
     * @param afterSaleNoList 售后单号
     * @return 结果
     */
    List<AfterSaleDeliveryPathFlatObject> queryValidAfterSaleDeliveryPathDetail(@Param("afterSaleNoList") List<String> afterSaleNoList);
}

