package net.summerfarm.manage.infrastructure.repository.account;

import net.summerfarm.manage.infrastructure.model.account.AccountChange;
import net.summerfarm.manage.infrastructure.mapper.account.AccountChangeMapper;
import net.summerfarm.manage.infrastructure.converter.account.AccountChangeConverter;
import net.summerfarm.manage.domain.account.repository.AccountChangeCommandRepository;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:19
* @version 1.0
*
*/
@Repository
public class AccountChangeCommandRepositoryImpl implements AccountChangeCommandRepository {

    @Autowired
    private AccountChangeMapper accountChangeMapper;

    @Override
    public AccountChangeEntity insertSelective(AccountChangeEntity entity) {
        AccountChange accountChange = AccountChangeConverter.toAccountChange(entity);
        accountChangeMapper.insertSelective(accountChange);
        return AccountChangeConverter.toAccountChangeEntity(accountChange);
    }

    @Override
    public int updateByIdSelective(AccountChangeEntity entity){
        return accountChangeMapper.updateByIdSelective(AccountChangeConverter.toAccountChange(entity));
    }


    @Override
    public int remove(Long id) {
        return accountChangeMapper.remove(id);
    }

}