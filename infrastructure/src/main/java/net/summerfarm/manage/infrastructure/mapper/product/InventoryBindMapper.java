package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.infrastructure.model.product.InventoryBind;
import net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-06 13:48:40
 * @version 1.0
 *
 */
@Mapper
public interface InventoryBindMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(InventoryBind record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(InventoryBind record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    InventoryBind selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<InventoryBind> selectByCondition(InventoryBindQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<InventoryBindEntity> getPage(InventoryBindQueryParam param);

    /**
     * @Describe: 根据条件获取一条信息
     * @param bindQueryParam
     * @return
     */
    InventoryBind selectOneByCondition(InventoryBindQueryParam bindQueryParam);

    /**
     * @Describe: 根据条件获取一条信息
     * @param bindQueryParam
     * @return
     */
    InventoryBind selectByBindSkuAndExtType(InventoryBindQueryParam bindQueryParam);

    List<InventoryBind> selectByPdIdAndExtType(InventoryBindQueryParam bindQueryParam);
}

