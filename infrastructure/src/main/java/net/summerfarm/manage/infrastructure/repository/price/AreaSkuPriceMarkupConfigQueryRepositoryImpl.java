package net.summerfarm.manage.infrastructure.repository.price;


import cn.hutool.core.collection.CollUtil;
import net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig;
import net.summerfarm.manage.infrastructure.mapper.price.AreaSkuPriceMarkupConfigMapper;
import net.summerfarm.manage.infrastructure.converter.price.AreaSkuPriceMarkupConfigConverter;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigQueryRepository;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-03-26 13:59:07
* @version 1.0
*
*/
@Repository
public class AreaSkuPriceMarkupConfigQueryRepositoryImpl implements AreaSkuPriceMarkupConfigQueryRepository {

    @Autowired
    private AreaSkuPriceMarkupConfigMapper areaSkuPriceMarkupConfigMapper;


    @Override
    public PageInfo<AreaSkuPriceMarkupConfigEntity> getPage(AreaSkuPriceMarkupConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AreaSkuPriceMarkupConfigEntity> entities = areaSkuPriceMarkupConfigMapper.getPage(param);
        if(CollUtil.isNotEmpty(entities)) {
            entities.forEach(entity -> entity.setOriginPrice(this.calculateOriginPrice(entity)));
        }
        return PageInfo.of(entities);
    }

    private BigDecimal calculateOriginPrice(AreaSkuPriceMarkupConfigEntity entity) {
        return entity.getSalePrice().subtract(entity.getMarkupValue());
    }

    @Override
    public AreaSkuPriceMarkupConfigEntity selectById(Long id) {
        return AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfigEntity(areaSkuPriceMarkupConfigMapper.selectById(id));
    }


    @Override
    public List<AreaSkuPriceMarkupConfigEntity> selectByCondition(AreaSkuPriceMarkupConfigQueryParam param) {
        return AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfigEntityList(areaSkuPriceMarkupConfigMapper.selectByCondition(param));
    }

}