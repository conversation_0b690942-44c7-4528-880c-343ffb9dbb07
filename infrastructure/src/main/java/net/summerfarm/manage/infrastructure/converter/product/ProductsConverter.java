package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.Products;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-07 11:31:28
 * @version 1.0
 *
 */
public class ProductsConverter {

    private ProductsConverter() {
        // 无需实现
    }




    public static List<ProductsEntity> toProductsEntityList(List<Products> productsList) {
        if (productsList == null) {
            return Collections.emptyList();
        }
        List<ProductsEntity> productsEntityList = new ArrayList<>();
        for (Products products : productsList) {
            productsEntityList.add(toProductsEntity(products));
        }
        return productsEntityList;
}


    public static ProductsEntity toProductsEntity(Products products) {
        if (products == null) {
             return null;
        }
        ProductsEntity productsEntity = new ProductsEntity();
        productsEntity.setPdId(products.getPdId());
        productsEntity.setCategoryId(products.getCategoryId());
        productsEntity.setBrandId(products.getBrandId());
        productsEntity.setPdName(products.getPdName());
        productsEntity.setPddetail(products.getPddetail());
        productsEntity.setDetailPicture(products.getDetailPicture());
        productsEntity.setViewCount(products.getViewCount());
        productsEntity.setPriority(products.getPriority());
        productsEntity.setAfterSaleTime(products.getAfterSaleTime());
        productsEntity.setAfterSaleType(products.getAfterSaleType());
        productsEntity.setAfterSaleUnit(products.getAfterSaleUnit());
        productsEntity.setCreateTime(products.getCreateTime());
        productsEntity.setExpireTime(products.getExpireTime());
        productsEntity.setOutdated(products.getOutdated());
        productsEntity.setStorageLocation(products.getStorageLocation());
        productsEntity.setPdNo(products.getPdNo());
        productsEntity.setOrigin(products.getOrigin());
        productsEntity.setStorageMethod(products.getStorageMethod());
        productsEntity.setSlogan(products.getSlogan());
        productsEntity.setOtherSlogan(products.getOtherSlogan());
        productsEntity.setPicturePath(products.getPicturePath());
        productsEntity.setRefundType(products.getRefundType());
        productsEntity.setQualityTime(products.getQualityTime());
        productsEntity.setQualityTimeUnit(products.getQualityTimeUnit());
        productsEntity.setWarnTime(products.getWarnTime());
        productsEntity.setAddTime(products.getAddTime());
        productsEntity.setUpdateTime(products.getUpdateTime());
        productsEntity.setCreator(products.getCreator());
        productsEntity.setCreateType(products.getCreateType());
        productsEntity.setRealName(products.getRealName());
        productsEntity.setCreateRemark(products.getCreateRemark());
        productsEntity.setAuditStatus(products.getAuditStatus());
        productsEntity.setAuditTime(products.getAuditTime());
        productsEntity.setProductIntroduction(products.getProductIntroduction());
        productsEntity.setAuditor(products.getAuditor());
        productsEntity.setQualityTimeType(products.getQualityTimeType());
        return productsEntity;
    }








    public static Products toProducts(ProductsCommandParam param) {
        if (param == null) {
            return null;
        }
        Products products = new Products();
        products.setPdId(param.getPdId());
        products.setCategoryId(param.getCategoryId());
        products.setBrandId(param.getBrandId());
        products.setPdName(param.getPdName());
        products.setPddetail(param.getPddetail());
        products.setDetailPicture(param.getDetailPicture());
        products.setViewCount(param.getViewCount());
        products.setPriority(param.getPriority());
        products.setAfterSaleTime(param.getAfterSaleTime());
        products.setAfterSaleType(param.getAfterSaleType());
        products.setAfterSaleUnit(param.getAfterSaleUnit());
        products.setCreateTime(param.getCreateTime());
        products.setExpireTime(param.getExpireTime());
        products.setOutdated(param.getOutdated());
        products.setStorageLocation(param.getStorageLocation());
        products.setPdNo(param.getPdNo());
        products.setOrigin(param.getOrigin());
        products.setStorageMethod(param.getStorageMethod());
        products.setSlogan(param.getSlogan());
        products.setOtherSlogan(param.getOtherSlogan());
        products.setPicturePath(param.getPicturePath());
        products.setRefundType(param.getRefundType());
        products.setQualityTime(param.getQualityTime());
        products.setQualityTimeUnit(param.getQualityTimeUnit());
        products.setWarnTime(param.getWarnTime());
        products.setAddTime(param.getAddTime());
        products.setUpdateTime(param.getUpdateTime());
        products.setCreator(param.getCreator());
        products.setCreateType(param.getCreateType());
        products.setRealName(param.getRealName());
        products.setCreateRemark(param.getCreateRemark());
        products.setAuditStatus(param.getAuditStatus());
        products.setAuditTime(param.getAuditTime());
        products.setProductIntroduction(param.getProductIntroduction());
        products.setAuditor(param.getAuditor());
        products.setQualityTimeType(param.getQualityTimeType());
        return products;
    }
}
