package net.summerfarm.manage.infrastructure.repository.coupon;


import net.summerfarm.manage.infrastructure.model.coupon.Coupon;
import net.summerfarm.manage.infrastructure.mapper.coupon.CouponMapper;
import net.summerfarm.manage.infrastructure.converter.coupon.CouponConverter;
import net.summerfarm.manage.domain.coupon.repository.CouponQueryRepository;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.query.CouponQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-12-19 13:49:12
* @version 1.0
*
*/
@Repository
public class CouponQueryRepositoryImpl implements CouponQueryRepository {

    @Autowired
    private CouponMapper couponMapper;


    @Override
    public PageInfo<CouponEntity> getPage(CouponQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<CouponEntity> entities = couponMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public CouponEntity selectById(Long id) {
        return CouponConverter.toCouponEntity(couponMapper.selectById(id));
    }


    @Override
    public List<CouponEntity> selectByCondition(CouponQueryParam param) {
        return CouponConverter.toCouponEntityList(couponMapper.selectByCondition(param));
    }

    @Override
    public List<CouponEntity> selectByIds(List<Long> ids) {
        return CouponConverter.toCouponEntityList(couponMapper.selectByIds(ids));
    }

}