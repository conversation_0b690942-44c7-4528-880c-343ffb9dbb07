package net.summerfarm.manage.infrastructure.mapper.activity;

import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuPrice;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-04-09 15:06:01
 * @version 1.0
 *
 */
@Mapper
public interface ActivitySkuPriceMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ActivitySkuPrice record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ActivitySkuPrice record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ActivitySkuPrice selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<ActivitySkuPrice> selectByCondition(ActivitySkuPriceQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ActivitySkuPriceEntity> getPage(ActivitySkuPriceQueryParam param);

    ActivitySkuPrice selectByDetailId(@Param("skuDetailId") Long skuDetailId, @Param("sku") String sku, @Param("areaNo") Integer areaNo);
}

