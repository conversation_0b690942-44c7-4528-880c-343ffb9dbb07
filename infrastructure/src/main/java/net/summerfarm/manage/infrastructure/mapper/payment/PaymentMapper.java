package net.summerfarm.manage.infrastructure.mapper.payment;

import net.summerfarm.manage.infrastructure.model.payment.Payment;
import net.summerfarm.manage.domain.payment.param.query.PaymentQueryParam;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-23 17:01:31
 * @version 1.0
 *
 */
@Mapper
public interface PaymentMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(Payment record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(Payment record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("paymentId") Long paymentId);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param paymentId
     * @return
     */
    Payment selectById(@Param("paymentId") Long paymentId);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<Payment> selectByCondition(PaymentQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<PaymentEntity> getPage(PaymentQueryParam param);
}

