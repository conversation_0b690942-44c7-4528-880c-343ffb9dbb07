package net.summerfarm.manage.infrastructure.offlinemapper;


import net.summerfarm.manage.domain.offline.entity.DataSynchronizationInformationEntity;
import net.summerfarm.manage.infrastructure.model.offline.DataSynchronizationInformation;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DataSynchronizationInformationMapper {

    /**
     * 根据id查询离线数据表数据变更日期
     * @param id id
     * @return 离线数据表数据变更信息
     */
    DataSynchronizationInformationEntity selectByPrimaryKey(Long id);

    /**
     * 根据表名获取数据更新信息
     * @param tableName 表名
     * @return 数据更新信息
     */
    DataSynchronizationInformationEntity selectByTableName(String tableName);

    List<DataSynchronizationInformationEntity> selectByTableNames(List<String> tableNames, Integer dateFlag);
}