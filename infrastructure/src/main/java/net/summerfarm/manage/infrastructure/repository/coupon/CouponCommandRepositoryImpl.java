package net.summerfarm.manage.infrastructure.repository.coupon;

import net.summerfarm.manage.infrastructure.model.coupon.Coupon;
import net.summerfarm.manage.infrastructure.mapper.coupon.CouponMapper;
import net.summerfarm.manage.infrastructure.converter.coupon.CouponConverter;
import net.summerfarm.manage.domain.coupon.repository.CouponCommandRepository;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.CouponCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-12-19 13:49:12
* @version 1.0
*
*/
@Repository
public class CouponCommandRepositoryImpl implements CouponCommandRepository {

    @Autowired
    private CouponMapper couponMapper;
    @Override
    public CouponEntity insertSelective(CouponCommandParam param) {
        Coupon coupon = CouponConverter.toCoupon(param);
        couponMapper.insertSelective(coupon);
        return CouponConverter.toCouponEntity(coupon);
    }

    @Override
    public int updateSelectiveById(CouponCommandParam param){
        return couponMapper.updateSelectiveById(CouponConverter.toCoupon(param));
    }


    @Override
    public int remove(Long id) {
        return couponMapper.remove(id);
    }
}