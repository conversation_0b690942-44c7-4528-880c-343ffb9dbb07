package net.summerfarm.manage.infrastructure.mapper.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023-09-19 13:44:23
 * @version 1.0
 *
 */
@Mapper
public interface MerchantSubAccountMapper {

    int insert(MerchantSubAccount record);

    int updateSelective(MerchantSubAccount record);

    List<MerchantSubAccount> selectListByPhone(Map selectKey);

    /**
     * 修改mid 切 门店状态
     * @param updateMid  要修改的
     * @param mId    where条件的
     * @return
     */
    Integer updateMain2Base(@Param("updateMid")Long updateMid, @Param("mId") Long mId);
}
