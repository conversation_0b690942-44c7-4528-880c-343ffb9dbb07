package net.summerfarm.manage.infrastructure.repository.offline;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.offline.repository.CrmMerchantDayLabelRepository;
import net.summerfarm.manage.infrastructure.offlinemapper.CrmMerchantDayLabelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class CrmMerchantDayLabelRepositoryImpl implements CrmMerchantDayLabelRepository {

    @Autowired
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;

    @Override
    public List<Long> selectMidListByInput(MerchantQueryInput selectKeys, Integer dayTag) {
        return crmMerchantDayLabelMapper.selectMidListByInput(selectKeys, dayTag);
    }
}
