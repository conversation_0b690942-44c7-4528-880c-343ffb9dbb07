package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyQueryParam;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import net.summerfarm.manage.infrastructure.model.product.ProductsProperty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-04-30 18:26:17
 * @version 1.0
 *
 */
@Mapper
public interface ProductsPropertyMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ProductsProperty record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ProductsProperty record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ProductsProperty selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<ProductsProperty> selectByCondition(ProductsPropertyQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<ProductsPropertyEntity> getPage(ProductsPropertyQueryParam param);

    /**
     * @Describe: 批量查询商品属性基础信息
     * @param ids
     * @return
     */
    List<ProductsProperty> batchByIds(@Param("ids") List<Integer> ids);

    ProductsPropertyEntity selectOneConditions(ProductsPropertyValueQueryParam productsProperty);

    List<ProductsPropertyEntity> selectEffectKeyPropertyByCategoryId(@Param("type") Integer type, @Param("mappingId") Integer categoryId);
}

