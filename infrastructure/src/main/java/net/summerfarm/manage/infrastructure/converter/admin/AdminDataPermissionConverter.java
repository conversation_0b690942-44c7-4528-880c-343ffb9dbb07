package net.summerfarm.manage.infrastructure.converter.admin;

import net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.command.AdminDataPermissionCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-06-19 16:33:43
 * @version 1.0
 *
 */
public class AdminDataPermissionConverter {

    private AdminDataPermissionConverter() {
        // 无需实现
    }




    public static List<AdminDataPermissionEntity> toAdminDataPermissionEntityList(List<AdminDataPermission> adminDataPermissionList) {
        if (adminDataPermissionList == null) {
            return Collections.emptyList();
        }
        List<AdminDataPermissionEntity> adminDataPermissionEntityList = new ArrayList<>();
        for (AdminDataPermission adminDataPermission : adminDataPermissionList) {
            adminDataPermissionEntityList.add(toAdminDataPermissionEntity(adminDataPermission));
        }
        return adminDataPermissionEntityList;
}


    public static AdminDataPermissionEntity toAdminDataPermissionEntity(AdminDataPermission adminDataPermission) {
        if (adminDataPermission == null) {
             return null;
        }
        AdminDataPermissionEntity adminDataPermissionEntity = new AdminDataPermissionEntity();
        adminDataPermissionEntity.setId(adminDataPermission.getId());
        adminDataPermissionEntity.setAdminId(adminDataPermission.getAdminId());
        adminDataPermissionEntity.setPermissionValue(adminDataPermission.getPermissionValue());
        adminDataPermissionEntity.setPermissionName(adminDataPermission.getPermissionName());
        adminDataPermissionEntity.setAddtime(adminDataPermission.getAddtime());
        adminDataPermissionEntity.setType(adminDataPermission.getType());
        return adminDataPermissionEntity;
    }








    public static AdminDataPermission toAdminDataPermission(AdminDataPermissionCommandParam param) {
        if (param == null) {
            return null;
        }
        AdminDataPermission adminDataPermission = new AdminDataPermission();
        adminDataPermission.setId(param.getId());
        adminDataPermission.setAdminId(param.getAdminId());
        adminDataPermission.setPermissionValue(param.getPermissionValue());
        adminDataPermission.setPermissionName(param.getPermissionName());
        adminDataPermission.setAddtime(param.getAddtime());
        adminDataPermission.setType(param.getType());
        return adminDataPermission;
    }
}
