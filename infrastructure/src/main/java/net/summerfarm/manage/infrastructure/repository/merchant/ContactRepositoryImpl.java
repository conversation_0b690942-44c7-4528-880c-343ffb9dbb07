package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.repository.ContactRepository;
import net.summerfarm.manage.infrastructure.converter.merchant.ContactConverter;
import net.summerfarm.manage.infrastructure.mapper.merchant.ContactMapper;
import net.summerfarm.manage.infrastructure.model.merchant.Contact;
import net.xianmu.common.cache.InMemoryCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-19 16:21:24
* @version 1.0
*
*/
@Repository
public class ContactRepositoryImpl implements ContactRepository {

    @Autowired
    private ContactMapper contactMapper;


    @Override
    public ContactEntity selectByPrimaryKey(Long id) {
        return ContactConverter.toContactEntity(contactMapper.selectByPrimaryKey(id));
    }
    

    @Override
    public ContactEntity insertSelective(ContactEntity entity) {
        Contact contact = ContactConverter.toContact(entity);
        contactMapper.insertSelective(contact);
        return ContactConverter.toContactEntity(contact);
    }

    @Override
    @InMemoryCache(expiryTimeInSeconds = 120)
    public ContactEntity selectDefaultContactByMid(Long mId) {
        Contact contact = contactMapper.selectDefaultContactByMid(mId);
        return ContactConverter.toContactEntity(contact);
    }
}