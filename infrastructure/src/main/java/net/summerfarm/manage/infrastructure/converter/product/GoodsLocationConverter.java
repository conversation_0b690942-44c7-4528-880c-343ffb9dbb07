package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.GoodsLocation;
import net.summerfarm.manage.domain.product.entity.GoodsLocationEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-07 14:36:00
 * @version 1.0
 *
 */
public class GoodsLocationConverter {

    private GoodsLocationConverter() {
        // 无需实现
    }




    public static List<GoodsLocationEntity> toGoodsLocationEntityList(List<GoodsLocation> goodsLocationList) {
        if (goodsLocationList == null) {
            return Collections.emptyList();
        }
        List<GoodsLocationEntity> goodsLocationEntityList = new ArrayList<>();
        for (GoodsLocation goodsLocation : goodsLocationList) {
            goodsLocationEntityList.add(toGoodsLocationEntity(goodsLocation));
        }
        return goodsLocationEntityList;
}


    public static GoodsLocationEntity toGoodsLocationEntity(GoodsLocation goodsLocation) {
        if (goodsLocation == null) {
             return null;
        }
        GoodsLocationEntity goodsLocationEntity = new GoodsLocationEntity();
        goodsLocationEntity.setId(goodsLocation.getId());
        goodsLocationEntity.setStoreNo(goodsLocation.getStoreNo());
        goodsLocationEntity.setGlNo(goodsLocation.getGlNo());
        goodsLocationEntity.setAddTime(goodsLocation.getAddTime());
        goodsLocationEntity.setUpdateTime(goodsLocation.getUpdateTime());
        goodsLocationEntity.setTemperature(goodsLocation.getTemperature());
        goodsLocationEntity.setPassageway(goodsLocation.getPassageway());
        goodsLocationEntity.setType(goodsLocation.getType());
        return goodsLocationEntity;
    }








    public static GoodsLocation toGoodsLocation(GoodsLocationCommandParam param) {
        if (param == null) {
            return null;
        }
        GoodsLocation goodsLocation = new GoodsLocation();
        goodsLocation.setId(param.getId());
        goodsLocation.setStoreNo(param.getStoreNo());
        goodsLocation.setGlNo(param.getGlNo());
        goodsLocation.setAddTime(param.getAddTime());
        goodsLocation.setUpdateTime(param.getUpdateTime());
        goodsLocation.setTemperature(param.getTemperature());
        goodsLocation.setPassageway(param.getPassageway());
        goodsLocation.setType(param.getType());
        return goodsLocation;
    }
}
