package net.summerfarm.manage.infrastructure.repository.merchantpool;

import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolDetailEntity;
import net.summerfarm.manage.domain.merchantpool.repository.MerchantPoolDetailRepository;
import net.summerfarm.manage.infrastructure.mapper.merchantPool.converter.MerchantPoolDetailConverter;
import net.summerfarm.manage.infrastructure.mapper.merchantPool.mapper.MerchantPoolDetailMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/22 16:25
 * @PackageName:net.summerfarm.manage.infrastructure.repository.merchantpool
 * @ClassName: MerchantPoolDetailRepositoryImpl
 * @Description: TODO
 * @Version 1.0
 */
@Repository
public class MerchantPoolDetailRepositoryImpl implements MerchantPoolDetailRepository {

    @Resource
    private MerchantPoolDetailMapper merchantPoolDetailMapper;

    @Override
    public List<MerchantPoolDetailEntity> selectByPoolInfoIds(Collection<Long> scopeIds) {
        return MerchantPoolDetailConverter.toMerchantPoolDetailEntityList(merchantPoolDetailMapper.selectByPoolInfoIds(scopeIds));
    }
}
