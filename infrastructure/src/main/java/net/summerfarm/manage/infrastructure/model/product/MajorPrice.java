package net.summerfarm.manage.infrastructure.model.product;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-04-15 14:50:22
 * @version 1.0
 *
 */
@Data
public class MajorPrice {

	/**
	 * 报价单id
	 */
	private Integer id;

	/**
	 * sku编号
	 */
	private String sku;

	/**
	 * 商品名称
	 */
	private String pdName;

	/**
	 * 商品规格
	 */
	private String weight;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 1是账期 2是现结
	 */
	private Integer direct;

	/**
	 * 城市编号
	 */
	private Integer areaNo;

	/**
	 * 城市名称
	 */
	private String areaName;

	/**
	 * 大客户账号id
	 */
	private Integer adminId;

	/**
	 * 当为代仓商品时的支付方式：0：无需支付 1：下单时支付, 非代仓商品为NULL默认
	 */
	private Integer payMethod;

	/**
	 * 报价单的生效时间
	 */
	private LocalDateTime validTime;

	/**
	 * 报价单的失效时间
	 */
	private LocalDateTime invalidTime;

	/**
	 * 商城是否展示 0 展示 1 不展示 类似上下架
	 */
	private Integer mallShow;

	/**
	 *  价格类型：0代表商城价，1合同价（指定价），2合同价（毛利率）， 3商城价上浮，4商城价下浮，5商城价加价，6商城价减价
	 */
	private Integer priceType;

	/**
	 * 毛利率
	 */
	private BigDecimal interestRate;

	/**
	 * 毛利率的固定价
	 */
	private BigDecimal fixedPrice;

	/**
	 * 毛利率的成本价
	 */
	private BigDecimal cost;

	/**
	 * 价格配置规则
	 */
	private String priceTypeRule;

	/**
	 * 原售价
	 */
	private BigDecimal originalPrice;

	/**
	 * 原价格
	 */
	private BigDecimal oldPrice;

	/**
	 * 原成本
	 */
	private BigDecimal oldCost;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 通过低价监控更新价格的时间
	 */
	private LocalDateTime lowPriceUpdateTime;

	/**
	 * 运营大区
	 */
	private Integer largeAreaNo;

	/**
	 * 商城价浮动/加减值
	 */
	private BigDecimal priceAdjustmentValue;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态：0=保存, 1=提交
	 */
	private Integer status;


}