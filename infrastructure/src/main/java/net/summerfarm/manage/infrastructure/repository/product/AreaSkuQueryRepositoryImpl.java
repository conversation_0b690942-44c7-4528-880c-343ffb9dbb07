package net.summerfarm.manage.infrastructure.repository.product;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AreaSkuQueryParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.AreaSkuConverter;
import net.summerfarm.manage.infrastructure.mapper.product.AreaSkuMapper;
import net.summerfarm.manage.infrastructure.model.product.AreaSku;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Slf4j
@Component
public class AreaSkuQueryRepositoryImpl implements AreaSkuQueryRepository {

    @Resource
    private AreaSkuMapper areaSkuMapper;

    public List<AreaSkuEntity> queryListSkuPrice(List<String> skus, List<Integer> areaNos,Boolean onsale) {
        List<AreaSku> areaSkus = areaSkuMapper.queryListSkuPrice(skus, areaNos,onsale);
        return AreaSkuConverter.toAreaSkuEntityList(areaSkus);
    }

    @Override
    public List<AreaSkuEntity> selectVOList(String sku, Integer areaNo) {
        return areaSkuMapper.selectVOList(sku, areaNo);
    }
    public AreaSkuEntity selectValidAndOnSale(Integer areaNo,String sku) {
        return areaSkuMapper.selectValidAndOnSale(sku, areaNo);
    }

    @Override
    public List<AreaSkuEntity> queryAreaSkuBySkuAndAreaNoList(List<AreaSkuQueryParam> param) {
        if (CollectionUtils.isEmpty(param)) {
            return null;
        }
        return AreaSkuConverter.toAreaSkuEntityList(areaSkuMapper.queryAreaSkuBySkuAndAreaNoList(param));
    }

    @Override
    public PageInfo<Integer> pageOnsaleIdsByAreaNo(Set<Integer> areaNos, Integer pageIndex, Integer pageSize) {
        if (Objects.isNull(pageIndex) || Objects.isNull(pageSize)) {
            return new PageInfo<>();
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<Integer> ids = areaSkuMapper.pageOnsaleIdsByAreaNo(areaNos);
        return PageInfo.of(ids);
    }
}
