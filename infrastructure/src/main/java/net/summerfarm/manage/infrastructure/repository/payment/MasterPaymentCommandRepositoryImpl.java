package net.summerfarm.manage.infrastructure.repository.payment;

import net.summerfarm.manage.infrastructure.model.payment.MasterPayment;
import net.summerfarm.manage.infrastructure.mapper.payment.MasterPaymentMapper;
import net.summerfarm.manage.infrastructure.converter.payment.MasterPaymentConverter;
import net.summerfarm.manage.domain.payment.repository.MasterPaymentCommandRepository;
import net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.MasterPaymentCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-10-11 14:22:49
* @version 1.0
*
*/
@Repository
public class MasterPaymentCommandRepositoryImpl implements MasterPaymentCommandRepository {

    @Autowired
    private MasterPaymentMapper masterPaymentMapper;
    @Override
    public MasterPaymentEntity insertSelective(MasterPaymentCommandParam param) {
        MasterPayment masterPayment = MasterPaymentConverter.toMasterPayment(param);
        masterPaymentMapper.insertSelective(masterPayment);
        return MasterPaymentConverter.toMasterPaymentEntity(masterPayment);
    }

    @Override
    public int updateSelectiveById(MasterPaymentCommandParam param){
        return masterPaymentMapper.updateSelectiveById(MasterPaymentConverter.toMasterPayment(param));
    }


    @Override
    public int remove(Long id) {
        return masterPaymentMapper.remove(id);
    }
}