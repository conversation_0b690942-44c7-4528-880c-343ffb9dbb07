package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder;
import net.summerfarm.manage.infrastructure.mapper.order.AfterSaleOrderMapper;
import net.summerfarm.manage.infrastructure.converter.order.AfterSaleOrderConverter;
import net.summerfarm.manage.domain.order.repository.AfterSaleOrderCommandRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleOrderCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-18 16:06:21
* @version 1.0
*
*/
@Repository
public class AfterSaleOrderCommandRepositoryImpl implements AfterSaleOrderCommandRepository {

    @Autowired
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Override
    public AfterSaleOrderEntity insertSelective(AfterSaleOrderCommandParam param) {
        AfterSaleOrder afterSaleOrder = AfterSaleOrderConverter.toAfterSaleOrder(param);
        afterSaleOrderMapper.insertSelective(afterSaleOrder);
        return AfterSaleOrderConverter.toAfterSaleOrderEntity(afterSaleOrder);
    }

    @Override
    public int updateSelectiveById(AfterSaleOrderCommandParam param){
        return afterSaleOrderMapper.updateSelectiveById(AfterSaleOrderConverter.toAfterSaleOrder(param));
    }


    @Override
    public int remove(Long id) {
        return afterSaleOrderMapper.remove(id);
    }
}