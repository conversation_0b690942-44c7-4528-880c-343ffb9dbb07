package net.summerfarm.manage.infrastructure.mapper.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantOuterEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商家外部信息DAO接口
 *
 * <AUTHOR>
 * @Create 2020-09-22
 */
@Repository
public interface MerchantOuterMapper {


    /**
     * 根据m_id查询出已有的关联数据
     *
     * @param exIdList
     * @return
     */
    List<MerchantOuterEntity> selectByMIdList(@Param("mIdList") List<Long> exIdList);


}
