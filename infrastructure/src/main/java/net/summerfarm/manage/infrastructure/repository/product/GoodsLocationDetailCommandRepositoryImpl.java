package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail;
import net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationDetailMapper;
import net.summerfarm.manage.infrastructure.converter.product.GoodsLocationDetailConverter;
import net.summerfarm.manage.domain.product.repository.GoodsLocationDetailCommandRepository;
import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationDetailCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:49
* @version 1.0
*
*/
@Repository
public class GoodsLocationDetailCommandRepositoryImpl implements GoodsLocationDetailCommandRepository {

    @Autowired
    private GoodsLocationDetailMapper goodsLocationDetailMapper;
    @Override
    public GoodsLocationDetailEntity insertSelective(GoodsLocationDetailCommandParam param) {
        GoodsLocationDetail goodsLocationDetail = GoodsLocationDetailConverter.toGoodsLocationDetail(param);
        goodsLocationDetailMapper.insertSelective(goodsLocationDetail);
        return GoodsLocationDetailConverter.toGoodsLocationDetailEntity(goodsLocationDetail);
    }

    @Override
    public int updateSelectiveById(GoodsLocationDetailCommandParam param){
        return goodsLocationDetailMapper.updateSelectiveById(GoodsLocationDetailConverter.toGoodsLocationDetail(param));
    }


    @Override
    public int remove(Long id) {
        return goodsLocationDetailMapper.remove(id);
    }
}