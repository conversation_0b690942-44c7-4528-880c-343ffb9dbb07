package net.summerfarm.manage.infrastructure.converter.sampleApply;

import net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply;
import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import net.summerfarm.manage.domain.sampleApply.param.command.SampleApplyCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-01-02 14:00:39
 * @version 1.0
 *
 */
public class SampleApplyConverter {

    private SampleApplyConverter() {
        // 无需实现
    }




    public static List<SampleApplyEntity> toSampleApplyEntityList(List<SampleApply> sampleApplyList) {
        if (sampleApplyList == null) {
            return Collections.emptyList();
        }
        List<SampleApplyEntity> sampleApplyEntityList = new ArrayList<>();
        for (SampleApply sampleApply : sampleApplyList) {
            sampleApplyEntityList.add(toSampleApplyEntity(sampleApply));
        }
        return sampleApplyEntityList;
}


    public static SampleApplyEntity toSampleApplyEntity(SampleApply sampleApply) {
        if (sampleApply == null) {
             return null;
        }
        SampleApplyEntity sampleApplyEntity = new SampleApplyEntity();
        sampleApplyEntity.setSampleId(sampleApply.getSampleId());
        sampleApplyEntity.setAddTime(sampleApply.getAddTime());
        sampleApplyEntity.setUpdateTime(sampleApply.getUpdateTime());
        sampleApplyEntity.setCreateId(sampleApply.getCreateId());
        sampleApplyEntity.setCreateName(sampleApply.getCreateName());
        sampleApplyEntity.setMId(sampleApply.getMId());
        sampleApplyEntity.setMName(sampleApply.getMName());
        sampleApplyEntity.setGrade(sampleApply.getGrade());
        sampleApplyEntity.setMSize(sampleApply.getMSize());
        sampleApplyEntity.setMPhone(sampleApply.getMPhone());
        sampleApplyEntity.setMContact(sampleApply.getMContact());
        sampleApplyEntity.setContactId(sampleApply.getContactId());
        sampleApplyEntity.setBdId(sampleApply.getBdId());
        sampleApplyEntity.setBdName(sampleApply.getBdName());
        sampleApplyEntity.setStatus(sampleApply.getStatus());
        sampleApplyEntity.setSatisfaction(sampleApply.getSatisfaction());
        sampleApplyEntity.setPurchaseIntention(sampleApply.getPurchaseIntention());
        sampleApplyEntity.setRemark(sampleApply.getRemark());
        sampleApplyEntity.setAreaNo(sampleApply.getAreaNo());
        sampleApplyEntity.setDeliveryTime(sampleApply.getDeliveryTime());
        sampleApplyEntity.setStoreNo(sampleApply.getStoreNo());
        sampleApplyEntity.setRiskLevel(sampleApply.getRiskLevel());
        return sampleApplyEntity;
    }








    public static SampleApply toSampleApply(SampleApplyCommandParam param) {
        if (param == null) {
            return null;
        }
        SampleApply sampleApply = new SampleApply();
        sampleApply.setSampleId(param.getSampleId());
        sampleApply.setAddTime(param.getAddTime());
        sampleApply.setUpdateTime(param.getUpdateTime());
        sampleApply.setCreateId(param.getCreateId());
        sampleApply.setCreateName(param.getCreateName());
        sampleApply.setMId(param.getMId());
        sampleApply.setMName(param.getMName());
        sampleApply.setGrade(param.getGrade());
        sampleApply.setMSize(param.getMSize());
        sampleApply.setMPhone(param.getMPhone());
        sampleApply.setMContact(param.getMContact());
        sampleApply.setContactId(param.getContactId());
        sampleApply.setBdId(param.getBdId());
        sampleApply.setBdName(param.getBdName());
        sampleApply.setStatus(param.getStatus());
        sampleApply.setSatisfaction(param.getSatisfaction());
        sampleApply.setPurchaseIntention(param.getPurchaseIntention());
        sampleApply.setRemark(param.getRemark());
        sampleApply.setAreaNo(param.getAreaNo());
        sampleApply.setDeliveryTime(param.getDeliveryTime());
        sampleApply.setStoreNo(param.getStoreNo());
        sampleApply.setRiskLevel(param.getRiskLevel());
        return sampleApply;
    }
}
