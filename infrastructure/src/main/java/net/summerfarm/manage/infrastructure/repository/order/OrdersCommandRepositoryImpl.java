package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.common.enums.OrderStatusEnum;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.param.command.OrdersCommandParam;
import net.summerfarm.manage.domain.order.repository.OrdersCommandRepository;
import net.summerfarm.manage.infrastructure.converter.order.OrdersConverter;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemMapper;
import net.summerfarm.manage.infrastructure.mapper.order.OrdersMapper;
import net.summerfarm.manage.infrastructure.model.order.OrderItem;
import net.summerfarm.manage.infrastructure.model.order.Orders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:06
* @version 1.0
*
*/
@Repository
public class OrdersCommandRepositoryImpl implements OrdersCommandRepository {

    @Autowired
    private OrdersMapper ordersMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Override
    public OrdersEntity insertSelective(OrdersCommandParam param) {
        Orders orders = OrdersConverter.toOrders(param);
        ordersMapper.insertSelective(orders);
        return OrdersConverter.toOrdersEntity(orders);
    }

    @Override
    public int updateSelectiveById(OrdersCommandParam param){
        return ordersMapper.updateSelectiveById(OrdersConverter.toOrders(param));
    }


    @Override
    public int remove(Long orderId) {
        return ordersMapper.remove(orderId);
    }

    @Override
    public Boolean orderConfirm(String orderNo) {
        //更新订单信息
        Orders orders = new Orders();
        orders.setOrderNo(orderNo);
        orders.setConfirmTime(LocalDateTime.now());
        orders.setStatus(OrderStatusEnum.RECEIVED.getId());
        ordersMapper.updateSelectiveByOrderNo(orders);

        //更新订单明细信息
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderNo(orderNo);
        orderItem.setStatus(OrderStatusEnum.RECEIVED.getId());
        orderItemMapper.updateSelectiveByOrderNo(orderItem);
        return Boolean.TRUE;
    }
}