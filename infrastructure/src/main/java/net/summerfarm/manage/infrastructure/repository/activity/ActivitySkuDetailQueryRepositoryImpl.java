package net.summerfarm.manage.infrastructure.repository.activity;


import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject;
import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail;
import net.summerfarm.manage.infrastructure.mapper.activity.ActivitySkuDetailMapper;
import net.summerfarm.manage.infrastructure.converter.activity.ActivitySkuDetailConverter;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-04-09 15:06:01
* @version 1.0
*
*/
@Repository
public class ActivitySkuDetailQueryRepositoryImpl implements ActivitySkuDetailQueryRepository {

    @Autowired
    private ActivitySkuDetailMapper activitySkuDetailMapper;


    @Override
    public PageInfo<ActivitySkuDetailEntity> getPage(ActivitySkuDetailQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivitySkuDetailEntity> entities = activitySkuDetailMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivitySkuDetailEntity selectById(Long id) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntity(activitySkuDetailMapper.selectById(id));
    }


    @Override
    public List<ActivitySkuDetailEntity> selectByCondition(ActivitySkuDetailQueryParam param) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetailMapper.selectByCondition(param));
    }

    @Override
    public List<ActivitySkuDetailEntity> listByItemConfigs(List<Long> list, String sku) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetailMapper.listByItemConfigs(list, sku));
    }

    @Override
    public List<ActivitySkuDetailEntity> listByItemConfigsAndSkus(List<Long> list, Collection<String> skus) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetailMapper.listByItemConfigsAndSkus(list, skus));
    }

    @Override
    public List<ActivitySkuDetailValueObject> listDetailByItemConfigs(List<Long> list, String sku) {
        return activitySkuDetailMapper.listDetailByItemConfigs(list, sku);
    }

    @Override
    public List<ActivitySkuDetailValueObject> selectByConditionV2(ActivitySkuDetailQueryParam param) {
        return activitySkuDetailMapper.selectByConditionV2(param);
    }
}