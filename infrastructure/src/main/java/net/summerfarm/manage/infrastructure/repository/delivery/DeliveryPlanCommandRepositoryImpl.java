package net.summerfarm.manage.infrastructure.repository.delivery;

import net.summerfarm.manage.domain.delivery.param.command.DeliveryPlanCommandParam;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanCommandRepository;
import net.summerfarm.manage.infrastructure.converter.delivery.DeliveryPlanConverter;
import net.summerfarm.manage.infrastructure.mapper.delivery.DeliveryPlanMapper;
import net.summerfarm.manage.infrastructure.model.delivery.DeliveryPlan;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @ClassName DeliveryPlanCommandRepositoryImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 17:44 2024/1/18
 * @Version 1.0
 **/
@Repository
public class DeliveryPlanCommandRepositoryImpl implements DeliveryPlanCommandRepository {

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Override
    public Boolean updateInfoById(DeliveryPlanCommandParam commandParam) {
        DeliveryPlan deliveryPlan = DeliveryPlanConverter.toDeliveryPlan(commandParam);
        return deliveryPlanMapper.updateInfoById(deliveryPlan) > 0;
    }
}
