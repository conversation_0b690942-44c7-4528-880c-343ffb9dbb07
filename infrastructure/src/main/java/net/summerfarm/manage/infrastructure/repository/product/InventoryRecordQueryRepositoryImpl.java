package net.summerfarm.manage.infrastructure.repository.product;


import net.summerfarm.manage.infrastructure.model.product.InventoryRecord;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryRecordMapper;
import net.summerfarm.manage.infrastructure.converter.product.InventoryRecordConverter;
import net.summerfarm.manage.domain.product.repository.InventoryRecordQueryRepository;
import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import net.summerfarm.manage.domain.product.param.query.InventoryRecordQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-06 15:10:53
* @version 1.0
*
*/
@Repository
public class InventoryRecordQueryRepositoryImpl implements InventoryRecordQueryRepository {

    @Autowired
    private InventoryRecordMapper inventoryRecordMapper;


    @Override
    public PageInfo<InventoryRecordEntity> getPage(InventoryRecordQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<InventoryRecordEntity> entities = inventoryRecordMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public InventoryRecordEntity selectById(Long id) {
        return InventoryRecordConverter.toInventoryRecordEntity(inventoryRecordMapper.selectById(id));
    }


    @Override
    public List<InventoryRecordEntity> selectByCondition(InventoryRecordQueryParam param) {
        return InventoryRecordConverter.toInventoryRecordEntityList(inventoryRecordMapper.selectByCondition(param));
    }

}