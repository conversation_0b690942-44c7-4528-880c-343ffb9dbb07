package net.summerfarm.manage.infrastructure.converter.activity;

import net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.command.MarketRuleHistoryCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-31 17:39:38
 * @version 1.0
 *
 */
public class MarketRuleHistoryConverter {

    private MarketRuleHistoryConverter() {
        // 无需实现
    }




    public static List<MarketRuleHistoryEntity> toMarketRuleHistoryEntityList(List<MarketRuleHistory> marketRuleHistoryList) {
        if (marketRuleHistoryList == null) {
            return Collections.emptyList();
        }
        List<MarketRuleHistoryEntity> marketRuleHistoryEntityList = new ArrayList<>();
        for (MarketRuleHistory marketRuleHistory : marketRuleHistoryList) {
            marketRuleHistoryEntityList.add(toMarketRuleHistoryEntity(marketRuleHistory));
        }
        return marketRuleHistoryEntityList;
}


    public static MarketRuleHistoryEntity toMarketRuleHistoryEntity(MarketRuleHistory marketRuleHistory) {
        if (marketRuleHistory == null) {
             return null;
        }
        MarketRuleHistoryEntity marketRuleHistoryEntity = new MarketRuleHistoryEntity();
        marketRuleHistoryEntity.setId(marketRuleHistory.getId());
        marketRuleHistoryEntity.setDetail(marketRuleHistory.getDetail());
        marketRuleHistoryEntity.setOrderNo(marketRuleHistory.getOrderNo());
        marketRuleHistoryEntity.setMarketRuleId(marketRuleHistory.getMarketRuleId());
        marketRuleHistoryEntity.setValue(marketRuleHistory.getValue());
        marketRuleHistoryEntity.setRuleLevel(marketRuleHistory.getRuleLevel());
        marketRuleHistoryEntity.setType(marketRuleHistory.getType());
        marketRuleHistoryEntity.setCreateTime(marketRuleHistory.getCreateTime());
        marketRuleHistoryEntity.setSendStatus(marketRuleHistory.getSendStatus());
        return marketRuleHistoryEntity;
    }








    public static MarketRuleHistory toMarketRuleHistory(MarketRuleHistoryCommandParam param) {
        if (param == null) {
            return null;
        }
        MarketRuleHistory marketRuleHistory = new MarketRuleHistory();
        marketRuleHistory.setId(param.getId());
        marketRuleHistory.setDetail(param.getDetail());
        marketRuleHistory.setOrderNo(param.getOrderNo());
        marketRuleHistory.setMarketRuleId(param.getMarketRuleId());
        marketRuleHistory.setValue(param.getValue());
        marketRuleHistory.setRuleLevel(param.getRuleLevel());
        marketRuleHistory.setType(param.getType());
        marketRuleHistory.setCreateTime(param.getCreateTime());
        marketRuleHistory.setSendStatus(param.getSendStatus());
        return marketRuleHistory;
    }
}
