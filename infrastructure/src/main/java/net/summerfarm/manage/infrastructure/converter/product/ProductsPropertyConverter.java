package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyCommandParam;
import net.summerfarm.manage.infrastructure.model.product.ProductsProperty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-04-30 18:26:17
 * @version 1.0
 *
 */
public class ProductsPropertyConverter {

    private ProductsPropertyConverter() {
        // 无需实现
    }




    public static List<ProductsPropertyEntity> toProductsPropertyEntityList(List<ProductsProperty> productsPropertyList) {
        if (productsPropertyList == null) {
            return Collections.emptyList();
        }
        List<ProductsPropertyEntity> productsPropertyEntityList = new ArrayList<>();
        for (ProductsProperty productsProperty : productsPropertyList) {
            productsPropertyEntityList.add(toProductsPropertyEntity(productsProperty));
        }
        return productsPropertyEntityList;
}


    public static ProductsPropertyEntity toProductsPropertyEntity(ProductsProperty productsProperty) {
        if (productsProperty == null) {
             return null;
        }
        ProductsPropertyEntity productsPropertyEntity = new ProductsPropertyEntity();
        productsPropertyEntity.setId(productsProperty.getId());
        productsPropertyEntity.setName(productsProperty.getName());
        productsPropertyEntity.setType(productsProperty.getType());
        productsPropertyEntity.setFormatType(productsProperty.getFormatType());
        productsPropertyEntity.setFormatStr(productsProperty.getFormatStr());
        productsPropertyEntity.setStatus(productsProperty.getStatus());
        productsPropertyEntity.setCreator(productsProperty.getCreator());
        productsPropertyEntity.setCreateTime(productsProperty.getCreateTime());
        return productsPropertyEntity;
    }








    public static ProductsProperty toProductsProperty(ProductsPropertyCommandParam param) {
        if (param == null) {
            return null;
        }
        ProductsProperty productsProperty = new ProductsProperty();
        productsProperty.setId(param.getId());
        productsProperty.setName(param.getName());
        productsProperty.setType(param.getType());
        productsProperty.setFormatType(param.getFormatType());
        productsProperty.setFormatStr(param.getFormatStr());
        productsProperty.setStatus(param.getStatus());
        productsProperty.setCreator(param.getCreator());
        productsProperty.setCreateTime(param.getCreateTime());
        return productsProperty;
    }
}
