package net.summerfarm.manage.infrastructure.repository.offline;


import net.summerfarm.manage.domain.offline.entity.CustGradeEntity;
import net.summerfarm.manage.domain.offline.repository.CustGradeQueryRepository;
import net.summerfarm.manage.infrastructure.offlinemapper.CustGradeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-10-24 11:26:16
* @version 1.0
*
*/
@Repository
public class CustGradeQueryRepositoryImpl implements CustGradeQueryRepository {

    @Autowired
    private CustGradeMapper custGradeMapper;


    @Override
    public List<CustGradeEntity> selectTaskDataList(String dateTag, Long startId, int offset) {
        return custGradeMapper.selectTaskDataList(dateTag, startId, offset);
    }
}