package net.summerfarm.manage.infrastructure.mapper.merchantPool.mapper;


import net.summerfarm.manage.infrastructure.model.merchantpool.MerchantPoolDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolDetailMapper {


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<MerchantPoolDetail> selectByPoolInfoIds(@Param("poolInfoIds") Collection<Long> poolInfoIds);
}