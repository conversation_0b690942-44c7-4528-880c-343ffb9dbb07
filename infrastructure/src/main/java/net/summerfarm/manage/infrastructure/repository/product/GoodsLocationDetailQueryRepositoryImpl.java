package net.summerfarm.manage.infrastructure.repository.product;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import net.summerfarm.manage.domain.product.param.query.GoodsLocationDetailQueryParam;
import net.summerfarm.manage.domain.product.repository.GoodsLocationDetailQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.GoodsLocationDetailConverter;
import net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:49
* @version 1.0
*
*/
@Repository
public class GoodsLocationDetailQueryRepositoryImpl implements GoodsLocationDetailQueryRepository {

    @Autowired
    private GoodsLocationDetailMapper goodsLocationDetailMapper;


    @Override
    public PageInfo<GoodsLocationDetailEntity> getPage(GoodsLocationDetailQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<GoodsLocationDetailEntity> entities = goodsLocationDetailMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public GoodsLocationDetailEntity selectById(Long id) {
        return GoodsLocationDetailConverter.toGoodsLocationDetailEntity(goodsLocationDetailMapper.selectById(id));
    }


    @Override
    public List<GoodsLocationDetailEntity> selectByCondition(GoodsLocationDetailQueryParam param) {
        return GoodsLocationDetailConverter.toGoodsLocationDetailEntityList(goodsLocationDetailMapper.selectByCondition(param));
    }

    @Override
    public List<GoodsLocationDetailEntity> selectBySkus(List<String> skus) {
        return goodsLocationDetailMapper.selectBySkus(skus);
    }
}