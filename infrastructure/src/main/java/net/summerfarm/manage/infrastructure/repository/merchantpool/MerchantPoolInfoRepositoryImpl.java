package net.summerfarm.manage.infrastructure.repository.merchantpool;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.summerfarm.manage.domain.merchantpool.repository.MerchantPoolInfoRepository;
import net.summerfarm.manage.infrastructure.converter.merchantpool.MerchantPoolInfoConverter;
import net.summerfarm.manage.infrastructure.mapper.MerchantPoolInfoMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Repository
public class MerchantPoolInfoRepositoryImpl implements MerchantPoolInfoRepository {

    @Resource
    private MerchantPoolInfoMapper merchantPoolInfoMapper;

    @Override
    public List<MerchantPoolInfoEntity> selectByIdIn(Collection<Long> idCollection) {
        if (CollectionUtil.isEmpty(idCollection)) {
            return Collections.emptyList();
        }
        return MerchantPoolInfoConverter.INSTANCE.toEntity(merchantPoolInfoMapper.selectByIdIn(idCollection));
    }
}
