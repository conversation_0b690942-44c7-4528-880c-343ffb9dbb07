<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.admin.InvitecodeMapper">
    <!-- 结果集映射 -->
    <resultMap id="invitecodeResultMap" type="net.summerfarm.manage.infrastructure.model.admin.Invitecode">
		<id column="invite_id" property="inviteId" jdbcType="INTEGER"/>
		<result column="invitecode" property="invitecode" jdbcType="VARCHAR"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="invitecodeColumns">
          t.invite_id,
          t.invitecode,
          t.admin_id,
          t.create_time,
          t.status
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="inviteId != null">
                AND t.invite_id = #{inviteId}
            </if>
			<if test="invitecode != null and invitecode !=''">
                AND t.invitecode = #{invitecode}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="invitecode != null">
                    t.invitecode = #{invitecode},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="invitecodeResultMap" >
        SELECT <include refid="invitecodeColumns" />
        FROM invitecode t
		WHERE t.invite_id = #{inviteId}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.admin.param.query.InvitecodeQueryParam"  resultType="net.summerfarm.manage.domain.admin.entity.InvitecodeEntity" >
        SELECT
            t.invite_id inviteId,
            t.invitecode invitecode,
            t.admin_id adminId,
            t.create_time createTime,
            t.status status
        FROM invitecode t
        <include refid="whereColumnBySelect" />
            ORDER BY t.invite_id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.admin.param.query.InvitecodeQueryParam" resultMap="invitecodeResultMap" >
        SELECT <include refid="invitecodeColumns" />
        FROM invitecode t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.admin.Invitecode" keyProperty="inviteId" useGeneratedKeys="true">
        INSERT INTO invitecode
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="inviteId != null">
				  invite_id,
              </if>
              <if test="invitecode != null">
				  invitecode,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="status != null">
				  status,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="inviteId != null">
				#{inviteId,jdbcType=INTEGER},
              </if>
              <if test="invitecode != null">
				#{invitecode,jdbcType=VARCHAR},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="status != null">
				#{status,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.admin.Invitecode" >
        UPDATE invitecode t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.invite_id = #{inviteId,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.admin.Invitecode" >
        DELETE FROM invitecode
		WHERE invite_id = #{inviteId,jdbcType=INTEGER}
    </delete>



</mapper>