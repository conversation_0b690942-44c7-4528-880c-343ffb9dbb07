<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.admin.AdminDataPermissionMapper">
    <!-- 结果集映射 -->
    <resultMap id="adminDataPermissionResultMap" type="net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="permission_value" property="permissionValue" jdbcType="VARCHAR"/>
		<result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
		<result column="addtime" property="addtime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="adminDataPermissionColumns">
          t.id,
          t.admin_id,
          t.permission_value,
          t.permission_name,
          t.addtime,
          t.type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="permissionValue != null and permissionValue !=''">
                AND t.permission_value = #{permissionValue}
            </if>
			<if test="permissionName != null and permissionName !=''">
                AND t.permission_name = #{permissionName}
            </if>
			<if test="addtime != null">
                AND t.addtime = #{addtime}
            </if>
			<if test="type != null and type !=''">
                AND t.type = #{type}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="permissionValue != null">
                    t.permission_value = #{permissionValue},
                </if>
                <if test="permissionName != null">
                    t.permission_name = #{permissionName},
                </if>
                <if test="addtime != null">
                    t.addtime = #{addtime},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="adminDataPermissionResultMap" >
        SELECT <include refid="adminDataPermissionColumns" />
        FROM admin_data_permission t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam"  resultType="net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity" >
        SELECT
            t.id id,
            t.admin_id adminId,
            t.permission_value permissionValue,
            t.permission_name permissionName,
            t.addtime addtime,
            t.type type
        FROM admin_data_permission t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam" resultMap="adminDataPermissionResultMap" >
        SELECT <include refid="adminDataPermissionColumns" />
        FROM admin_data_permission t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO admin_data_permission
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="permissionValue != null">
				  permission_value,
              </if>
              <if test="permissionName != null">
				  permission_name,
              </if>
              <if test="addtime != null">
				  addtime,
              </if>
              <if test="type != null">
				  type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="permissionValue != null">
				#{permissionValue,jdbcType=VARCHAR},
              </if>
              <if test="permissionName != null">
				#{permissionName,jdbcType=VARCHAR},
              </if>
              <if test="addtime != null">
				#{addtime,jdbcType=TIMESTAMP},
              </if>
              <if test="type != null">
				#{type,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission" >
        UPDATE admin_data_permission t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission" >
        DELETE FROM admin_data_permission
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>