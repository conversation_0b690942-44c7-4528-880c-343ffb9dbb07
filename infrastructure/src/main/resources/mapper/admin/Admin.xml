<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.admin.AdminMapper">
    <!-- 结果集映射 -->
    <resultMap id="adminResultMap" type="net.summerfarm.manage.infrastructure.model.admin.Admin">
		<id column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
		<result column="is_disabled" property="isDisabled" jdbcType="INTEGER"/>
		<result column="username" property="username" jdbcType="VARCHAR"/>
		<result column="password" property="password" jdbcType="VARCHAR"/>
		<result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
		<result column="realname" property="realname" jdbcType="VARCHAR"/>
		<result column="gender" property="gender" jdbcType="INTEGER"/>
		<result column="department" property="department" jdbcType="VARCHAR"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="kp" property="kp" jdbcType="VARCHAR"/>
		<result column="saler_id" property="salerId" jdbcType="INTEGER"/>
		<result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
		<result column="contract" property="contract" jdbcType="VARCHAR"/>
		<result column="contract_method" property="contractMethod" jdbcType="VARCHAR"/>
		<result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
		<result column="operate_id" property="operateId" jdbcType="INTEGER"/>
		<result column="major_cycle" property="majorCycle" jdbcType="INTEGER"/>
		<result column="close_order_type" property="closeOrderType" jdbcType="INTEGER"/>
		<result column="cooperation_stage" property="cooperationStage" jdbcType="SMALLINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="close_order_time" property="closeOrderTime" jdbcType="VARCHAR"/>
		<result column="update_close_order_time" property="updateCloseOrderTime" jdbcType="VARCHAR"/>
		<result column="low_price_remainder" property="lowPriceRemainder" jdbcType="INTEGER"/>
		<result column="not_included_area" property="notIncludedArea" jdbcType="VARCHAR"/>
		<result column="sku_sorting" property="skuSorting" jdbcType="INTEGER"/>
		<result column="admin_type" property="adminType" jdbcType="INTEGER"/>
		<result column="admin_chain" property="adminChain" jdbcType="INTEGER"/>
		<result column="admin_grade" property="adminGrade" jdbcType="INTEGER"/>
		<result column="admin_switch" property="adminSwitch" jdbcType="INTEGER"/>
		<result column="credit_code" property="creditCode" jdbcType="VARCHAR"/>
		<result column="business_license_address" property="businessLicenseAddress" jdbcType="VARCHAR"/>
		<result column="bill_to_pay" property="billToPay" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="adminColumns">
          t.admin_id,
          t.create_time,
          t.login_fail_times,
          t.is_disabled,
          t.username,
          t.password,
          t.login_time,
          t.realname,
          t.gender,
          t.department,
          t.phone,
          t.kp,
          t.saler_id,
          t.saler_name,
          t.contract,
          t.contract_method,
          t.name_remakes,
          t.operate_id,
          t.major_cycle,
          t.close_order_type,
          t.cooperation_stage,
          t.update_time,
          t.close_order_time,
          t.update_close_order_time,
          t.low_price_remainder,
          t.not_included_area,
          t.sku_sorting,
          t.admin_type,
          t.admin_chain,
          t.admin_grade,
          t.admin_switch,
          t.credit_code,
          t.business_license_address,
          t.bill_to_pay
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="loginFailTimes != null">
                AND t.login_fail_times = #{loginFailTimes}
            </if>
			<if test="isDisabled != null">
                AND t.is_disabled = #{isDisabled}
            </if>
			<if test="username != null and username !=''">
                AND t.username = #{username}
            </if>
			<if test="password != null and password !=''">
                AND t.password = #{password}
            </if>
			<if test="loginTime != null">
                AND t.login_time = #{loginTime}
            </if>
			<if test="realname != null and realname !=''">
                AND t.realname like CONCAT('%',#{realname},'%')
            </if>
			<if test="gender != null">
                AND t.gender = #{gender}
            </if>
			<if test="department != null and department !=''">
                AND t.department = #{department}
            </if>
			<if test="phone != null and phone !=''">
                AND t.phone = #{phone}
            </if>
			<if test="kp != null and kp !=''">
                AND t.kp = #{kp}
            </if>
			<if test="salerId != null">
                AND t.saler_id = #{salerId}
            </if>
			<if test="salerName != null and salerName !=''">
                AND t.saler_name = #{salerName}
            </if>
			<if test="contract != null and contract !=''">
                AND t.contract = #{contract}
            </if>
			<if test="contractMethod != null and contractMethod !=''">
                AND t.contract_method = #{contractMethod}
            </if>
			<if test="nameRemakes != null and nameRemakes !=''">
                AND t.name_remakes = #{nameRemakes}
            </if>
			<if test="operateId != null">
                AND t.operate_id = #{operateId}
            </if>
			<if test="majorCycle != null">
                AND t.major_cycle = #{majorCycle}
            </if>
			<if test="closeOrderType != null">
                AND t.close_order_type = #{closeOrderType}
            </if>
			<if test="cooperationStage != null">
                AND t.cooperation_stage = #{cooperationStage}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="closeOrderTime != null and closeOrderTime !=''">
                AND t.close_order_time = #{closeOrderTime}
            </if>
			<if test="updateCloseOrderTime != null and updateCloseOrderTime !=''">
                AND t.update_close_order_time = #{updateCloseOrderTime}
            </if>
			<if test="lowPriceRemainder != null">
                AND t.low_price_remainder = #{lowPriceRemainder}
            </if>
			<if test="notIncludedArea != null and notIncludedArea !=''">
                AND t.not_included_area = #{notIncludedArea}
            </if>
			<if test="skuSorting != null">
                AND t.sku_sorting = #{skuSorting}
            </if>
			<if test="adminType != null">
                AND t.admin_type = #{adminType}
            </if>
			<if test="adminChain != null">
                AND t.admin_chain = #{adminChain}
            </if>
			<if test="adminGrade != null">
                AND t.admin_grade = #{adminGrade}
            </if>
			<if test="adminSwitch != null">
                AND t.admin_switch = #{adminSwitch}
            </if>
			<if test="creditCode != null and creditCode !=''">
                AND t.credit_code = #{creditCode}
            </if>
			<if test="businessLicenseAddress != null and businessLicenseAddress !=''">
                AND t.business_license_address = #{businessLicenseAddress}
            </if>
			<if test="billToPay != null">
                AND t.bill_to_pay = #{billToPay}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="loginFailTimes != null">
                    t.login_fail_times = #{loginFailTimes},
                </if>
                <if test="isDisabled != null">
                    t.is_disabled = #{isDisabled},
                </if>
                <if test="username != null">
                    t.username = #{username},
                </if>
                <if test="password != null">
                    t.password = #{password},
                </if>
                <if test="loginTime != null">
                    t.login_time = #{loginTime},
                </if>
                <if test="realname != null">
                    t.realname = #{realname},
                </if>
                <if test="gender != null">
                    t.gender = #{gender},
                </if>
                <if test="department != null">
                    t.department = #{department},
                </if>
                <if test="phone != null">
                    t.phone = #{phone},
                </if>
                <if test="kp != null">
                    t.kp = #{kp},
                </if>
                <if test="salerId != null">
                    t.saler_id = #{salerId},
                </if>
                <if test="salerName != null">
                    t.saler_name = #{salerName},
                </if>
                <if test="contract != null">
                    t.contract = #{contract},
                </if>
                <if test="contractMethod != null">
                    t.contract_method = #{contractMethod},
                </if>
                <if test="nameRemakes != null">
                    t.name_remakes = #{nameRemakes},
                </if>
                <if test="operateId != null">
                    t.operate_id = #{operateId},
                </if>
                <if test="majorCycle != null">
                    t.major_cycle = #{majorCycle},
                </if>
                <if test="closeOrderType != null">
                    t.close_order_type = #{closeOrderType},
                </if>
                <if test="cooperationStage != null">
                    t.cooperation_stage = #{cooperationStage},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="closeOrderTime != null">
                    t.close_order_time = #{closeOrderTime},
                </if>
                <if test="updateCloseOrderTime != null">
                    t.update_close_order_time = #{updateCloseOrderTime},
                </if>
                <if test="lowPriceRemainder != null">
                    t.low_price_remainder = #{lowPriceRemainder},
                </if>
                <if test="notIncludedArea != null">
                    t.not_included_area = #{notIncludedArea},
                </if>
                <if test="skuSorting != null">
                    t.sku_sorting = #{skuSorting},
                </if>
                <if test="adminType != null">
                    t.admin_type = #{adminType},
                </if>
                <if test="adminChain != null">
                    t.admin_chain = #{adminChain},
                </if>
                <if test="adminGrade != null">
                    t.admin_grade = #{adminGrade},
                </if>
                <if test="adminSwitch != null">
                    t.admin_switch = #{adminSwitch},
                </if>
                <if test="creditCode != null">
                    t.credit_code = #{creditCode},
                </if>
                <if test="businessLicenseAddress != null">
                    t.business_license_address = #{businessLicenseAddress},
                </if>
                <if test="billToPay != null">
                    t.bill_to_pay = #{billToPay},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="adminResultMap" >
        SELECT <include refid="adminColumns" />
        FROM admin t
		WHERE t.admin_id = #{adminId}
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" resultMap="adminResultMap" >
        SELECT <include refid="adminColumns" />
        FROM admin t
        <include refid="whereColumnBySelect"></include>
    </select>

    <!-- 根据条件查询对象 -->
    <select id="selectOne" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" resultMap="adminResultMap" >
        SELECT <include refid="adminColumns" />
        FROM admin t
        <include refid="whereColumnBySelect"></include>
    </select>

	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" >
        INSERT INTO admin
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="loginFailTimes != null">
				  login_fail_times,
              </if>
              <if test="isDisabled != null">
				  is_disabled,
              </if>
              <if test="username != null">
				  username,
              </if>
              <if test="password != null">
				  password,
              </if>
              <if test="loginTime != null">
				  login_time,
              </if>
              <if test="realname != null">
				  realname,
              </if>
              <if test="gender != null">
				  gender,
              </if>
              <if test="department != null">
				  department,
              </if>
              <if test="phone != null">
				  phone,
              </if>
              <if test="kp != null">
				  kp,
              </if>
              <if test="salerId != null">
				  saler_id,
              </if>
              <if test="salerName != null">
				  saler_name,
              </if>
              <if test="contract != null">
				  contract,
              </if>
              <if test="contractMethod != null">
				  contract_method,
              </if>
              <if test="nameRemakes != null">
				  name_remakes,
              </if>
              <if test="operateId != null">
				  operate_id,
              </if>
              <if test="majorCycle != null">
				  major_cycle,
              </if>
              <if test="closeOrderType != null">
				  close_order_type,
              </if>
              <if test="cooperationStage != null">
				  cooperation_stage,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="closeOrderTime != null">
				  close_order_time,
              </if>
              <if test="updateCloseOrderTime != null">
				  update_close_order_time,
              </if>
              <if test="lowPriceRemainder != null">
				  low_price_remainder,
              </if>
              <if test="notIncludedArea != null">
				  not_included_area,
              </if>
              <if test="skuSorting != null">
				  sku_sorting,
              </if>
              <if test="adminType != null">
				  admin_type,
              </if>
              <if test="adminChain != null">
				  admin_chain,
              </if>
              <if test="adminGrade != null">
				  admin_grade,
              </if>
              <if test="adminSwitch != null">
				  admin_switch,
              </if>
              <if test="creditCode != null">
				  credit_code,
              </if>
              <if test="businessLicenseAddress != null">
				  business_license_address,
              </if>
              <if test="billToPay != null">
				  bill_to_pay,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="loginFailTimes != null">
				#{loginFailTimes,jdbcType=INTEGER},
              </if>
              <if test="isDisabled != null">
				#{isDisabled,jdbcType=INTEGER},
              </if>
              <if test="username != null">
				#{username,jdbcType=VARCHAR},
              </if>
              <if test="password != null">
				#{password,jdbcType=VARCHAR},
              </if>
              <if test="loginTime != null">
				#{loginTime,jdbcType=TIMESTAMP},
              </if>
              <if test="realname != null">
				#{realname,jdbcType=VARCHAR},
              </if>
              <if test="gender != null">
				#{gender,jdbcType=INTEGER},
              </if>
              <if test="department != null">
				#{department,jdbcType=VARCHAR},
              </if>
              <if test="phone != null">
				#{phone,jdbcType=VARCHAR},
              </if>
              <if test="kp != null">
				#{kp,jdbcType=VARCHAR},
              </if>
              <if test="salerId != null">
				#{salerId,jdbcType=INTEGER},
              </if>
              <if test="salerName != null">
				#{salerName,jdbcType=VARCHAR},
              </if>
              <if test="contract != null">
				#{contract,jdbcType=VARCHAR},
              </if>
              <if test="contractMethod != null">
				#{contractMethod,jdbcType=VARCHAR},
              </if>
              <if test="nameRemakes != null">
				#{nameRemakes,jdbcType=VARCHAR},
              </if>
              <if test="operateId != null">
				#{operateId,jdbcType=INTEGER},
              </if>
              <if test="majorCycle != null">
				#{majorCycle,jdbcType=INTEGER},
              </if>
              <if test="closeOrderType != null">
				#{closeOrderType,jdbcType=INTEGER},
              </if>
              <if test="cooperationStage != null">
				#{cooperationStage,jdbcType=SMALLINT},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="closeOrderTime != null">
				#{closeOrderTime,jdbcType=VARCHAR},
              </if>
              <if test="updateCloseOrderTime != null">
				#{updateCloseOrderTime,jdbcType=VARCHAR},
              </if>
              <if test="lowPriceRemainder != null">
				#{lowPriceRemainder,jdbcType=INTEGER},
              </if>
              <if test="notIncludedArea != null">
				#{notIncludedArea,jdbcType=VARCHAR},
              </if>
              <if test="skuSorting != null">
				#{skuSorting,jdbcType=INTEGER},
              </if>
              <if test="adminType != null">
				#{adminType,jdbcType=INTEGER},
              </if>
              <if test="adminChain != null">
				#{adminChain,jdbcType=INTEGER},
              </if>
              <if test="adminGrade != null">
				#{adminGrade,jdbcType=INTEGER},
              </if>
              <if test="adminSwitch != null">
				#{adminSwitch,jdbcType=INTEGER},
              </if>
              <if test="creditCode != null">
				#{creditCode,jdbcType=VARCHAR},
              </if>
              <if test="businessLicenseAddress != null">
				#{businessLicenseAddress,jdbcType=VARCHAR},
              </if>
              <if test="billToPay != null">
				#{billToPay,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateById" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" >
        UPDATE admin t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.admin_id = #{adminId,jdbcType=INTEGER}
        </where>
    </update>

	<!-- 根据条件进行修改 -->
	<update id="updateByCondition" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" >
        UPDATE admin t
        <include refid="whereColumnByUpdate"></include>
        <include refid="whereColumnBySelect"></include>
    </update>


	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.admin.Admin" >
        DELETE FROM admin t
		WHERE t.admin_id = #{adminId,jdbcType=INTEGER}
    </delete>


    <!-- 根据条件查询对象 -->
    <select id="selectByAdminIds" parameterType="java.lang.Long" resultMap="adminResultMap" >
        SELECT <include refid="adminColumns" />
        FROM admin t
        where admin_id in
        <foreach collection="adminIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByAdminRealNames" resultMap="adminResultMap" >
        SELECT <include refid="adminColumns" />
        FROM admin t
        where realname in
        <foreach collection="realNames" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPage" parameterType="net.summerfarm.manage.domain.admin.param.query.AdminQueryParam"
            resultType="net.summerfarm.manage.domain.admin.entity.AdminEntity" >
        SELECT
        t.admin_id adminId,
        t.create_time createTime,
        t.login_fail_times loginFailTimes,
        t.is_disabled isDisabled,
        t.username username,
        t.password password,
        t.login_time loginTime,
        t.realname realname,
        t.gender gender,
        t.department department,
        t.phone phone,
        t.kp kp,
        t.saler_id salerId,
        t.saler_name salerName,
        t.contract contract,
        t.contract_method contractMethod,
        t.name_remakes nameRemakes,
        t.operate_id operateId,
        t.major_cycle majorCycle,
        t.close_order_type closeOrderType,
        t.cooperation_stage cooperationStage,
        t.update_time updateTime,
        t.close_order_time closeOrderTime,
        t.update_close_order_time updateCloseOrderTime,
        t.low_price_remainder lowPriceRemainder,
        t.not_included_area notIncludedArea,
        t.sku_sorting skuSorting,
        t.admin_type adminType,
        t.admin_chain adminChain,
        t.admin_grade adminGrade,
        t.admin_switch adminSwitch,
        t.credit_code creditCode,
        t.business_license_address businessLicenseAddress,
        t.bill_to_pay billToPay,
        t.base_user_id baseUserId
        FROM admin t
        <include refid="whereColumnBySelect" />
        ORDER BY t.admin_id DESC
    </select>

    <select id="selectMajorCustomerCooperationLargeAreaNo" parameterType="java.lang.Long"
            resultType="net.summerfarm.manage.domain.area.valueobject.LargeAreaValueObject">
        select lar.`large_area_no` largeAreaNo ,lar.`large_area_name` largeAreaName
        from admin a
                     inner join merchant m on a.`admin_id` = m.`admin_id`
                     inner join area ar on ar.`area_no` = m.`area_no`
                     inner join `large_area` lar on lar.`large_area_no` = ar.`large_area_no`
        where a.admin_id = #{adminId}
        group by lar.`large_area_no`,lar.`large_area_name`
    </select>

</mapper>