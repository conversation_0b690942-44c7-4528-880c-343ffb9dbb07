<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.searchSynonym.ProductSearchSynonymDictionaryMapper">
    <!-- 结果集映射 -->
    <resultMap id="productSearchSynonymDictionaryResultMap" type="net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="synonym_terms" property="synonymTerms" jdbcType="VARCHAR"/>
		<result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
		<result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
		<result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
		<result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productSearchSynonymDictionaryColumns">
          t.id,
          t.synonym_terms,
          t.created_by,
          t.updated_by,
          t.created_at,
          t.updated_at
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="synonymTerms != null and synonymTerms !=''">
                AND t.synonym_terms like CONCAT('%',#{synonymTerms},'%')
            </if>
			<if test="createdBy != null and createdBy !=''">
                AND t.created_by = #{createdBy}
            </if>
			<if test="updatedBy != null and updatedBy !=''">
                AND t.updated_by = #{updatedBy}
            </if>
			<if test="createdAt != null">
                AND t.created_at = #{createdAt}
            </if>
			<if test="updatedAt != null">
                AND t.updated_at = #{updatedAt}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="synonymTerms != null">
                    t.synonym_terms = #{synonymTerms},
                </if>
                <if test="createdBy != null">
                    t.created_by = #{createdBy},
                </if>
                <if test="updatedBy != null">
                    t.updated_by = #{updatedBy},
                </if>
                <if test="createdAt != null">
                    t.created_at = #{createdAt},
                </if>
                <if test="updatedAt != null">
                    t.updated_at = #{updatedAt},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productSearchSynonymDictionaryResultMap" >
        SELECT <include refid="productSearchSynonymDictionaryColumns" />
        FROM product_search_synonym_dictionary t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam"  resultType="net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity" >
        SELECT
            t.id id,
            t.synonym_terms synonymTerms,
            t.created_by createdBy,
            t.updated_by updatedBy,
            t.created_at createdAt,
            t.updated_at updatedAt
        FROM product_search_synonym_dictionary t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam" resultMap="productSearchSynonymDictionaryResultMap" >
        SELECT <include refid="productSearchSynonymDictionaryColumns" />
        FROM product_search_synonym_dictionary t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO product_search_synonym_dictionary
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="synonymTerms != null">
				  synonym_terms,
              </if>
              <if test="createdBy != null">
				  created_by,
              </if>
              <if test="updatedBy != null">
				  updated_by,
              </if>
              <if test="createdAt != null">
				  created_at,
              </if>
              <if test="updatedAt != null">
				  updated_at,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="synonymTerms != null">
				#{synonymTerms,jdbcType=VARCHAR},
              </if>
              <if test="createdBy != null">
				#{createdBy,jdbcType=VARCHAR},
              </if>
              <if test="updatedBy != null">
				#{updatedBy,jdbcType=VARCHAR},
              </if>
              <if test="createdAt != null">
				#{createdAt,jdbcType=TIMESTAMP},
              </if>
              <if test="updatedAt != null">
				#{updatedAt,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary" >
        UPDATE product_search_synonym_dictionary t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary" >
        DELETE FROM product_search_synonym_dictionary
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>