<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantSubAccountMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount">
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="m_id" property="mId" jdbcType="BIGINT"/>
    <result column="contact" property="contact" jdbcType="VARCHAR"/>
    <result column="phone" property="phone" jdbcType="VARCHAR"/>
    <result column="unionid" property="unionid" jdbcType="VARCHAR"/>
    <result column="openid" property="openid" jdbcType="VARCHAR"/>
    <result column="mp_openid" property="mpOpenid" jdbcType="VARCHAR"/>
    <result column="pop_view" property="popView" jdbcType="INTEGER"/>
    <result column="first_pop_view" property="firstPopView" jdbcType="INTEGER"/>
    <result column="cash_amount" property="cashAmount" jdbcType="DECIMAL"/>
    <result column="cash_update_time" property="cashUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
    <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
    <result column="m_info" property="mInfo" jdbcType="VARCHAR"/>
    <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
    <result column="audit_user" property="auditUser" jdbcType="INTEGER"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List">
    account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`
  </sql>
  <insert id="insert" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount">
    insert into merchant_sub_account (account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`)
    values (#{accountId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{contact,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR},
      #{mpOpenid,jdbcType=VARCHAR}, #{popView,jdbcType=INTEGER}, #{firstPopView,jdbcType=INTEGER},
      #{cashAmount,jdbcType=DECIMAL}, #{cashUpdateTime,jdbcType=TIMESTAMP}, #{loginTime,jdbcType=TIMESTAMP},
      #{lastOrderTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{deleteFlag,jdbcType=INTEGER},
      #{mInfo,jdbcType=VARCHAR}, #{registerTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP},
      #{auditUser,jdbcType=INTEGER},#{type,jdbcType=INTEGER})
  </insert>



  <update id="updateSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount">
    update merchant_sub_account
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null">
        mp_openid = #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="popView != null">
        pop_view = #{popView,jdbcType=INTEGER},
      </if>
      <if test="firstPopView != null">
        first_pop_view = #{firstPopView,jdbcType=INTEGER},
      </if>
      <if test="cashAmount != null">
        cash_amount = #{cashAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashUpdateTime != null">
        cash_update_time = #{cashUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderTime != null">
        last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="mInfo != null">
        m_info = #{mInfo,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null">
        audit_user = #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER}
      </if>
    </set>
    where account_id = #{accountId}
  </update>

  <select id="selectListByPhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    <where>
      delete_flag = 1 and phone = #{phone}
    </where>
  </select>


  <update id="updateMain2Base" >
    update merchant_sub_account set m_id =#{updateMid},type=1  where m_id =#{mId} and delete_flag = 1 and status  =1 and type = 0
  </update>
</mapper>