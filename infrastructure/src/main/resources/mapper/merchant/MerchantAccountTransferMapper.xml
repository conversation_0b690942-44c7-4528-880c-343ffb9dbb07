<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantAccountTransferMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantAccountTransferResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantAccountTransfer">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="mname" property="mname" jdbcType="VARCHAR"/>
		<result column="transfer_m_id" property="transferMId" jdbcType="NUMERIC"/>
		<result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="NUMERIC"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="addr" property="addr" jdbcType="VARCHAR"/>
		<result column="bd_name" property="bdName" jdbcType="VARCHAR"/>
		<result column="transfer_mname" property="transferMname" jdbcType="VARCHAR"/>
		<result column="transfer_bd_name" property="transferBdName" jdbcType="VARCHAR"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="transfer_phone" property="transferPhone" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, create_time, update_time, m_id, mname, transfer_m_id, operator_name, area_no,
    area_name, remark, addr, bd_name, transfer_mname, transfer_bd_name,transfer_phone,phone
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="merchantAccountTransferResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_account_transfer
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getPage" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam" resultType="net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity">
        select
        t.id id,
        t.create_time createTime,
        t.update_time updateTime,
        t.m_id mId,
        t.mname mname,
        t.transfer_m_id transferMId,
        t.operator_name operatorName,
        t.area_no areaNo,
        t.area_name areaName,
        t.remark remark,
        t.addr addr,
        t.bd_name bdName,
        t.transfer_mname transferMname,
        t.transfer_bd_name transferBdName,
        t.phone phone,
        t.transfer_phone transferPhone
        from merchant_account_transfer t
        <where>
            <if test="mName != null">
                and t.mname like CONCAT (#{mName} ,'%')
            </if>
            <if test="mId != null">
                and t.m_id = #{mId}
            </if>
            <if test="id != null">
                and t.id = #{id}
            </if>
            <if test="transferMids != null and transferMids.size > 0">
                and  t.transfer_m_id in
                <foreach collection="transferMids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t.create_time desc
    </select>


    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantAccountTransfer" useGeneratedKeys="true">
        insert into merchant_account_transfer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="mname != null">
                mname,
            </if>
            <if test="transferMId != null">
                transfer_m_id,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="areaName != null">
                area_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="addr != null">
                addr,
            </if>
            <if test="bdName != null">
                bd_name,
            </if>
            <if test="transferMname != null">
                transfer_mname,
            </if>
            <if test="transferBdName != null">
                transfer_bd_name,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="transferPhone != null">
                transfer_phone,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="mname != null">
                #{mname,jdbcType=VARCHAR},
            </if>
            <if test="transferMId != null">
                #{transferMId,jdbcType=BIGINT},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                #{areaNo,jdbcType=BIGINT},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="addr != null">
                #{addr,jdbcType=VARCHAR},
            </if>
            <if test="bdName != null">
                #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="transferMname != null">
                #{transferMname,jdbcType=VARCHAR},
            </if>
            <if test="transferBdName != null">
                #{transferBdName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="transferPhone != null">
                #{transferPhone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


</mapper>