<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ProductLabelValueMapper">
    <!-- 结果集映射 -->
    <resultMap id="productLabelValueResultMap" type="net.summerfarm.manage.infrastructure.model.product.ProductLabelValue">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="label_id" property="labelId" jdbcType="VARCHAR"/>
		<result column="label_value" property="labelValue" jdbcType="TINYINT"/>
		<result column="creat_time" property="creatTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productLabelValueColumns">
          t.id,
          t.sku,
          t.label_id,
          t.label_value,
          t.creat_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="labelId != null and labelId !=''">
                AND t.label_id = #{labelId}
            </if>
			<if test="labelValue != null">
                AND t.label_value = #{labelValue}
            </if>
			<if test="creatTime != null">
                AND t.creat_time = #{creatTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="labelId != null">
                    t.label_id = #{labelId},
                </if>
                <if test="labelValue != null">
                    t.label_value = #{labelValue},
                </if>
                <if test="creatTime != null">
                    t.creat_time = #{creatTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productLabelValueResultMap" >
        SELECT <include refid="productLabelValueColumns" />
        FROM product_label_value t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.ProductLabelValueQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity" >
        SELECT
            t.id id,
            t.sku sku,
            t.label_id labelId,
            t.label_value labelValue,
            t.creat_time creatTime,
            t.update_time updateTime
        FROM product_label_value t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.ProductLabelValueQueryParam" resultMap="productLabelValueResultMap" >
        SELECT <include refid="productLabelValueColumns" />
        FROM product_label_value t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectBySku" resultType="net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity">
        SELECT plv.label_id labelId,
               plv.label_value labelValue,
               pl.label_field labelField,
               pl.label_name labelName,
               plv.id
        from product_label_value plv
        LEFT JOIN product_label pl on plv.label_id = pl.id
        where
        plv.sku = #{sku}
        order by plv.id desc
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductLabelValue" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO product_label_value
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="labelId != null">
				  label_id,
              </if>
              <if test="labelValue != null">
				  label_value,
              </if>
              <if test="creatTime != null">
				  creat_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="labelId != null">
				#{labelId,jdbcType=VARCHAR},
              </if>
              <if test="labelValue != null">
				#{labelValue,jdbcType=TINYINT},
              </if>
              <if test="creatTime != null">
				#{creatTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductLabelValue" >
        UPDATE product_label_value t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductLabelValue" >
        DELETE FROM product_label_value t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>