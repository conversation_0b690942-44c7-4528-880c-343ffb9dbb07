<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.MajorPriceMapper">
  <!-- 结果集映射 -->
  <resultMap id="majorPriceResultMap" type="net.summerfarm.manage.infrastructure.model.product.MajorPrice">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
    <result column="weight" property="weight" jdbcType="VARCHAR"/>
    <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <result column="price" property="price" jdbcType="DOUBLE"/>
    <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
    <result column="direct" property="direct" jdbcType="INTEGER"/>
    <result column="pay_method" property="payMethod" jdbcType="TINYINT"/>
    <result column="valid_time" property="validTime" jdbcType="TIMESTAMP"/>
    <result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP"/>
    <result column="mall_show" property="mallShow" jdbcType="INTEGER"/>
    <result column="price_type" property="priceType" jdbcType="TINYINT"/>
    <result column="cost" property="cost" jdbcType="DOUBLE"/>
    <result column="interest_rate" property="interestRate" jdbcType="DOUBLE"/>
    <result column="fixed_price" property="fixedPrice" jdbcType="DOUBLE"/>
    <result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="low_price_update_time" property="lowPriceUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="price_adjustment_value" property="priceAdjustmentValue" jdbcType="DOUBLE"/>
    <result column="status" property="status" jdbcType="TINYINT"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="large_area_no" property="largeAreaNo" jdbcType="INTEGER"/>
  </resultMap>


  <resultMap id="majorPriceEntityResultMap" type="net.summerfarm.manage.domain.product.entity.MajorPriceEntity">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
    <result column="weight" property="weight" jdbcType="VARCHAR"/>
    <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <result column="price" property="price" jdbcType="DOUBLE"/>
    <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
    <result column="direct" property="direct" jdbcType="INTEGER"/>
    <result column="pay_method" property="payMethod" jdbcType="TINYINT"/>
    <result column="valid_time" property="validTime" jdbcType="TIMESTAMP"/>
    <result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP"/>
    <result column="mall_show" property="mallShow" jdbcType="INTEGER"/>
    <result column="price_type" property="priceType" jdbcType="TINYINT"/>
    <result column="cost" property="cost" jdbcType="DOUBLE"/>
    <result column="interest_rate" property="interestRate" jdbcType="DOUBLE"/>
    <result column="fixed_price" property="fixedPrice" jdbcType="DOUBLE"/>
    <result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="low_price_update_time" property="lowPriceUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="price_adjustment_value" property="priceAdjustmentValue" jdbcType="DOUBLE"/>
    <result column="status" property="status" jdbcType="TINYINT"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="large_area_no" property="largeAreaNo" jdbcType="INTEGER"/>
  </resultMap>

  <!-- 列定义 -->
  <sql id="majorPriceColumns">
    t.id,
          t.sku,
          t.pd_name,
          t.weight,
          t.area_no,
          t.admin_id,
          t.price,
          t.area_name,
          t.direct,
          t.pay_method,
          t.valid_time,
          t.invalid_time,
          t.mall_show,
          t.price_type,
          t.cost,
          t.interest_rate,
          t.fixed_price,
          t.original_price,
          t.update_time,
          t.low_price_update_time,
          t.large_area_no,
          t.price_adjustment_value,
          t.remark,
          t.status

  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="sku != null and sku !=''">
        AND t.sku = #{sku}
      </if>
      <if test="pdName != null and pdName !=''">
        AND t.pd_name = #{pdName}
      </if>
      <if test="weight != null and weight !=''">
        AND t.weight = #{weight}
      </if>
      <if test="areaNo != null">
        AND t.area_no = #{areaNo}
      </if>
      <if test="adminId != null">
        AND t.admin_id = #{adminId}
      </if>
      <if test="price != null">
        AND t.price = #{price}
      </if>
      <if test="areaName != null and areaName !=''">
        AND t.area_name = #{areaName}
      </if>
      <if test="direct != null">
        AND t.direct = #{direct}
      </if>
      <if test="payMethod != null">
        AND t.pay_method = #{payMethod}
      </if>
      <if test="validTime != null">
        AND t.valid_time <![CDATA[<=]]> #{validTime}
      </if>
      <if test="invalidTime != null">
        AND t.invalid_time <![CDATA[>]]> #{invalidTime}
      </if>
      <if test="mallShow != null">
        AND t.mall_show = #{mallShow}
      </if>
      <if test="priceType != null">
        AND t.price_type = #{priceType}
      </if>
      <if test="cost != null">
        AND t.cost = #{cost}
      </if>
      <if test="interestRate != null">
        AND t.interest_rate = #{interestRate}
      </if>
      <if test="fixedPrice != null">
        AND t.fixed_price = #{fixedPrice}
      </if>
      <if test="originalPrice != null">
        AND t.original_price = #{originalPrice}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
      <if test="lowPriceUpdateTime != null">
        AND t.low_price_update_time = #{lowPriceUpdateTime}
      </if>
    </trim>
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="sku != null">
        t.sku = #{sku},
      </if>
      <if test="pdName != null">
        t.pd_name = #{pdName},
      </if>
      <if test="weight != null">
        t.weight = #{weight},
      </if>
      <if test="areaNo != null">
        t.area_no = #{areaNo},
      </if>
      <if test="adminId != null">
        t.admin_id = #{adminId},
      </if>
      <if test="price != null">
        t.price = #{price},
      </if>
      <if test="areaName != null">
        t.area_name = #{areaName},
      </if>
      <if test="direct != null">
        t.direct = #{direct},
      </if>
      <if test="payMethod != null">
        t.pay_method = #{payMethod},
      </if>
      <if test="validTime != null">
        t.valid_time = #{validTime},
      </if>
      <if test="invalidTime != null">
        t.invalid_time = #{invalidTime},
      </if>
      <if test="mallShow != null">
        t.mall_show = #{mallShow},
      </if>
      <if test="priceType != null">
        t.price_type = #{priceType},
      </if>
      <if test="cost != null">
        t.cost = #{cost},
      </if>
      <if test="interestRate != null">
        t.interest_rate = #{interestRate},
      </if>
      <if test="fixedPrice != null">
        t.fixed_price = #{fixedPrice},
      </if>
      <if test="originalPrice != null">
        t.original_price = #{originalPrice},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
      <if test="lowPriceUpdateTime != null">
        t.low_price_update_time = #{lowPriceUpdateTime},
      </if>
    </trim>
  </sql>

  <!-- 根据主键ID获取数据 -->
  <select id="selectById" parameterType="java.lang.Long" resultMap="majorPriceResultMap" >
    SELECT <include refid="majorPriceColumns" />
    FROM major_price t
    WHERE t.id = #{id}
  </select>

  <!-- 查询列表可以根据分页进行查询 -->
  <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.MajorPriceQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.MajorPriceEntity" >
    SELECT
    t.id id,
    t.sku sku,
    t.pd_name pdName,
    t.weight weight,
    t.area_no areaNo,
    t.admin_id adminId,
    t.price price,
    t.area_name areaName,
    t.direct direct,
    t.pay_method payMethod,
    t.valid_time validTime,
    t.invalid_time invalidTime,
    t.mall_show mallShow,
    t.price_type priceType,
    t.cost cost,
    t.interest_rate interestRate,
    t.fixed_price fixedPrice,
    t.original_price originalPrice,
    t.update_time updateTime,
    t.low_price_update_time lowPriceUpdateTime
    FROM major_price t
    <include refid="whereColumnBySelect" />
    ORDER BY t.id DESC
  </select>


  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.MajorPriceQueryParam" resultMap="majorPriceResultMap" >
    SELECT <include refid="majorPriceColumns" />
    FROM major_price t
    <include refid="whereColumnBySelect"></include>
  </select>



  <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
  <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.MajorPrice" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO major_price
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="direct != null">
        direct,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>
      <if test="validTime != null">
        valid_time,
      </if>
      <if test="invalidTime != null">
        invalid_time,
      </if>
      <if test="mallShow != null">
        mall_show,
      </if>
      <if test="priceType != null">
        price_type,
      </if>
      <if test="cost != null">
        cost,
      </if>
      <if test="interestRate != null">
        interest_rate,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lowPriceUpdateTime != null">
        low_price_update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="direct != null">
        #{direct,jdbcType=INTEGER},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=TINYINT},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidTime != null">
        #{invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mallShow != null">
        #{mallShow,jdbcType=INTEGER},
      </if>
      <if test="priceType != null">
        #{priceType,jdbcType=TINYINT},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DOUBLE},
      </if>
      <if test="interestRate != null">
        #{interestRate,jdbcType=DOUBLE},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DOUBLE},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DOUBLE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lowPriceUpdateTime != null">
        #{lowPriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <!-- 根据主键ID进行修改 -->
  <update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.MajorPrice" >
    UPDATE major_price t
    <include refid="whereColumnByUpdate"></include>
    <where>
      t.id = #{id,jdbcType=INTEGER}
    </where>
  </update>
  <update id="updateMallShowByIds">
    UPDATE major_price set mall_show = #{mallShow}
    where id in
    <foreach collection="ids" open="(" close=") " separator="," item="id">
      #{id}
    </foreach>
  </update>
  <update id="updateBatch">
    <foreach collection="list" item="item" separator=";">
      UPDATE major_price
      <set>
        <if test="item.sku != null">sku = #{item.sku},</if>
        <if test="item.pdName != null">pd_name = #{item.pdName},</if>
        <if test="item.weight != null">weight = #{item.weight},</if>
        <if test="item.areaNo != null">area_no = #{item.areaNo},</if>
        <if test="item.adminId != null">admin_id = #{item.adminId},</if>
        price = #{item.price},
        <if test="item.areaName != null">area_name = #{item.areaName},</if>
        <if test="item.direct != null">direct = #{item.direct},</if>
        <if test="item.payMethod != null">pay_method = #{item.payMethod},</if>
        <if test="item.validTime != null">valid_time = #{item.validTime},</if>
        <if test="item.invalidTime != null">invalid_time = #{item.invalidTime},</if>
        <if test="item.mallShow != null">mall_show = #{item.mallShow},</if>
        <if test="item.priceType != null">price_type = #{item.priceType},</if>
        cost = #{item.cost},
        interest_rate = #{item.interestRate},
        fixed_price = #{item.fixedPrice},
        original_price = #{item.originalPrice},
        <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
        <if test="item.lowPriceUpdateTime != null">low_price_update_time = #{item.lowPriceUpdateTime},</if>
        <if test="item.largeAreaNo != null">large_area_no = #{item.largeAreaNo},</if>
        price_adjustment_value = #{item.priceAdjustmentValue},
        <if test="item.remark != null">remark = #{item.remark},</if>
        <if test="item.status != null">status = #{item.status},</if>
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>
  <update id="removeByIds">
    DELETE FROM major_price
    WHERE id IN
    <foreach collection="ids"  open="(" close=") " separator="," item="id">
      #{id}
    </foreach>
  </update>
  <update id="commitBatch">
    UPDATE major_price set status = 1
    where id in
    <foreach collection="ids" open="(" close=") " separator="," item="id">
      #{id}
    </foreach>
  </update>


  <!-- 根据主键ID进行物理删除 -->
  <delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.MajorPrice" >
    DELETE FROM major_price
    WHERE id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteBySku">
      DELETE FROM major_price
    WHERE sku = #{sku}
      and admin_id = #{adminId}
      and direct = #{direct}
    </delete>


    <select id="queryListMajorPrice" resultType="net.summerfarm.manage.domain.product.entity.MajorPriceEntity">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, min(m.price) minPrice, max(m.price)maxPrice,
    m.price,m.price_type priceType,m.large_area_no largeAreaNo,m.price_adjustment_value priceAdjustmentValue, m.remark,m.status
    from major_price m
    left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
    and m.admin_id = #{adminId}
    and ak.on_sale = 1
    and m.valid_time <![CDATA[<=]]> now()
    and m.invalid_time <![CDATA[>]]> now()
    and m.status = 1
    and m.area_no in
    <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    group by m.area_no
  </select>



  <select id="selectLowPriceRemainderSku" resultType="net.summerfarm.manage.domain.product.entity.MajorPriceLowRemainder">
    select mp.id,
    mp.sku,
    mp.pd_name        pdName,
    mp.weight,
    mp.area_no        areaNo,
    mp.admin_id       adminId,
    mp.price,
    mp.area_name      areaName,
    mp.valid_time     validTime,
    mp.invalid_time   invalidTime,
    mp.mall_show      mallShow,
    mp.price_type     priceType,
    mp.interest_rate     interestRate,
    mp.fixed_price     fixedPrice,
    ak.original_price originalPrice,
    ak.price          mallPrice
    from major_price mp
    left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
    <where>
      ak.on_sale = 1
      and mp.invalid_time > now()
      and ak.m_type = 0
      and mp.admin_id = #{adminId}
      and mp.area_no = #{areaNo}
      <if test="sku != null">
        and mp.sku = #{sku}
      </if>
    </where>
  </select>
  <select id="selectMajorPrice" resultType="net.summerfarm.manage.domain.product.entity.MajorPriceEntity">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price, m.price_type priceType, m.pay_method payMethod, m.valid_time validTime, m.invalid_time invalidTime, m.mall_show mallShow
               ,m.large_area_no largeAreaNo,m.price_adjustment_value priceAdjustmentValue, m.remark,m.status
    from major_price m
           left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
      and m.area_no = #{areaNo}
      and m.admin_id = #{adminId}
      and m.direct = #{direct}
      and ak.on_sale = 1
      and m.status = 1
      and m.valid_time <![CDATA[<=]]> now()
      and m.invalid_time <![CDATA[>]]> now()
  </select>
  <select id="queryListMajorPriceWithoutTime" resultType="net.summerfarm.manage.domain.product.entity.MajorPriceEntity">
    select
    id AS id,
    sku AS sku,
    pd_name AS pdName,
    weight AS weight,
    area_no AS areaNo,
    admin_id AS adminId,
    price AS price,
    area_name AS areaName,
    direct AS direct,
    pay_method AS payMethod,
    valid_time AS validTime,
    invalid_time AS invalidTime,
    mall_show AS mallShow,
    price_type AS priceType,
    cost AS cost,
    interest_rate AS interestRate,
    fixed_price AS fixedPrice,
    original_price AS originalPrice,
    update_time AS updateTime,
    low_price_update_time AS lowPriceUpdateTime,
    large_area_no AS largeAreaNo,
    price_adjustment_value AS priceAdjustmentValue,
    remark AS remark,
    status AS status from major_price m
    where m.direct = #{direct} and m.invalid_time <![CDATA[>=]]> now()  and m.sku in
    <foreach collection="skus" item="sku" index="index" open="(" separator="," close=")">
      #{sku}
    </foreach>
    and m.admin_id = #{adminId}
    and m.area_no in
    <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="queryListMajorPriceByIds" resultType="net.summerfarm.manage.domain.product.entity.MajorPriceEntity">
    select
    id AS id,
    sku AS sku,
    pd_name AS pdName,
    weight AS weight,
    area_no AS areaNo,
    admin_id AS adminId,
    price AS price,
    area_name AS areaName,
    direct AS direct,
    pay_method AS payMethod,
    valid_time AS validTime,
    invalid_time AS invalidTime,
    mall_show AS mallShow,
    price_type AS priceType,
    cost AS cost,
    interest_rate AS interestRate,
    fixed_price AS fixedPrice,
    original_price AS originalPrice,
    update_time AS updateTime,
    low_price_update_time AS lowPriceUpdateTime,
    large_area_no AS largeAreaNo,
    price_adjustment_value AS priceAdjustmentValue,
    remark AS remark,
    status AS status  from major_price
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <insert id="insertBatch" parameterType="net.summerfarm.manage.infrastructure.model.product.MajorPrice" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO major_price (sku, pd_name, weight, price, direct, area_no, area_name, admin_id, pay_method,
    valid_time, invalid_time, mall_show, price_type, interest_rate, fixed_price,
    cost, original_price,low_price_update_time, large_area_no, price_adjustment_value, remark, status)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.sku}, #{item.pdName}, #{item.weight}, #{item.price}, #{item.direct},
      #{item.areaNo}, #{item.areaName}, #{item.adminId}, #{item.payMethod},
      #{item.validTime}, #{item.invalidTime}, #{item.mallShow}, #{item.priceType},
      #{item.interestRate}, #{item.fixedPrice}, #{item.cost},
      #{item.originalPrice},#{item.lowPriceUpdateTime}, #{item.largeAreaNo}, #{item.priceAdjustmentValue},
      #{item.remark}, #{item.status})
    </foreach>
  </insert>


  <select id="selectLastCommitMajorPrice" resultMap="majorPriceEntityResultMap">
    select <include refid="majorPriceColumns" />
    from major_price t
    where t.admin_id = #{adminId} and t.status = 1 order by t.id desc limit 1
  </select>


    <select id="selectMajorPriceList"
            parameterType="net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam"
            resultType="net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject">
        SELECT m.large_area_no largeAreaNo, la.large_area_name largeAreaName, m.sku, p.pd_name pdName, i.weight specification, i.unit specificationUnit
        FROM major_price m
                     LEFT JOIN large_area la on la.large_area_no = m.large_area_no
                     LEFT JOIN inventory i ON m.sku = i.sku
                     LEFT JOIN products p on i.pd_id = p.pd_id
                     LEFT JOIN area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
                     LEFT JOIN (
                select area_no, store_no
                from fence
                where status = 0
                ) f on f.area_no = ak.area_no
                     inner join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <include refid="cityPageWhere"/>
        GROUP BY m.large_area_no, m.sku
        ORDER BY m.large_area_no, m.sku
    </select>

    <select id="selectMajorPriceCityList"
            parameterType="net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam"
            resultType="net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject">
        SELECT m.id,
               m.sku,
               p.pd_name        pdName,
               p.category_id categoryId,
               m.area_name      areaName,
               la.large_area_name largeAreaName,
               m.price          price,
               m.direct,
               mpl.price        lastCyclePrice,
               mpl.interest_rate        lastCycleInterestRate,
               mpl.price_type        lastCyclePriceType,
               i.weight specification,
               i.unit specificationUnit,
               m.admin_id       adminId,
               m.area_no        areaNo,
               m.valid_time     validTime,
               m.invalid_time   invalidTime,
               m.price_type     priceType,
               m.price_adjustment_value priceAdjustmentValue,
               m.interest_rate  interestRate,
               m.fixed_price    fixedPrice,
               (case
                       when m.valid_time <![CDATA[>]]> now() and m.status = 1 then 0
                       when (m.valid_time <![CDATA[<=]]> now() and m.invalid_time <![CDATA[>]]> now()) and m.status = 1 then 1
                       when m.invalid_time <![CDATA[<=]]> now() then 2
                       when m.invalid_time <![CDATA[>]]> now() and m.status = 0 then 3
                       end)     validStatus,
               ak.price         salePrice,
               ak.on_sale       onSale,
               ak.show,
               m.mall_show      mallShow,
               i.sub_type       subType,
               i.origin,
               i.pd_id pdId,
               wim.warehouse_no warehouseNo,
               m.large_area_no largeAreaNo
        FROM major_price m
                     LEFT JOIN large_area la on la.large_area_no = m.large_area_no
                     LEFT JOIN inventory i ON m.sku = i.sku
                     LEFT JOIN products p on i.pd_id = p.pd_id
                     LEFT JOIN area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
                     LEFT JOIN (
                SELECT sku,
                       admin_id,
                       area_no,
                       direct,
                       MAX(id) AS latest_log_id
                FROM major_price_log
                GROUP BY sku, admin_id, area_no, direct
                ) latest ON m.sku = latest.sku
                AND m.admin_id = latest.admin_id
                AND m.area_no = latest.area_no
                and m.direct = latest.direct
                     LEFT JOIN major_price_log mpl ON latest.latest_log_id = mpl.id
                     LEFT JOIN (
                select area_no, store_no
                from fence
                where status = 0
                ) f on f.area_no = ak.area_no
                     inner join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <include refid="cityPageWhere"/>
        GROUP BY m.id
        order by id desc
    </select>

    <sql id="cityPageWhere">
        WHERE m.admin_id=#{adminId}
        <if test="direct != null">
            AND m.direct=#{direct}
        </if>
        <if test="sku != null">
            AND m.sku LIKE concat(#{sku},'%')
        </if>
        <if test="pdName != null">
            AND p.pd_name LIKE concat('%',#{pdName},'%')
        </if>

        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="largeAreaNo != null">
            AND m.large_area_no = #{largeAreaNo}
        </if>
        <if test="priceType != null">
            AND m.price_type =#{priceType}
        </if>
        <if test="validStatus != null">
            <if test="validStatus == 0">
                AND m.valid_time <![CDATA[>]]> now() and m.status = 1
            </if>
            <if test="validStatus == 1">
                AND m.valid_time <![CDATA[<=]]> now()
                AND m.invalid_time <![CDATA[>]]> now()
                and m.status = 1
            </if>
            <if test="validStatus == 2">
                AND m.invalid_time <![CDATA[<=]]> now()
            </if>
            <if test="validStatus == 3">
                AND m.invalid_time <![CDATA[>]]> now() and m.status = 0
            </if>
        </if>
        <if test="subType != null">
            AND i.sub_type=#{subType}
        </if>
        <if test="warehouseNo != null">
            and wim.warehouse_no = #{warehouseNo}
        </if>
        <if test="largeNoAndSkuList != null and !largeNoAndSkuList.isEmpty">
            AND (
            <foreach collection="largeNoAndSkuList" item="item" separator=" OR ">
                (m.large_area_no = #{item.largeAreaNo} AND m.sku = #{item.sku})
            </foreach>
            )
        </if>
    </sql>


        <select id="selectMajorPriceDownloadList"
            parameterType="net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam"
            resultType="net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject">
        SELECT m.id,
               m.sku,
               p.pd_name        pdName,
               m.area_name      areaName,
               m.price          price,
               m.price_type     priceType,
               m.price_adjustment_value priceAdjustmentValue,
               m.direct,
               i.weight specification,
               i.unit specificationUnit,
               m.admin_id       adminId,
               m.area_no        areaNo,
               m.valid_time     validTime,
               m.invalid_time   invalidTime,
               ak.price         salePrice,
               m.large_area_no largeAreaNo,
               la.large_area_name largeAreaName,
               i.sub_type       subType,
               i.origin
        FROM major_price m
                     LEFT JOIN large_area la on la.large_area_no = m.large_area_no
                     LEFT JOIN inventory i ON m.sku = i.sku
                     LEFT JOIN products p on i.pd_id = p.pd_id
                     LEFT JOIN area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
         WHERE m.admin_id=#{adminId}
        <if test="direct != null">
            AND m.direct=#{direct}
        </if>
        <if test="sku != null">
            AND m.sku =#{sku}
        </if>
        <if test="validStatus != null">
            <if test="validStatus == 0">
                AND m.valid_time <![CDATA[>]]> now() and m.status = 1
            </if>
            <if test="validStatus == 1">
                AND m.valid_time <![CDATA[<=]]> now()
                AND m.invalid_time <![CDATA[>]]> now()
                and m.status = 1
            </if>
            <if test="validStatus == 2">
                AND m.invalid_time <![CDATA[<=]]> now()
            </if>
            <if test="validStatus == 3">
                AND m.invalid_time <![CDATA[>]]> now() and m.status = 0
            </if>
        </if>
        <if test="largeNoAndSkuList != null and !largeNoAndSkuList.isEmpty">
            AND (
            <foreach collection="largeNoAndSkuList" item="item" separator=" OR ">
                (m.large_area_no = #{item.largeAreaNo} AND m.sku = #{item.sku})
            </foreach>
            )
        </if>
        order by m.id desc
    </select>
</mapper>