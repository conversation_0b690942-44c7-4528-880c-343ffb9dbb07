<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ExternalProductMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="externalProductMappingResultMap" type="net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="internal_value" property="internalValue" jdbcType="VARCHAR"/>
		<result column="external_value" property="externalValue" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="externalProductMappingColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.type,
          t.internal_value,
          t.external_value
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="internalValue != null and internalValue !=''">
                AND t.internal_value = #{internalValue}
            </if>
			<if test="externalValue != null and externalValue !=''">
                AND t.external_value = #{externalValue}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="internalValue != null">
                    t.internal_value = #{internalValue},
                </if>
                <if test="externalValue != null">
                    t.external_value = #{externalValue},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="externalProductMappingResultMap" >
        SELECT <include refid="externalProductMappingColumns" />
        FROM external_product_mapping t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.type type,
            t.internal_value internalValue,
            t.external_value externalValue
        FROM external_product_mapping t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam" resultMap="externalProductMappingResultMap" >
        SELECT <include refid="externalProductMappingColumns" />
        FROM external_product_mapping t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO external_product_mapping
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
                create_time,update_time,
              <if test="type != null">
				  type,
              </if>
              <if test="internalValue != null">
				  internal_value,
              </if>
              <if test="externalValue != null">
				  external_value,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              now(),now(),
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="internalValue != null">
				#{internalValue,jdbcType=VARCHAR},
              </if>
              <if test="externalValue != null">
				#{externalValue,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping" >
        UPDATE external_product_mapping t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping" >
        DELETE FROM external_product_mapping
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <select id="getPageUnmapped" resultType="net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity">
        select
            i.sku as xmSkuCode,
            p.pd_name as title,
            i.weight as specification,
            i.weight_num as weightNum,
            i.net_weight_num as netWeightNum,
            p.picture_path as mainPicture,
            p.category_id as categoryId,
            c.category as categoryName
        FROM
            `inventory` i
                INNER JOIN `products` p on i.pd_id = p.pd_id
                INNER JOIN area_sku a on i.sku = a.sku
                INNER JOIN category c on p.category_id = c.id
        where
            <!-- 特殊场景数据，固定参数 -->
            i.`sub_type` = 5 -- pop
            and i.`outdated` = 0 -- 生效中
            and a.on_sale = 1 -- 上架
            <if test="buyerId != null">
                AND i.buyer_id = #{buyerId}
            </if>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="title != null and title !=''">
                AND p.pd_name like  CONCAT('%',#{title},'%')
            </if>
            <if test="ignoreMapping == null or ignoreMapping == false">
                AND not EXISTS (
                    select
                        internal_value
                    FROM
                        `external_product_mapping`
                    where
                        type = 1 -- sku
                        and i.sku = internal_value
                )
            </if>

        GROUP BY
            i.sku
    </select>


    <select id="getListUnmappedCategory" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
        select
            id,
            category,
            type,
            outdated,
            parent_id parentId
        FROM
        category
        WHERE id in (
            select
            DISTINCT(p.`category_id`)
            FROM
            `inventory` i
            INNER JOIN `products` p on i.pd_id = p.pd_id
            INNER JOIN area_sku a on i.sku = a.sku
            where
            <!-- 特殊场景数据，固定参数 -->
            i.`sub_type` = 5 -- pop
            and i.`outdated` = 0 -- 生效中
            and a.on_sale = 1 -- 上架
            <if test="buyerId != null">
                AND i.buyer_id = #{buyerId}
            </if>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="title != null and title !=''">
                AND p.pd_name like  CONCAT('%',#{title},'%')
            </if>
            and not EXISTS (
            select
            internal_value
            FROM
            `external_product_mapping`
            where
            type = 1 -- sku
            and i.sku = internal_value
            )
            GROUP BY
            i.sku
        )

    </select>

    <select id="getPageMapped" resultType="net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity">
        select
            epm.id as id,
            epm.internal_value as xmSkuCode,
            epm.external_value as externalSkuCode,
            p.pd_name as title,
            i.weight as specification,
            i.weight_num as weightNum,
            i.net_weight_num as netWeightNum,
            p.picture_path as mainPicture,
            p.category_id as categoryId,
            c.category as categoryName
        FROM
            `external_product_mapping` epm
                INNER JOIN `inventory` i on epm.`internal_value` = i.sku
                INNER JOIN `products` p on i.pd_id = p.pd_id
                INNER JOIN category c on p.category_id = c.id
        WHERE
            i.outdated  = 0
            <if test="buyerId != null">
                AND i.buyer_id = #{buyerId}
            </if>
            <if test="internalValue != null and internalValue != ''">
                AND epm.internal_value = #{internalValue}
            </if>
            <if test="categoryId != null ">
                AND p.category_id = #{categoryId}
            </if>
        order by epm.internal_value
    </select>

    <select id="getListMappedCategory" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
        select
            id,
            category,
            type,
            outdated,
            parent_id parentId
        FROM
            category
        WHERE
            id in (
                select DISTINCT
                    (p.category_id)
                FROM
                    `external_product_mapping` epm
                        INNER JOIN `inventory` i on epm.`internal_value` = i.sku
                        INNER JOIN `products` p on i.pd_id = p.pd_id
                WHERE
                    i.buyer_id = #{buyerId}
            )
    </select>

    <select id="getListExternalValueList" resultType="java.lang.String">
        select distinct
            (epm.external_value)
        from
            external_product_mapping epm
                inner join inventory i on epm.internal_value = i.sku
        where
            i.outdated  = 0
            <if test="type != null">
                AND epm.type = #{type}
            </if>
    </select>
</mapper>