<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.AreaSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.product.AreaSku">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
    <result column="ladder_price" jdbcType="VARCHAR" property="ladderPrice"/>
    <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    <result column="sales_mode" jdbcType="INTEGER" property="salesMode"/>
    <result column="limited_quantity" jdbcType="INTEGER" property="limitedQuantity"/>
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="on_sale" jdbcType="BIT" property="onSale"/>
    <result column="share" jdbcType="BIT" property="share"/>
    <result column="priority" jdbcType="INTEGER" property="priority"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
    <result column="level" jdbcType="INTEGER" property="level"/>
    <result column="pd_priority" jdbcType="INTEGER" property="pdPriority"/>
    <result column="info" jdbcType="VARCHAR" property="info"/>
    <result column="m_type" property="mType"/>
    <result column="corner_status" jdbcType="INTEGER" property="cornerStatus"/>
    <result column="open_sale" property="openSale"/>
    <result column="open_sale_time" property="openSaleTime"/>
    <result column="close_sale" property="closeSale"/>
    <result column="close_sale_time" property="closeSaleTime"/>
    <result column="fix_flag" property="fixFlag"/>
    <result column="fix_num" property="fixNum"/>
    <result column="show" property="show"/>
    <result column="show_advance" property="showAdvance"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    , sku, area_no, quantity,ladder_price,sales_mode, limited_quantity, original_price, price, on_sale, priority, update_time , share,
    add_time,pd_priority,info,m_type,corner_status, open_sale, open_sale_time, close_sale, close_sale_time,fix_flag,fix_num,`show`
  </sql>

  <!-- 插入区域SKU -->
  <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.AreaSku" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO area_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null and sku != ''">sku,</if>
      <if test="areaNo != null">area_no,</if>
      <if test="quantity != null">quantity,</if>
      <if test="lockQuantity != null">lock_quantity,</if>
      <if test="safeQuantity != null">safe_quantity,</if>
      <if test="originalPrice != null">original_price,</if>
      <if test="price != null">price,</if>
      <if test="ladderPrice != null">ladder_price,</if>
      <if test="share != null">share,</if>
      <if test="onSale != null">on_sale,</if>
      <if test="priority != null">priority,</if>
      <if test="pdPriority != null">pd_priority,</if>
      <if test="limitedQuantity != null">limited_quantity,</if>
      <if test="salesMode != null">sales_mode,</if>
      <if test="show != null">`show`,</if>
      <if test="info != null">info,</if>
      <if test="mType != null">m_type,</if>
      <if test="cornerStatus != null">corner_status,</if>
      <if test="openSale != null">open_sale,</if>
      <if test="closeSale != null">close_sale,</if>
      <if test="fixFlag != null">fix_flag,</if>
      <if test="fixNum != null">fix_num,</if>
      add_time, update_time
    </trim>
    <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
      <if test="sku != null and sku != ''">#{sku},</if>
      <if test="areaNo != null">#{areaNo},</if>
      <if test="quantity != null">#{quantity},</if>
      <if test="lockQuantity != null">#{lockQuantity},</if>
      <if test="safeQuantity != null">#{safeQuantity},</if>
      <if test="originalPrice != null">#{originalPrice},</if>
      <if test="price != null">#{price},</if>
      <if test="ladderPrice != null">#{ladderPrice},</if>
      <if test="share != null">#{share},</if>
      <if test="onSale != null">#{onSale},</if>
      <if test="priority != null">#{priority},</if>
      <if test="pdPriority != null">#{pdPriority},</if>
      <if test="limitedQuantity != null">#{limitedQuantity},</if>
      <if test="salesMode != null">#{salesMode},</if>
      <if test="show != null">#{show},</if>
      <if test="info != null">#{info},</if>
      <if test="mType != null">#{mType},</if>
      <if test="cornerStatus != null">#{cornerStatus},</if>
      <if test="openSale != null">#{openSale},</if>
      <if test="closeSale != null">#{closeSale},</if>
      <if test="fixFlag != null">#{fixFlag},</if>
      <if test="fixNum != null">#{fixNum},</if>
      NOW(), NOW()
    </trim>
  </insert>

  <update id="updateAreaSkuPrice">
    <foreach collection="list" item="item" separator=";">
      UPDATE area_sku
      set
      price = #{item.price},
      update_time = now()
      where sku =#{item.sku} AND area_no = #{item.areaNo}
    </foreach>
  </update>

    <update id="updateAreaSkuPopOnSale">
        UPDATE area_sku
        set
        on_sale = #{param.onSale},
        close_sale =  #{param.closeSale},
        open_sale = #{param.openSale},
        <if test="param.show != null">
            `show` = #{param.show},
        </if>
        update_time = now()
        where sku =#{param.sku}
    </update>
  <update id="offSaleByIds">
    UPDATE area_sku
    set
    on_sale = 0,
    close_sale = 0,
    open_sale = null,
    open_sale_time = null,
    update_time = now()
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>
  <update id="updateAreaSkuOnSaleBatch">
    <foreach collection="list" item="param" separator=";">
      UPDATE area_sku
      set
      on_sale = #{param.onSale},
      close_sale =  #{param.closeSale},
      open_sale = #{param.openSale},
      <if test="param.show != null">
        `show` = #{param.show},
      </if>
      update_time = now()
      where id =#{param.id}
    </foreach>
  </update>
  <update id="updateAreaSkuOnSaleBatchBySku">
    UPDATE area_sku
    set
    on_sale = #{onSale},
    update_time = now()
    where sku in
    <foreach collection="skus" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>
  <update id="offSaleBySkus">
    UPDATE area_sku
    set
    on_sale = 0,
    close_sale = 0,
    open_sale = null,
    open_sale_time = null,
    update_time = now()
    where on_sale=1 and sku in
    <foreach collection="skus" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="queryListSkuPrice" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from area_sku
    where sku in
    <foreach collection="skus" item="sku" index="index" open="(" separator="," close=")">
      #{sku}
    </foreach>
    <if test="onsale != null">
      AND on_sale = #{onsale}
    </if>
    <if test="areaNos != null and areaNos.size() > 0">
      and area_no in
      <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectValidAndOnSale" resultType="net.summerfarm.manage.domain.product.entity.AreaSkuEntity">
    select i.inv_id as skuId, id
            , ak.sku, ak.area_no, ak.quantity,ak.ladder_price,ak.sales_mode, ak.limited_quantity, original_price, price, on_sale, priority,ak.update_time , share,
           ak.add_time,pd_priority,info,m_type,corner_status, open_sale, open_sale_time, close_sale, close_sale_time,fix_flag,fix_num,`show`
    from area_sku ak left join inventory i on ak.sku = i.sku
    where area_no = #{areaNo} and i.sku = #{sku} and ak.on_sale = 1 and i.outdated  = 0
  </select>

  <select id="selectVOList" resultType="net.summerfarm.manage.domain.product.entity.AreaSkuEntity">
    SELECT
    ak.sku sku,ak.id, ak.`original_price` originalPrice, ak.`price`, ak.`ladder_price` ladderPrice,
    ak.`limited_quantity` limitedQuantity, ak.`sales_mode` salesMode,
    ak.on_sale onSale,ak.corner_status cornerStatus,
    ak.area_no areaNo,ak.show, a.area_name areaName,ak.info,ak.m_type mType, ak.area_no areaNo,
    ak.open_sale openSale, ak.open_sale_time openSaleTime, ak.close_sale closeSale, ak.close_sale_time closeSaleTime,
    wim.warehouse_no warehouseNo,a.large_area_no parentNo
    FROM area_sku ak
    RIGHT JOIN area a on a.area_no = ak.area_no and a.status = 1
    LEFT JOIN (
    select area_no ,store_no from fence
    where status = 0
    ) f on f.area_no = a.area_no
    inner join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
    <where>
      <if test="sku != null">
        AND ak.sku = #{sku}
      </if>
      <if test="areaNo != null">
        AND ak.area_no = #{areaNo}
      </if>
    </where>
    group by a.area_no
    order by a.type, a.large_area_no, ak.add_time
  </select>
    <select id="queryAreaSkuBySkuAndAreaNoList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      FROM area_sku
      <where>
        <foreach collection="list" item="item" separator=" OR ">
        (
          sku = #{item.sku}
          AND area_no IN
          <foreach collection="item.areaNos" item="areaNo" open="(" separator="," close=")">
            #{areaNo}
          </foreach>
          )
        </foreach>
      </where>
    </select>

    <!-- 分页查询指定区域的上架商品ID -->
    <select id="pageOnsaleIdsByAreaNo" resultType="java.lang.Integer">
        SELECT id
        FROM area_sku
        WHERE on_sale = 1
        <if test="areaNos != null and areaNos.size() > 0">
            AND area_no IN
            <foreach collection="areaNos" item="areaNo" open="(" separator="," close=")">
                #{areaNo}
            </foreach>
        </if>
        ORDER BY id DESC
    </select>

</mapper>