<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.crm.FollowUpRelationMapper">
    <!-- 结果集映射 -->
    <resultMap id="followUpRelationResultMap" type="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="reassign" property="reassign" jdbcType="TINYINT"/>
		<result column="last_follow_up_time" property="lastFollowUpTime" jdbcType="TIMESTAMP"/>
		<result column="reassign_time" property="reassignTime" jdbcType="TIMESTAMP"/>
		<result column="reason" property="reason" jdbcType="VARCHAR"/>
		<result column="follow_type" property="followType" jdbcType="INTEGER"/>
		<result column="danger_day" property="dangerDay" jdbcType="INTEGER"/>
		<result column="timing_follow_type" property="timingFollowType" jdbcType="INTEGER"/>
		<result column="care_bd_id" property="careBdId" jdbcType="NUMERIC"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="followUpRelationColumns">
          t.id,
          t.m_id,
          t.admin_id,
          t.admin_name,
          t.add_time,
          t.reassign,
          t.last_follow_up_time,
          t.reassign_time,
          t.reason,
          t.follow_type,
          t.danger_day,
          t.timing_follow_type,
          t.care_bd_id
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
            <if test="mIds != null">
                AND  m_id in
                <foreach collection="mIds" item="mid" open="(" close=")" separator=",">
                    #{mid}
                </foreach>
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="adminName != null and adminName !=''">
                AND t.admin_name = #{adminName}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="reassign != null">
                AND t.reassign = #{reassign}
            </if>
			<if test="lastFollowUpTime != null">
                AND t.last_follow_up_time = #{lastFollowUpTime}
            </if>
			<if test="reassignTime != null">
                AND t.reassign_time = #{reassignTime}
            </if>
			<if test="reason != null and reason !=''">
                AND t.reason = #{reason}
            </if>
			<if test="followType != null">
                AND t.follow_type = #{followType}
            </if>
			<if test="dangerDay != null">
                AND t.danger_day = #{dangerDay}
            </if>
			<if test="timingFollowType != null">
                AND t.timing_follow_type = #{timingFollowType}
            </if>
			<if test="careBdId != null">
                AND t.care_bd_id = #{careBdId}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="adminName != null">
                    t.admin_name = #{adminName},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="reassign != null">
                    t.reassign = #{reassign},
                </if>
                <if test="lastFollowUpTime != null">
                    t.last_follow_up_time = #{lastFollowUpTime},
                </if>
                <if test="reassignTime != null">
                    t.reassign_time = #{reassignTime},
                </if>
                <if test="reason != null">
                    t.reason = #{reason},
                </if>
                <if test="followType != null">
                    t.follow_type = #{followType},
                </if>
                <if test="dangerDay != null">
                    t.danger_day = #{dangerDay},
                </if>
                <if test="timingFollowType != null">
                    t.timing_follow_type = #{timingFollowType},
                </if>
                <if test="careBdId != null">
                    t.care_bd_id = #{careBdId},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="followUpRelationResultMap" >
        SELECT <include refid="followUpRelationColumns" />
        FROM follow_up_relation t
		WHERE t.id = #{id}
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation" resultMap="followUpRelationResultMap" >
        SELECT <include refid="followUpRelationColumns" />
        FROM follow_up_relation t
        <include refid="whereColumnBySelect"></include>
        order by t.id desc
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectOne" parameterType="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation" resultMap="followUpRelationResultMap" >
        SELECT <include refid="followUpRelationColumns" />
        FROM follow_up_relation t
        <include refid="whereColumnBySelect"></include>
        limit 1
    </select>

    <select id="batchQueryByMids" resultMap="followUpRelationResultMap">
        SELECT <include refid="followUpRelationColumns" />
        FROM follow_up_relation t
        WHERE  t.reassign = 0
        AND t.m_id IN
        <foreach collection="mIds" open="(" separator="," close=")" item="mId">
            #{mId}
        </foreach>
    </select>

    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation" >
        INSERT INTO follow_up_relation
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="adminName != null">
				  admin_name,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="reassign != null">
				  reassign,
              </if>
              <if test="lastFollowUpTime != null">
				  last_follow_up_time,
              </if>
              <if test="reassignTime != null">
				  reassign_time,
              </if>
              <if test="reason != null">
				  reason,
              </if>
              <if test="followType != null">
				  follow_type,
              </if>
              <if test="dangerDay != null">
				  danger_day,
              </if>
              <if test="timingFollowType != null">
				  timing_follow_type,
              </if>
              <if test="careBdId != null">
				  care_bd_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="adminName != null">
				#{adminName,jdbcType=VARCHAR},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="reassign != null">
				#{reassign,jdbcType=INTEGER},
              </if>
              <if test="lastFollowUpTime != null">
				#{lastFollowUpTime,jdbcType=TIMESTAMP},
              </if>
              <if test="reassignTime != null">
				#{reassignTime,jdbcType=TIMESTAMP},
              </if>
              <if test="reason != null">
				#{reason,jdbcType=VARCHAR},
              </if>
              <if test="followType != null">
				#{followType,jdbcType=INTEGER},
              </if>
              <if test="dangerDay != null">
				#{dangerDay,jdbcType=INTEGER},
              </if>
              <if test="timingFollowType != null">
				#{timingFollowType,jdbcType=INTEGER},
              </if>
              <if test="careBdId != null">
				#{careBdId,jdbcType=NUMERIC},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateById" parameterType="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation" >
        UPDATE follow_up_relation t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation" >
        DELETE FROM follow_up_relation t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM follow_up_relation t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>



</mapper>