<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.activity.MarketRuleHistoryMapper">
    <!-- 结果集映射 -->
    <resultMap id="marketRuleHistoryResultMap" type="net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="detail" property="detail" jdbcType="LONGVARCHAR"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="market_rule_id" property="marketRuleId" jdbcType="INTEGER"/>
		<result column="value" property="value" jdbcType="INTEGER"/>
		<result column="rule_level" property="ruleLevel" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="send_status" property="sendStatus" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="marketRuleHistoryColumns">
          t.id,
          t.detail,
          t.order_no,
          t.market_rule_id,
          t.value,
          t.rule_level,
          t.type,
          t.create_time,
          t.send_status
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="detail != null and detail !=''">
                AND t.detail = #{detail}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="marketRuleId != null">
                AND t.market_rule_id = #{marketRuleId}
            </if>
			<if test="value != null">
                AND t.value = #{value}
            </if>
			<if test="ruleLevel != null">
                AND t.rule_level = #{ruleLevel}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="sendStatus != null">
                AND t.send_status = #{sendStatus}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="detail != null">
                    t.detail = #{detail},
                </if>
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="marketRuleId != null">
                    t.market_rule_id = #{marketRuleId},
                </if>
                <if test="value != null">
                    t.value = #{value},
                </if>
                <if test="ruleLevel != null">
                    t.rule_level = #{ruleLevel},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="sendStatus != null">
                    t.send_status = #{sendStatus},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="marketRuleHistoryResultMap" >
        SELECT <include refid="marketRuleHistoryColumns" />
        FROM market_rule_history t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam"  resultType="net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity" >
        SELECT
            t.id id,
            t.detail detail,
            t.order_no orderNo,
            t.market_rule_id marketRuleId,
            t.value value,
            t.rule_level ruleLevel,
            t.type type,
            t.create_time createTime,
            t.send_status sendStatus
        FROM market_rule_history t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam" resultMap="marketRuleHistoryResultMap" >
        SELECT <include refid="marketRuleHistoryColumns" />
        FROM market_rule_history t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO market_rule_history
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="detail != null">
				  detail,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="marketRuleId != null">
				  market_rule_id,
              </if>
              <if test="value != null">
				  value,
              </if>
              <if test="ruleLevel != null">
				  rule_level,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="sendStatus != null">
				  send_status,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="detail != null">
				#{detail,jdbcType=LONGVARCHAR},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="marketRuleId != null">
				#{marketRuleId,jdbcType=INTEGER},
              </if>
              <if test="value != null">
				#{value,jdbcType=INTEGER},
              </if>
              <if test="ruleLevel != null">
				#{ruleLevel,jdbcType=INTEGER},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="sendStatus != null">
				#{sendStatus,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory" >
        UPDATE market_rule_history t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory" >
        DELETE FROM market_rule_history t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>