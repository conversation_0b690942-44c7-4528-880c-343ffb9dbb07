<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.account.ContactAdjustMapper">
    <!-- 结果集映射 -->
    <resultMap id="contactAdjustResultMap" type="net.summerfarm.manage.infrastructure.model.account.ContactAdjust">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="DATE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="INTEGER"/>
		<result column="contact_id" property="contactId" jdbcType="INTEGER"/>
		<result column="new_poi" property="newPoi" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="new_province" property="newProvince" jdbcType="VARCHAR"/>
		<result column="new_city" property="newCity" jdbcType="VARCHAR"/>
		<result column="new_area" property="newArea" jdbcType="VARCHAR"/>
		<result column="new_address" property="newAddress" jdbcType="VARCHAR"/>
		<result column="new_house_number" property="newHouseNumber" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="contactAdjustColumns">
          t.id,
          t.add_time,
          t.update_time,
          t.m_id,
          t.contact_id,
          t.new_poi,
          t.status,
          t.new_province,
          t.new_city,
          t.new_area,
          t.new_address,
          t.new_house_number
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="contactId != null">
                AND t.contact_id = #{contactId}
            </if>
			<if test="newPoi != null and newPoi !=''">
                AND t.new_poi = #{newPoi}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="newProvince != null and newProvince !=''">
                AND t.new_province = #{newProvince}
            </if>
			<if test="newCity != null and newCity !=''">
                AND t.new_city = #{newCity}
            </if>
			<if test="newArea != null and newArea !=''">
                AND t.new_area = #{newArea}
            </if>
			<if test="newAddress != null and newAddress !=''">
                AND t.new_address = #{newAddress}
            </if>
			<if test="newHouseNumber != null and newHouseNumber !=''">
                AND t.new_house_number = #{newHouseNumber}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="contactId != null">
                    t.contact_id = #{contactId},
                </if>
                <if test="newPoi != null">
                    t.new_poi = #{newPoi},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="newProvince != null">
                    t.new_province = #{newProvince},
                </if>
                <if test="newCity != null">
                    t.new_city = #{newCity},
                </if>
                <if test="newArea != null">
                    t.new_area = #{newArea},
                </if>
                <if test="newAddress != null">
                    t.new_address = #{newAddress},
                </if>
                <if test="newHouseNumber != null">
                    t.new_house_number = #{newHouseNumber},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="contactAdjustResultMap" >
        SELECT <include refid="contactAdjustColumns" />
        FROM contact_adjust t
		WHERE t.id = #{id}
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.common.input.merchant.MerchantQueryInput" resultMap="contactAdjustResultMap" >
        SELECT <include refid="contactAdjustColumns" />
        FROM contact_adjust t
        where 1=1
        <if test="contactAdjustId != null">
            and id = #{contactAdjustId}
        </if>
            and t.status = 0
        order  by t.id desc
    </select>



    <!-- 根据条件查询对象 -->
    <select id="selectOne" parameterType="net.summerfarm.manage.infrastructure.model.account.ContactAdjust" resultMap="contactAdjustResultMap" >
        SELECT <include refid="contactAdjustColumns" />
        FROM contact_adjust t
        <include refid="whereColumnBySelect"></include>
    </select>

	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.account.ContactAdjust" >
        INSERT INTO contact_adjust
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="contactId != null">
				  contact_id,
              </if>
              <if test="newPoi != null">
				  new_poi,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="newProvince != null">
				  new_province,
              </if>
              <if test="newCity != null">
				  new_city,
              </if>
              <if test="newArea != null">
				  new_area,
              </if>
              <if test="newAddress != null">
				  new_address,
              </if>
              <if test="newHouseNumber != null">
				  new_house_number,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=DATE},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=INTEGER},
              </if>
              <if test="contactId != null">
				#{contactId,jdbcType=INTEGER},
              </if>
              <if test="newPoi != null">
				#{newPoi,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="newProvince != null">
				#{newProvince,jdbcType=VARCHAR},
              </if>
              <if test="newCity != null">
				#{newCity,jdbcType=VARCHAR},
              </if>
              <if test="newArea != null">
				#{newArea,jdbcType=VARCHAR},
              </if>
              <if test="newAddress != null">
				#{newAddress,jdbcType=VARCHAR},
              </if>
              <if test="newHouseNumber != null">
				#{newHouseNumber,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateByIdSelective" parameterType="net.summerfarm.manage.infrastructure.model.account.ContactAdjust" >
        UPDATE contact_adjust t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.account.ContactAdjust" >
        DELETE FROM contact_adjust t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>





</mapper>