<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.AfterSaleOrderMapper">
    <!-- 结果集映射 -->
    <resultMap id="afterSaleOrderResultMap" type="net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="after_sale_type" property="afterSaleType" jdbcType="VARCHAR"/>
		<result column="proof_pic" property="proofPic" jdbcType="VARCHAR"/>
		<result column="handle_type" property="handleType" jdbcType="INTEGER"/>
		<result column="handle_num" property="handleNum" jdbcType="DOUBLE"/>
		<result column="handler" property="handler" jdbcType="VARCHAR"/>
		<result column="auditer" property="auditer" jdbcType="VARCHAR"/>
		<result column="apply_remark" property="applyRemark" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="handle_remark" property="handleRemark" jdbcType="VARCHAR"/>
		<result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="grade" property="grade" jdbcType="INTEGER"/>
		<result column="deliveryed" property="deliveryed" jdbcType="INTEGER"/>
		<result column="times" property="times" jdbcType="INTEGER"/>
		<result column="suit_id" property="suitId" jdbcType="INTEGER"/>
		<result column="view" property="view" jdbcType="INTEGER"/>
		<result column="is_full" property="isFull" jdbcType="TINYINT"/>
		<result column="account_id" property="accountId" jdbcType="NUMERIC"/>
		<result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
		<result column="recovery_type" property="recoveryType" jdbcType="INTEGER"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="after_sale_remark_type" property="afterSaleRemarkType" jdbcType="TINYINT"/>
		<result column="after_sale_remark" property="afterSaleRemark" jdbcType="VARCHAR"/>
		<result column="after_sale_order_status" property="afterSaleOrderStatus" jdbcType="TINYINT"/>
		<result column="refund_freight" property="refundFreight" jdbcType="TINYINT"/>
		<result column="closer" property="closer" jdbcType="VARCHAR"/>
		<result column="close_time" property="closeTime" jdbcType="TIMESTAMP"/>
		<result column="product_type" property="productType" jdbcType="INTEGER"/>
		<result column="carrying_goods" property="carryingGoods" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="afterSaleOrderColumns">
          t.id,
          t.after_sale_order_no,
          t.m_id,
          t.order_no,
          t.sku,
          t.quantity,
          t.after_sale_type,
          t.proof_pic,
          t.handle_type,
          t.handle_num,
          t.handler,
          t.auditer,
          t.apply_remark,
          t.status,
          t.handle_remark,
          t.after_sale_unit,
          t.updatetime,
          t.add_time,
          t.type,
          t.grade,
          t.deliveryed,
          t.times,
          t.suit_id,
          t.view,
          t.is_full,
          t.account_id,
          t.delivery_id,
          t.recovery_type,
          t.update_time,
          t.after_sale_remark_type,
          t.after_sale_remark,
          t.after_sale_order_status,
          t.refund_freight,
          t.closer,
          t.close_time,
          t.product_type,
          t.carrying_goods
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="afterSaleOrderNo != null and afterSaleOrderNo !=''">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="afterSaleType != null and afterSaleType !=''">
                AND t.after_sale_type = #{afterSaleType}
            </if>
			<if test="proofPic != null and proofPic !=''">
                AND t.proof_pic = #{proofPic}
            </if>
			<if test="handleType != null">
                AND t.handle_type = #{handleType}
            </if>
			<if test="handleNum != null">
                AND t.handle_num = #{handleNum}
            </if>
			<if test="handler != null and handler !=''">
                AND t.handler = #{handler}
            </if>
			<if test="auditer != null and auditer !=''">
                AND t.auditer = #{auditer}
            </if>
			<if test="applyRemark != null and applyRemark !=''">
                AND t.apply_remark = #{applyRemark}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="handleRemark != null and handleRemark !=''">
                AND t.handle_remark = #{handleRemark}
            </if>
			<if test="afterSaleUnit != null and afterSaleUnit !=''">
                AND t.after_sale_unit = #{afterSaleUnit}
            </if>
			<if test="updatetime != null">
                AND t.updatetime = #{updatetime}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="grade != null">
                AND t.grade = #{grade}
            </if>
			<if test="deliveryed != null">
                AND t.deliveryed = #{deliveryed}
            </if>
			<if test="times != null">
                AND t.times = #{times}
            </if>
			<if test="suitId != null">
                AND t.suit_id = #{suitId}
            </if>
			<if test="view != null">
                AND t.view = #{view}
            </if>
			<if test="isFull != null">
                AND t.is_full = #{isFull}
            </if>
			<if test="accountId != null">
                AND t.account_id = #{accountId}
            </if>
			<if test="deliveryId != null">
                AND t.delivery_id = #{deliveryId}
            </if>
			<if test="recoveryType != null">
                AND t.recovery_type = #{recoveryType}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="afterSaleRemarkType != null">
                AND t.after_sale_remark_type = #{afterSaleRemarkType}
            </if>
			<if test="afterSaleRemark != null and afterSaleRemark !=''">
                AND t.after_sale_remark = #{afterSaleRemark}
            </if>
			<if test="afterSaleOrderStatus != null">
                AND t.after_sale_order_status = #{afterSaleOrderStatus}
            </if>
			<if test="refundFreight != null">
                AND t.refund_freight = #{refundFreight}
            </if>
			<if test="closer != null and closer !=''">
                AND t.closer = #{closer}
            </if>
			<if test="closeTime != null">
                AND t.close_time = #{closeTime}
            </if>
			<if test="productType != null">
                AND t.product_type = #{productType}
            </if>
			<if test="carryingGoods != null">
                AND t.carrying_goods = #{carryingGoods}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="afterSaleOrderNo != null">
                    t.after_sale_order_no = #{afterSaleOrderNo},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="afterSaleType != null">
                    t.after_sale_type = #{afterSaleType},
                </if>
                <if test="proofPic != null">
                    t.proof_pic = #{proofPic},
                </if>
                <if test="handleType != null">
                    t.handle_type = #{handleType},
                </if>
                <if test="handleNum != null">
                    t.handle_num = #{handleNum},
                </if>
                <if test="handler != null">
                    t.handler = #{handler},
                </if>
                <if test="auditer != null">
                    t.auditer = #{auditer},
                </if>
                <if test="applyRemark != null">
                    t.apply_remark = #{applyRemark},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="handleRemark != null">
                    t.handle_remark = #{handleRemark},
                </if>
                <if test="afterSaleUnit != null">
                    t.after_sale_unit = #{afterSaleUnit},
                </if>
                <if test="updatetime != null">
                    t.updatetime = #{updatetime},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="grade != null">
                    t.grade = #{grade},
                </if>
                <if test="deliveryed != null">
                    t.deliveryed = #{deliveryed},
                </if>
                <if test="times != null">
                    t.times = #{times},
                </if>
                <if test="suitId != null">
                    t.suit_id = #{suitId},
                </if>
                <if test="view != null">
                    t.view = #{view},
                </if>
                <if test="isFull != null">
                    t.is_full = #{isFull},
                </if>
                <if test="accountId != null">
                    t.account_id = #{accountId},
                </if>
                <if test="deliveryId != null">
                    t.delivery_id = #{deliveryId},
                </if>
                <if test="recoveryType != null">
                    t.recovery_type = #{recoveryType},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="afterSaleRemarkType != null">
                    t.after_sale_remark_type = #{afterSaleRemarkType},
                </if>
                <if test="afterSaleRemark != null">
                    t.after_sale_remark = #{afterSaleRemark},
                </if>
                <if test="afterSaleOrderStatus != null">
                    t.after_sale_order_status = #{afterSaleOrderStatus},
                </if>
                <if test="refundFreight != null">
                    t.refund_freight = #{refundFreight},
                </if>
                <if test="closer != null">
                    t.closer = #{closer},
                </if>
                <if test="closeTime != null">
                    t.close_time = #{closeTime},
                </if>
                <if test="productType != null">
                    t.product_type = #{productType},
                </if>
                <if test="carryingGoods != null">
                    t.carrying_goods = #{carryingGoods},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="afterSaleOrderResultMap" >
        SELECT <include refid="afterSaleOrderColumns" />
        FROM after_sale_order t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.AfterSaleOrderQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity" >
        SELECT
            t.id id,
            t.after_sale_order_no afterSaleOrderNo,
            t.m_id mId,
            t.order_no orderNo,
            t.sku sku,
            t.quantity quantity,
            t.after_sale_type afterSaleType,
            t.proof_pic proofPic,
            t.handle_type handleType,
            t.handle_num handleNum,
            t.handler handler,
            t.auditer auditer,
            t.apply_remark applyRemark,
            t.status status,
            t.handle_remark handleRemark,
            t.after_sale_unit afterSaleUnit,
            t.updatetime updatetime,
            t.add_time addTime,
            t.type type,
            t.grade grade,
            t.deliveryed deliveryed,
            t.times times,
            t.suit_id suitId,
            t.view view,
            t.is_full isFull,
            t.account_id accountId,
            t.delivery_id deliveryId,
            t.recovery_type recoveryType,
            t.update_time updateTime,
            t.after_sale_remark_type afterSaleRemarkType,
            t.after_sale_remark afterSaleRemark,
            t.after_sale_order_status afterSaleOrderStatus,
            t.refund_freight refundFreight,
            t.closer closer,
            t.close_time closeTime,
            t.product_type productType,
            t.carrying_goods carryingGoods
        FROM after_sale_order t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.AfterSaleOrderQueryParam" resultMap="afterSaleOrderResultMap" >
        SELECT <include refid="afterSaleOrderColumns" />
        FROM after_sale_order t
        <include refid="whereColumnBySelect"></include>
        <if test="orderNo == null">
            order by t.add_time desc,t.order_no desc
        </if>
        <if test="orderNo != null">
            order by t.add_time
        </if>
    </select>
    <select id="selectByOrderNo" resultType="net.summerfarm.manage.domain.order.flatObject.AfterSaleOrderFlatObject">
        select asp.after_sale_order_no afterSaleOrderNo, asp.quantity, asp.handle_num handleNum, asp.status, asp.handle_type handleType,
        asp.refund_type refundType, aso.deliveryed
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        where aso.order_no = #{orderNo}
        <if test="sku != null">
            AND aso.sku = #{sku}
        </if>
        <if test="suitId != null">
            AND aso.suit_id = #{suitId}
        </if>
        <if test="type != null">
            AND aso.type = #{type}
        </if>
        <if test="status != null">
            AND aso.status = #{status}
        </if>
        order by aso.add_time
    </select>

    <select id="getAfterSaleInfoByOrderNos" resultMap="afterSaleOrderResultMap">
        SELECT <include refid="afterSaleOrderColumns" />
        FROM after_sale_order t
        WHERE t.deliveryed = 0 and t.status in (0, 1, 2) and t.order_no in
        <foreach collection="normalOrderNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO after_sale_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="afterSaleOrderNo != null">
				  after_sale_order_no,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="quantity != null">
				  quantity,
              </if>
              <if test="afterSaleType != null">
				  after_sale_type,
              </if>
              <if test="proofPic != null">
				  proof_pic,
              </if>
              <if test="handleType != null">
				  handle_type,
              </if>
              <if test="handleNum != null">
				  handle_num,
              </if>
              <if test="handler != null">
				  handler,
              </if>
              <if test="auditer != null">
				  auditer,
              </if>
              <if test="applyRemark != null">
				  apply_remark,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="handleRemark != null">
				  handle_remark,
              </if>
              <if test="afterSaleUnit != null">
				  after_sale_unit,
              </if>
              <if test="updatetime != null">
				  updatetime,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="grade != null">
				  grade,
              </if>
              <if test="deliveryed != null">
				  deliveryed,
              </if>
              <if test="times != null">
				  times,
              </if>
              <if test="suitId != null">
				  suit_id,
              </if>
              <if test="view != null">
				  view,
              </if>
              <if test="isFull != null">
				  is_full,
              </if>
              <if test="accountId != null">
				  account_id,
              </if>
              <if test="deliveryId != null">
				  delivery_id,
              </if>
              <if test="recoveryType != null">
				  recovery_type,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="afterSaleRemarkType != null">
				  after_sale_remark_type,
              </if>
              <if test="afterSaleRemark != null">
				  after_sale_remark,
              </if>
              <if test="afterSaleOrderStatus != null">
				  after_sale_order_status,
              </if>
              <if test="refundFreight != null">
				  refund_freight,
              </if>
              <if test="closer != null">
				  closer,
              </if>
              <if test="closeTime != null">
				  close_time,
              </if>
              <if test="productType != null">
				  product_type,
              </if>
              <if test="carryingGoods != null">
				  carrying_goods,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="afterSaleOrderNo != null">
				#{afterSaleOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
              </if>
              <if test="afterSaleType != null">
				#{afterSaleType,jdbcType=VARCHAR},
              </if>
              <if test="proofPic != null">
				#{proofPic,jdbcType=VARCHAR},
              </if>
              <if test="handleType != null">
				#{handleType,jdbcType=INTEGER},
              </if>
              <if test="handleNum != null">
				#{handleNum,jdbcType=DOUBLE},
              </if>
              <if test="handler != null">
				#{handler,jdbcType=VARCHAR},
              </if>
              <if test="auditer != null">
				#{auditer,jdbcType=VARCHAR},
              </if>
              <if test="applyRemark != null">
				#{applyRemark,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="handleRemark != null">
				#{handleRemark,jdbcType=VARCHAR},
              </if>
              <if test="afterSaleUnit != null">
				#{afterSaleUnit,jdbcType=VARCHAR},
              </if>
              <if test="updatetime != null">
				#{updatetime,jdbcType=TIMESTAMP},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="grade != null">
				#{grade,jdbcType=INTEGER},
              </if>
              <if test="deliveryed != null">
				#{deliveryed,jdbcType=INTEGER},
              </if>
              <if test="times != null">
				#{times,jdbcType=INTEGER},
              </if>
              <if test="suitId != null">
				#{suitId,jdbcType=INTEGER},
              </if>
              <if test="view != null">
				#{view,jdbcType=INTEGER},
              </if>
              <if test="isFull != null">
				#{isFull,jdbcType=TINYINT},
              </if>
              <if test="accountId != null">
				#{accountId,jdbcType=NUMERIC},
              </if>
              <if test="deliveryId != null">
				#{deliveryId,jdbcType=INTEGER},
              </if>
              <if test="recoveryType != null">
				#{recoveryType,jdbcType=INTEGER},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="afterSaleRemarkType != null">
				#{afterSaleRemarkType,jdbcType=TINYINT},
              </if>
              <if test="afterSaleRemark != null">
				#{afterSaleRemark,jdbcType=VARCHAR},
              </if>
              <if test="afterSaleOrderStatus != null">
				#{afterSaleOrderStatus,jdbcType=TINYINT},
              </if>
              <if test="refundFreight != null">
				#{refundFreight,jdbcType=TINYINT},
              </if>
              <if test="closer != null">
				#{closer,jdbcType=VARCHAR},
              </if>
              <if test="closeTime != null">
				#{closeTime,jdbcType=TIMESTAMP},
              </if>
              <if test="productType != null">
				#{productType,jdbcType=INTEGER},
              </if>
              <if test="carryingGoods != null">
				#{carryingGoods,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder" >
        UPDATE after_sale_order t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder" >
        DELETE FROM after_sale_order t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>


    <select id="getNotDeliveryAfterSaleInfoByOrderNo"  resultType="net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity">
        select asp.quantity,asp.handle_num handlePrice ,aso.sku, aso.after_sale_order_no afterSaleOrderNo
        from after_sale_order aso
                 left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
        where asp.status = 2 and aso.deliveryed = 0
          and aso.order_no = #{orderNo}
    </select>


</mapper>