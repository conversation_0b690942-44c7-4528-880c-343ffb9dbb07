<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.WxShippingInfoUploadRecordMapper">
    <!-- 结果集映射 -->
    <resultMap id="wxShippingInfoUploadRecordResultMap" type="net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
		<result column="transaction_id" property="transactionId" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="wxShippingInfoUploadRecordEntityResultMap" type="net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wxShippingInfoUploadRecordColumns">
          t.id,
          t.master_order_no,
          t.transaction_id,
          t.status,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="masterOrderNo != null and masterOrderNo !=''">
                AND t.master_order_no = #{masterOrderNo}
            </if>
			<if test="transactionId != null and transactionId !=''">
                AND t.transaction_id = #{transactionId}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="masterOrderNo != null">
                    t.master_order_no = #{masterOrderNo},
                </if>
                <if test="transactionId != null">
                    t.transaction_id = #{transactionId},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wxShippingInfoUploadRecordResultMap" >
        SELECT <include refid="wxShippingInfoUploadRecordColumns" />
        FROM wx_shipping_info_upload_record t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity" >
        SELECT
            t.id id,
            t.master_order_no masterOrderNo,
            t.transaction_id transactionId,
            t.status status,
            t.create_time createTime,
            t.update_time updateTime
        FROM wx_shipping_info_upload_record t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam" resultMap="wxShippingInfoUploadRecordResultMap" >
        SELECT <include refid="wxShippingInfoUploadRecordColumns" />
        FROM wx_shipping_info_upload_record t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wx_shipping_info_upload_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="masterOrderNo != null">
				  master_order_no,
              </if>
              <if test="transactionId != null">
				  transaction_id,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="masterOrderNo != null">
				#{masterOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="transactionId != null">
				#{transactionId,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord" >
        UPDATE wx_shipping_info_upload_record t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord" >
        DELETE FROM wx_shipping_info_upload_record
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <!-- 根据条件查询对象 -->
    <select id="selectByMasterOrderNo"  resultMap="wxShippingInfoUploadRecordEntityResultMap" >
        SELECT <include refid="wxShippingInfoUploadRecordColumns" />
        FROM wx_shipping_info_upload_record t
        where t.master_order_no = #{masterOrderNo}
    </select>



</mapper>