<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.payment.MasterPaymentMapper">
    <!-- 结果集映射 -->
    <resultMap id="masterPaymentResultMap" type="net.summerfarm.manage.infrastructure.model.payment.MasterPayment">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="pay_type" property="payType" jdbcType="VARCHAR"/>
		<result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
		<result column="transaction_number" property="transactionNumber" jdbcType="VARCHAR"/>
		<result column="money" property="money" jdbcType="DOUBLE"/>
		<result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
		<result column="trade_type" property="tradeType" jdbcType="VARCHAR"/>
		<result column="bank_type" property="bankType" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="TINYINT"/>
		<result column="err_code" property="errCode" jdbcType="VARCHAR"/>
		<result column="err_code_des" property="errCodeDes" jdbcType="VARCHAR"/>
		<result column="company_account_id" property="companyAccountId" jdbcType="INTEGER"/>
		<result column="scan_code" property="scanCode" jdbcType="VARCHAR"/>
		<result column="account_info" property="accountInfo" jdbcType="VARCHAR"/>
		<result column="boc_pay_type" property="bocPayType" jdbcType="VARCHAR"/>
		<result column="online_pay_end_time" property="onlinePayEndTime" jdbcType="TIMESTAMP"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="masterPaymentColumns">
          t.id,
          t.pay_type,
          t.master_order_no,
          t.transaction_number,
          t.money,
          t.end_time,
          t.trade_type,
          t.bank_type,
          t.status,
          t.err_code,
          t.err_code_des,
          t.company_account_id,
          t.scan_code,
          t.account_info,
          t.boc_pay_type,
          t.online_pay_end_time,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="payType != null and payType !=''">
                AND t.pay_type = #{payType}
            </if>
			<if test="masterOrderNo != null and masterOrderNo !=''">
                AND t.master_order_no = #{masterOrderNo}
            </if>
			<if test="transactionNumber != null and transactionNumber !=''">
                AND t.transaction_number = #{transactionNumber}
            </if>
			<if test="money != null">
                AND t.money = #{money}
            </if>
			<if test="endTime != null">
                AND t.end_time = #{endTime}
            </if>
			<if test="tradeType != null and tradeType !=''">
                AND t.trade_type = #{tradeType}
            </if>
			<if test="bankType != null and bankType !=''">
                AND t.bank_type = #{bankType}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="errCode != null and errCode !=''">
                AND t.err_code = #{errCode}
            </if>
			<if test="errCodeDes != null and errCodeDes !=''">
                AND t.err_code_des = #{errCodeDes}
            </if>
			<if test="companyAccountId != null">
                AND t.company_account_id = #{companyAccountId}
            </if>
			<if test="scanCode != null and scanCode !=''">
                AND t.scan_code = #{scanCode}
            </if>
			<if test="accountInfo != null and accountInfo !=''">
                AND t.account_info = #{accountInfo}
            </if>
			<if test="bocPayType != null and bocPayType !=''">
                AND t.boc_pay_type = #{bocPayType}
            </if>
			<if test="onlinePayEndTime != null">
                AND t.online_pay_end_time = #{onlinePayEndTime}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="payType != null">
                    t.pay_type = #{payType},
                </if>
                <if test="masterOrderNo != null">
                    t.master_order_no = #{masterOrderNo},
                </if>
                <if test="transactionNumber != null">
                    t.transaction_number = #{transactionNumber},
                </if>
                <if test="money != null">
                    t.money = #{money},
                </if>
                <if test="endTime != null">
                    t.end_time = #{endTime},
                </if>
                <if test="tradeType != null">
                    t.trade_type = #{tradeType},
                </if>
                <if test="bankType != null">
                    t.bank_type = #{bankType},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="errCode != null">
                    t.err_code = #{errCode},
                </if>
                <if test="errCodeDes != null">
                    t.err_code_des = #{errCodeDes},
                </if>
                <if test="companyAccountId != null">
                    t.company_account_id = #{companyAccountId},
                </if>
                <if test="scanCode != null">
                    t.scan_code = #{scanCode},
                </if>
                <if test="accountInfo != null">
                    t.account_info = #{accountInfo},
                </if>
                <if test="bocPayType != null">
                    t.boc_pay_type = #{bocPayType},
                </if>
                <if test="onlinePayEndTime != null">
                    t.online_pay_end_time = #{onlinePayEndTime},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="masterPaymentResultMap" >
        SELECT <include refid="masterPaymentColumns" />
        FROM master_payment t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.payment.param.query.MasterPaymentQueryParam"  resultType="net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity" >
        SELECT
            t.id id,
            t.pay_type payType,
            t.master_order_no masterOrderNo,
            t.transaction_number transactionNumber,
            t.money money,
            t.end_time endTime,
            t.trade_type tradeType,
            t.bank_type bankType,
            t.status status,
            t.err_code errCode,
            t.err_code_des errCodeDes,
            t.company_account_id companyAccountId,
            t.scan_code scanCode,
            t.account_info accountInfo,
            t.boc_pay_type bocPayType,
            t.online_pay_end_time onlinePayEndTime,
            t.create_time createTime,
            t.update_time updateTime
        FROM master_payment t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.payment.param.query.MasterPaymentQueryParam" resultMap="masterPaymentResultMap" >
        SELECT <include refid="masterPaymentColumns" />
        FROM master_payment t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.payment.MasterPayment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO master_payment
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="payType != null">
				  pay_type,
              </if>
              <if test="masterOrderNo != null">
				  master_order_no,
              </if>
              <if test="transactionNumber != null">
				  transaction_number,
              </if>
              <if test="money != null">
				  money,
              </if>
              <if test="endTime != null">
				  end_time,
              </if>
              <if test="tradeType != null">
				  trade_type,
              </if>
              <if test="bankType != null">
				  bank_type,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="errCode != null">
				  err_code,
              </if>
              <if test="errCodeDes != null">
				  err_code_des,
              </if>
              <if test="companyAccountId != null">
				  company_account_id,
              </if>
              <if test="scanCode != null">
				  scan_code,
              </if>
              <if test="accountInfo != null">
				  account_info,
              </if>
              <if test="bocPayType != null">
				  boc_pay_type,
              </if>
              <if test="onlinePayEndTime != null">
				  online_pay_end_time,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="payType != null">
				#{payType,jdbcType=VARCHAR},
              </if>
              <if test="masterOrderNo != null">
				#{masterOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="transactionNumber != null">
				#{transactionNumber,jdbcType=VARCHAR},
              </if>
              <if test="money != null">
				#{money,jdbcType=DOUBLE},
              </if>
              <if test="endTime != null">
				#{endTime,jdbcType=TIMESTAMP},
              </if>
              <if test="tradeType != null">
				#{tradeType,jdbcType=VARCHAR},
              </if>
              <if test="bankType != null">
				#{bankType,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=TINYINT},
              </if>
              <if test="errCode != null">
				#{errCode,jdbcType=VARCHAR},
              </if>
              <if test="errCodeDes != null">
				#{errCodeDes,jdbcType=VARCHAR},
              </if>
              <if test="companyAccountId != null">
				#{companyAccountId,jdbcType=INTEGER},
              </if>
              <if test="scanCode != null">
				#{scanCode,jdbcType=VARCHAR},
              </if>
              <if test="accountInfo != null">
				#{accountInfo,jdbcType=VARCHAR},
              </if>
              <if test="bocPayType != null">
				#{bocPayType,jdbcType=VARCHAR},
              </if>
              <if test="onlinePayEndTime != null">
				#{onlinePayEndTime,jdbcType=TIMESTAMP},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.payment.MasterPayment" >
        UPDATE master_payment t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.payment.MasterPayment" >
        DELETE FROM master_payment
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>