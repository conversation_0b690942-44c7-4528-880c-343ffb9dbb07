<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.coupon.MerchantCouponMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantCouponResultMap" type="net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="coupon_id" property="couponId" jdbcType="INTEGER"/>
		<result column="vaild_date" property="vaildDate" jdbcType="TIMESTAMP"/>
		<result column="sender" property="sender" jdbcType="VARCHAR"/>
		<result column="used" property="used" jdbcType="TINYINT"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="view" property="view" jdbcType="INTEGER"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="receive_type" property="receiveType" jdbcType="TINYINT"/>
		<result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="send_id" property="sendId" jdbcType="NUMERIC"/>
		<result column="related_id" property="relatedId" jdbcType="NUMERIC"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantCouponColumns">
          t.id,
          t.m_id,
          t.coupon_id,
          t.vaild_date,
          t.sender,
          t.used,
          t.add_time,
          t.view,
          t.order_no,
          t.receive_type,
          t.start_time,
          t.update_time,
          t.send_id
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="couponId != null">
                AND t.coupon_id = #{couponId}
            </if>
			<if test="vaildDate != null">
                AND t.vaild_date = #{vaildDate}
            </if>
			<if test="sender != null and sender !=''">
                AND t.sender = #{sender}
            </if>
			<if test="used != null">
                AND t.used = #{used}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="view != null">
                AND t.view = #{view}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="receiveType != null">
                AND t.receive_type = #{receiveType}
            </if>
			<if test="startTime != null">
                AND t.start_time = #{startTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="sendId != null">
                AND t.send_id = #{sendId}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="couponId != null">
                    t.coupon_id = #{couponId},
                </if>
                <if test="vaildDate != null">
                    t.vaild_date = #{vaildDate},
                </if>
                <if test="sender != null">
                    t.sender = #{sender},
                </if>
                <if test="used != null">
                    t.used = #{used},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="view != null">
                    t.view = #{view},
                </if>
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="receiveType != null">
                    t.receive_type = #{receiveType},
                </if>
                <if test="startTime != null">
                    t.start_time = #{startTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="sendId != null">
                    t.send_id = #{sendId},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="merchantCouponResultMap" >
        SELECT <include refid="merchantCouponColumns" />
        FROM merchant_coupon t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.coupon.param.query.MerchantCouponQueryParam"  resultType="net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity" >
        SELECT
            t.id id,
            t.m_id mId,
            t.coupon_id couponId,
            t.vaild_date vaildDate,
            t.sender sender,
            t.used used,
            t.add_time addTime,
            t.view view,
            t.order_no orderNo,
            t.receive_type receiveType,
            t.start_time startTime,
            t.update_time updateTime,
            t.send_id sendId
        FROM merchant_coupon t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.coupon.param.query.MerchantCouponQueryParam" resultMap="merchantCouponResultMap" >
        SELECT <include refid="merchantCouponColumns" />
        FROM merchant_coupon t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO merchant_coupon
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="couponId != null">
				  coupon_id,
              </if>
              <if test="vaildDate != null">
				  vaild_date,
              </if>
              <if test="sender != null">
				  sender,
              </if>
              <if test="used != null">
				  used,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="view != null">
				  view,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="receiveType != null">
				  receive_type,
              </if>
              <if test="startTime != null">
				  start_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="sendId != null">
				  send_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="couponId != null">
				#{couponId,jdbcType=INTEGER},
              </if>
              <if test="vaildDate != null">
				#{vaildDate,jdbcType=TIMESTAMP},
              </if>
              <if test="sender != null">
				#{sender,jdbcType=VARCHAR},
              </if>
              <if test="used != null">
				#{used,jdbcType=TINYINT},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="view != null">
				#{view,jdbcType=INTEGER},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="receiveType != null">
				#{receiveType,jdbcType=TINYINT},
              </if>
              <if test="startTime != null">
				#{startTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="sendId != null">
				#{sendId,jdbcType=NUMERIC},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon" >
        UPDATE merchant_coupon t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon" >
        DELETE FROM merchant_coupon t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteMerchantCoupon">
        DELETE FROM merchant_coupon
        WHERE m_id = #{mId,jdbcType=NUMERIC}
          and coupon_id = #{couponId,jdbcType=INTEGER} and used = #{used,jdbcType=TINYINT}
          and related_id = #{relatedId} and receive_type = #{receiveType,jdbcType=TINYINT}
    </delete>


</mapper>