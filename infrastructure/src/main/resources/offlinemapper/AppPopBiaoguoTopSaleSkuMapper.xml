<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.offlinemapper.AppPopBiaoguoTopSaleSkuMapper">
    <!-- 结果集映射 -->
    <resultMap id="appPopBiaoguoTopSaleSkuResultMap" type="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoTopSaleSku">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
		<result column="competitor" property="competitor" jdbcType="VARCHAR"/>
		<result column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
		<result column="spider_fetch_time" property="spiderFetchTime" jdbcType="VARCHAR"/>
		<result column="final_standard_price" property="finalStandardPrice" jdbcType="DOUBLE"/>
		<result column="month_sale" property="monthSale" jdbcType="NUMERIC"/>
		<result column="url" property="url" jdbcType="VARCHAR"/>
		<result column="gross_weight" property="grossWeight" jdbcType="VARCHAR"/>
		<result column="net_weight" property="netWeight" jdbcType="VARCHAR"/>
		<result column="specification" property="specification" jdbcType="VARCHAR"/>
		<result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
		<result column="sales_volume_3d" property="salesVolume3d" jdbcType="NUMERIC"/>
		<result column="monthsale_gmv" property="monthsaleGmv" jdbcType="DOUBLE"/>
		<result column="category_level2" property="categoryLevel2" jdbcType="VARCHAR"/>
		<result column="monthsale_gmv_3d_ago" property="monthsaleGmv3dAgo" jdbcType="DOUBLE"/>
		<result column="top_matched_xianmu_sku_list" property="topMatchedXianmuSkuList" jdbcType="LONGVARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="VARCHAR"/>
		<result column="ds" property="ds" jdbcType="VARCHAR"/>
        <result column="category1" property="category1" jdbcType="VARCHAR"/>
        <result column="category2" property="category2" jdbcType="VARCHAR"/>
        <result column="category3" property="category3" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="appPopBiaoguoTopSaleSkuColumns">
          t.id,
          t.category_name,
          t.competitor,
          t.sku_code,
          t.spider_fetch_time,
          t.final_standard_price,
          t.month_sale,
          t.url,
          t.gross_weight,
          t.net_weight,
          t.specification,
          t.goods_name,
          t.sales_volume_3d,
          t.monthsale_gmv,
          t.category_level2,
          t.monthsale_gmv_3d_ago,
          t.top_matched_xianmu_sku_list,
          t.create_time,
          t.ds,
          t.category1,
          t.category2,
          t.category3
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="categoryName != null and categoryName !=''">
                AND t.category_name = #{categoryName}
            </if>
			<if test="competitor != null and competitor !=''">
                AND t.competitor = #{competitor}
            </if>
			<if test="skuCode != null and skuCode !=''">
                AND t.sku_code = #{skuCode}
            </if>
            <if test="skuCodeList != null and skuCodeList.size > 0">
                AND t.sku_code in
                <foreach collection="skuCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="notInSkuCodeList != null and notInSkuCodeList.size > 0">
                AND t.sku_code not in
                <foreach collection="notInSkuCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
			<if test="spiderFetchTime != null and spiderFetchTime !=''">
                AND t.spider_fetch_time = #{spiderFetchTime}
            </if>
			<if test="finalStandardPrice != null">
                AND t.final_standard_price = #{finalStandardPrice}
            </if>
			<if test="monthSale != null">
                AND t.month_sale = #{monthSale}
            </if>
			<if test="url != null and url !=''">
                AND t.url = #{url}
            </if>
			<if test="grossWeight != null and grossWeight !=''">
                AND t.gross_weight = #{grossWeight}
            </if>
			<if test="netWeight != null and netWeight !=''">
                AND t.net_weight = #{netWeight}
            </if>
			<if test="specification != null and specification !=''">
                AND t.specification = #{specification}
            </if>
			<if test="goodsName != null and goodsName !=''">
                AND t.goods_name like CONCAT('%', #{goodsName} ,'%')
            </if>
			<if test="salesVolume3d != null">
                AND t.sales_volume_3d = #{salesVolume3d}
            </if>
			<if test="monthsaleGmv != null">
                AND t.monthsale_gmv = #{monthsaleGmv}
            </if>
			<if test="categoryLevel2 != null and categoryLevel2 !=''">
                AND t.category_level2 = #{categoryLevel2}
            </if>
			<if test="monthsaleGmv3dAgo != null">
                AND t.monthsale_gmv_3d_ago = #{monthsaleGmv3dAgo}
            </if>
			<if test="topMatchedXianmuSkuList != null and topMatchedXianmuSkuList !=''">
                AND t.top_matched_xianmu_sku_list = #{topMatchedXianmuSkuList}
            </if>
			<if test="createTime != null and createTime !=''">
                AND t.create_time = #{createTime}
            </if>
			<if test="ds != null and ds !=''">
                AND t.ds = #{ds}
            </if>
            <if test="category1 != null and category1 !=''">
                AND t.category1 = #{category1}
            </if>
            <if test="category2 != null and category2 !=''">
                AND t.category2 = #{category2}
            </if>
            <if test="category3 != null and category3 !=''">
                AND t.category3 = #{category3}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="categoryName != null">
                    t.category_name = #{categoryName},
                </if>
                <if test="competitor != null">
                    t.competitor = #{competitor},
                </if>
                <if test="skuCode != null">
                    t.sku_code = #{skuCode},
                </if>
                <if test="spiderFetchTime != null">
                    t.spider_fetch_time = #{spiderFetchTime},
                </if>
                <if test="finalStandardPrice != null">
                    t.final_standard_price = #{finalStandardPrice},
                </if>
                <if test="monthSale != null">
                    t.month_sale = #{monthSale},
                </if>
                <if test="url != null">
                    t.url = #{url},
                </if>
                <if test="grossWeight != null">
                    t.gross_weight = #{grossWeight},
                </if>
                <if test="netWeight != null">
                    t.net_weight = #{netWeight},
                </if>
                <if test="specification != null">
                    t.specification = #{specification},
                </if>
                <if test="goodsName != null">
                    t.goods_name = #{goodsName},
                </if>
                <if test="salesVolume3d != null">
                    t.sales_volume_3d = #{salesVolume3d},
                </if>
                <if test="monthsaleGmv != null">
                    t.monthsale_gmv = #{monthsaleGmv},
                </if>
                <if test="categoryLevel2 != null">
                    t.category_level2 = #{categoryLevel2},
                </if>
                <if test="monthsaleGmv3dAgo != null">
                    t.monthsale_gmv_3d_ago = #{monthsaleGmv3dAgo},
                </if>
                <if test="topMatchedXianmuSkuList != null">
                    t.top_matched_xianmu_sku_list = #{topMatchedXianmuSkuList},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="ds != null">
                    t.ds = #{ds},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="appPopBiaoguoTopSaleSkuResultMap" >
        SELECT <include refid="appPopBiaoguoTopSaleSkuColumns" />
        FROM app_pop_biaoguo_top_sale_sku t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity" >
        SELECT
            t.id id,
            t.category_name categoryName,
            t.competitor competitor,
            t.sku_code skuCode,
            t.spider_fetch_time spiderFetchTime,
            t.final_standard_price finalStandardPrice,
            t.month_sale monthSale,
            t.url url,
            t.gross_weight grossWeight,
            t.net_weight netWeight,
            t.specification specification,
            t.goods_name goodsName,
            t.sales_volume_3d salesVolume3d,
            t.monthsale_gmv monthsaleGmv,
            t.category_level2 categoryLevel2,
            t.monthsale_gmv_3d_ago monthsaleGmv3dAgo,
            t.top_matched_xianmu_sku_list topMatchedXianmuSkuList,
            t.create_time createTime,
            t.ds ds
        FROM app_pop_biaoguo_top_sale_sku t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam" resultMap="appPopBiaoguoTopSaleSkuResultMap" >
        SELECT <include refid="appPopBiaoguoTopSaleSkuColumns" />
        FROM app_pop_biaoguo_top_sale_sku t
        <include refid="whereColumnBySelect"></include>
        order by t.month_sale desc limit 200
    </select>

	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoTopSaleSku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO app_pop_biaoguo_top_sale_sku
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="categoryName != null">
				  category_name,
              </if>
              <if test="competitor != null">
				  competitor,
              </if>
              <if test="skuCode != null">
				  sku_code,
              </if>
              <if test="spiderFetchTime != null">
				  spider_fetch_time,
              </if>
              <if test="finalStandardPrice != null">
				  final_standard_price,
              </if>
              <if test="monthSale != null">
				  month_sale,
              </if>
              <if test="url != null">
				  url,
              </if>
              <if test="grossWeight != null">
				  gross_weight,
              </if>
              <if test="netWeight != null">
				  net_weight,
              </if>
              <if test="specification != null">
				  specification,
              </if>
              <if test="goodsName != null">
				  goods_name,
              </if>
              <if test="salesVolume3d != null">
				  sales_volume_3d,
              </if>
              <if test="monthsaleGmv != null">
				  monthsale_gmv,
              </if>
              <if test="categoryLevel2 != null">
				  category_level2,
              </if>
              <if test="monthsaleGmv3dAgo != null">
				  monthsale_gmv_3d_ago,
              </if>
              <if test="topMatchedXianmuSkuList != null">
				  top_matched_xianmu_sku_list,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="ds != null">
				  ds,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="categoryName != null">
				#{categoryName,jdbcType=VARCHAR},
              </if>
              <if test="competitor != null">
				#{competitor,jdbcType=VARCHAR},
              </if>
              <if test="skuCode != null">
				#{skuCode,jdbcType=VARCHAR},
              </if>
              <if test="spiderFetchTime != null">
				#{spiderFetchTime,jdbcType=VARCHAR},
              </if>
              <if test="finalStandardPrice != null">
				#{finalStandardPrice,jdbcType=DOUBLE},
              </if>
              <if test="monthSale != null">
				#{monthSale,jdbcType=NUMERIC},
              </if>
              <if test="url != null">
				#{url,jdbcType=VARCHAR},
              </if>
              <if test="grossWeight != null">
				#{grossWeight,jdbcType=VARCHAR},
              </if>
              <if test="netWeight != null">
				#{netWeight,jdbcType=VARCHAR},
              </if>
              <if test="specification != null">
				#{specification,jdbcType=VARCHAR},
              </if>
              <if test="goodsName != null">
				#{goodsName,jdbcType=VARCHAR},
              </if>
              <if test="salesVolume3d != null">
				#{salesVolume3d,jdbcType=NUMERIC},
              </if>
              <if test="monthsaleGmv != null">
				#{monthsaleGmv,jdbcType=DOUBLE},
              </if>
              <if test="categoryLevel2 != null">
				#{categoryLevel2,jdbcType=VARCHAR},
              </if>
              <if test="monthsaleGmv3dAgo != null">
				#{monthsaleGmv3dAgo,jdbcType=DOUBLE},
              </if>
              <if test="topMatchedXianmuSkuList != null">
				#{topMatchedXianmuSkuList,jdbcType=LONGVARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=VARCHAR},
              </if>
              <if test="ds != null">
				#{ds,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoTopSaleSku" >
        UPDATE app_pop_biaoguo_top_sale_sku t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>

	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoTopSaleSku" >
        DELETE FROM app_pop_biaoguo_top_sale_sku
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <select id="selectCategory2List"
            resultType="net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity">
        select DISTINCT(category2) as category2
        FROM
            `app_pop_biaoguo_top_sale_sku` t
        <include refid="whereColumnBySelect"></include>
    </select>
</mapper>