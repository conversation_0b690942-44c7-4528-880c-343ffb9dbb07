<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.offlinemapper.AppPopTopMatchedCompetitorSkuListMapper">
    <!-- 结果集映射 -->
    <resultMap id="appPopTopMatchedCompetitorSkuListResultMap" type="net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
		<result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
		<result column="top_matched_competitor_sku_list" property="topMatchedCompetitorSkuList" jdbcType="LONGVARCHAR"/>
		<result column="category" property="category" jdbcType="VARCHAR"/>
		<result column="gmv" property="gmv" jdbcType="DECIMAL"/>
		<result column="order_cnt" property="orderCnt" jdbcType="NUMERIC"/>
		<result column="order_quantity" property="orderQuantity" jdbcType="NUMERIC"/>
		<result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
		<result column="create_time" property="createTime" jdbcType="VARCHAR"/>
		<result column="ds" property="ds" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="appPopTopMatchedCompetitorSkuListColumns">
          t.id,
          t.sku,
          t.weight,
          t.sku_name,
          t.pd_name,
          t.top_matched_competitor_sku_list,
          t.category,
          t.gmv,
          t.order_cnt,
          t.order_quantity,
          t.last_order_time,
          t.create_time,
          t.ds
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="weight != null and weight !=''">
                AND t.weight = #{weight}
            </if>
			<if test="skuName != null and skuName !=''">
                AND t.sku_name = #{skuName}
            </if>
			<if test="pdName != null and pdName !=''">
                AND t.pd_name = #{pdName}
            </if>
			<if test="topMatchedCompetitorSkuList != null and topMatchedCompetitorSkuList !=''">
                AND t.top_matched_competitor_sku_list = #{topMatchedCompetitorSkuList}
            </if>
			<if test="category != null and category !=''">
                AND t.category = #{category}
            </if>
			<if test="gmv != null">
                AND t.gmv = #{gmv}
            </if>
			<if test="orderCnt != null">
                AND t.order_cnt = #{orderCnt}
            </if>
			<if test="orderQuantity != null">
                AND t.order_quantity = #{orderQuantity}
            </if>
			<if test="lastOrderTime != null">
                AND t.last_order_time = #{lastOrderTime}
            </if>
			<if test="createTime != null and createTime !=''">
                AND t.create_time = #{createTime}
            </if>
			<if test="ds != null and ds !=''">
                AND t.ds = #{ds}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="weight != null">
                    t.weight = #{weight},
                </if>
                <if test="skuName != null">
                    t.sku_name = #{skuName},
                </if>
                <if test="pdName != null">
                    t.pd_name = #{pdName},
                </if>
                <if test="topMatchedCompetitorSkuList != null">
                    t.top_matched_competitor_sku_list = #{topMatchedCompetitorSkuList},
                </if>
                <if test="category != null">
                    t.category = #{category},
                </if>
                <if test="gmv != null">
                    t.gmv = #{gmv},
                </if>
                <if test="orderCnt != null">
                    t.order_cnt = #{orderCnt},
                </if>
                <if test="orderQuantity != null">
                    t.order_quantity = #{orderQuantity},
                </if>
                <if test="lastOrderTime != null">
                    t.last_order_time = #{lastOrderTime},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="ds != null">
                    t.ds = #{ds},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="appPopTopMatchedCompetitorSkuListResultMap" >
        SELECT <include refid="appPopTopMatchedCompetitorSkuListColumns" />
        FROM app_pop_top_matched_competitor_sku_list t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity" >
        SELECT
            t.id id,
            t.sku sku,
            t.weight weight,
            t.sku_name skuName,
            t.pd_name pdName,
            t.top_matched_competitor_sku_list topMatchedCompetitorSkuList,
            t.category category,
            t.gmv gmv,
            t.order_cnt orderCnt,
            t.order_quantity orderQuantity,
            t.last_order_time lastOrderTime,
            t.create_time createTime,
            t.ds ds
        FROM app_pop_top_matched_competitor_sku_list t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam" resultMap="appPopTopMatchedCompetitorSkuListResultMap" >
        SELECT <include refid="appPopTopMatchedCompetitorSkuListColumns" />
        FROM app_pop_top_matched_competitor_sku_list t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO app_pop_top_matched_competitor_sku_list
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="weight != null">
				  weight,
              </if>
              <if test="skuName != null">
				  sku_name,
              </if>
              <if test="pdName != null">
				  pd_name,
              </if>
              <if test="topMatchedCompetitorSkuList != null">
				  top_matched_competitor_sku_list,
              </if>
              <if test="category != null">
				  category,
              </if>
              <if test="gmv != null">
				  gmv,
              </if>
              <if test="orderCnt != null">
				  order_cnt,
              </if>
              <if test="orderQuantity != null">
				  order_quantity,
              </if>
              <if test="lastOrderTime != null">
				  last_order_time,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="ds != null">
				  ds,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="weight != null">
				#{weight,jdbcType=VARCHAR},
              </if>
              <if test="skuName != null">
				#{skuName,jdbcType=VARCHAR},
              </if>
              <if test="pdName != null">
				#{pdName,jdbcType=VARCHAR},
              </if>
              <if test="topMatchedCompetitorSkuList != null">
				#{topMatchedCompetitorSkuList,jdbcType=LONGVARCHAR},
              </if>
              <if test="category != null">
				#{category,jdbcType=VARCHAR},
              </if>
              <if test="gmv != null">
				#{gmv,jdbcType=DECIMAL},
              </if>
              <if test="orderCnt != null">
				#{orderCnt,jdbcType=NUMERIC},
              </if>
              <if test="orderQuantity != null">
				#{orderQuantity,jdbcType=NUMERIC},
              </if>
              <if test="lastOrderTime != null">
				#{lastOrderTime,jdbcType=TIMESTAMP},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=VARCHAR},
              </if>
              <if test="ds != null">
				#{ds,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList" >
        UPDATE app_pop_top_matched_competitor_sku_list t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList" >
        DELETE FROM app_pop_top_matched_competitor_sku_list
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>