<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.offlinemapper.AppPopBiaoguoProductsDfMapper">
    <!-- 结果集映射 -->
    <resultMap id="appPopBiaoguoProductsDfResultMap" type="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
		<result column="category1" property="category1" jdbcType="VARCHAR"/>
		<result column="category2" property="category2" jdbcType="VARCHAR"/>
		<result column="category3" property="category3" jdbcType="VARCHAR"/>
		<result column="back_category_name" property="backCategoryName" jdbcType="VARCHAR"/>
		<result column="competitor" property="competitor" jdbcType="VARCHAR"/>
		<result column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
		<result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
		<result column="baby_name" property="babyName" jdbcType="VARCHAR"/>
		<result column="standard_price" property="standardPrice" jdbcType="VARCHAR"/>
		<result column="final_standard_price" property="finalStandardPrice" jdbcType="VARCHAR"/>
		<result column="last_time_standard_price" property="lastTimeStandardPrice" jdbcType="VARCHAR"/>
		<result column="final_unit_price_catty" property="finalUnitPriceCatty" jdbcType="VARCHAR"/>
		<result column="unit_price_catty" property="unitPriceCatty" jdbcType="VARCHAR"/>
		<result column="goods_type" property="goodsType" jdbcType="VARCHAR"/>
		<result column="specification" property="specification" jdbcType="VARCHAR"/>
		<result column="unit" property="unit" jdbcType="VARCHAR"/>
		<result column="gross_weight" property="grossWeight" jdbcType="VARCHAR"/>
		<result column="net_weight" property="netWeight" jdbcType="VARCHAR"/>
		<result column="month_sale" property="monthSale" jdbcType="VARCHAR"/>
		<result column="goods_siphon_commission_rate" property="goodsSiphonCommissionRate" jdbcType="VARCHAR"/>
		<result column="seller_siphon_commission_rate" property="sellerSiphonCommissionRate" jdbcType="VARCHAR"/>
		<result column="seller_name" property="sellerName" jdbcType="VARCHAR"/>
		<result column="goods_prop_detail_list" property="goodsPropDetailList" jdbcType="VARCHAR"/>
		<result column="url" property="url" jdbcType="VARCHAR"/>
		<result column="seven_day_after_sale" property="sevenDayAfterSale" jdbcType="VARCHAR"/>
		<result column="spider_fetch_time" property="spiderFetchTime" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="VARCHAR"/>
		<result column="ds" property="ds" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="appPopBiaoguoProductsDfColumns">
          t.id,
          t.category_name,
          t.category1,
          t.category2,
          t.category3,
          t.back_category_name,
          t.competitor,
          t.sku_code,
          t.goods_name,
          t.baby_name,
          t.standard_price,
          t.final_standard_price,
          t.last_time_standard_price,
          t.final_unit_price_catty,
          t.unit_price_catty,
          t.goods_type,
          t.specification,
          t.unit,
          t.gross_weight,
          t.net_weight,
          t.month_sale,
          t.goods_siphon_commission_rate,
          t.seller_siphon_commission_rate,
          t.seller_name,
          t.goods_prop_detail_list,
          t.url,
          t.seven_day_after_sale,
          t.spider_fetch_time,
          t.create_time,
          t.ds
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="categoryName != null and categoryName !=''">
                AND t.category_name = #{categoryName}
            </if>
			<if test="category1 != null and category1 !=''">
                AND t.category1 = #{category1}
            </if>
			<if test="category2 != null and category2 !=''">
                AND t.category2 = #{category2}
            </if>
			<if test="category3 != null and category3 !=''">
                AND t.category3 = #{category3}
            </if>
			<if test="backCategoryName != null and backCategoryName !=''">
                AND t.back_category_name = #{backCategoryName}
            </if>
			<if test="competitor != null and competitor !=''">
                AND t.competitor = #{competitor}
            </if>
			<if test="skuCode != null and skuCode !=''">
                AND t.sku_code = #{skuCode}
            </if>
            <if test="skuCodeList != null and skuCodeList.size > 0">
                AND  t.sku_code in
                <foreach collection="skuCodeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="goodsName != null and goodsName !=''">
                AND t.goods_name like CONCAT('%', #{goodsName} ,'%')
            </if>
			<if test="babyName != null and babyName !=''">
                AND t.baby_name = #{babyName}
            </if>
			<if test="standardPrice != null and standardPrice !=''">
                AND t.standard_price = #{standardPrice}
            </if>
			<if test="finalStandardPrice != null and finalStandardPrice !=''">
                AND t.final_standard_price = #{finalStandardPrice}
            </if>
			<if test="lastTimeStandardPrice != null and lastTimeStandardPrice !=''">
                AND t.last_time_standard_price = #{lastTimeStandardPrice}
            </if>
			<if test="finalUnitPriceCatty != null and finalUnitPriceCatty !=''">
                AND t.final_unit_price_catty = #{finalUnitPriceCatty}
            </if>
			<if test="unitPriceCatty != null and unitPriceCatty !=''">
                AND t.unit_price_catty = #{unitPriceCatty}
            </if>
			<if test="goodsType != null and goodsType !=''">
                AND t.goods_type = #{goodsType}
            </if>
			<if test="specification != null and specification !=''">
                AND t.specification = #{specification}
            </if>
			<if test="unit != null and unit !=''">
                AND t.unit = #{unit}
            </if>
			<if test="grossWeight != null and grossWeight !=''">
                AND t.gross_weight = #{grossWeight}
            </if>
			<if test="netWeight != null and netWeight !=''">
                AND t.net_weight = #{netWeight}
            </if>
			<if test="monthSale != null and monthSale !=''">
                AND t.month_sale = #{monthSale}
            </if>
			<if test="goodsSiphonCommissionRate != null and goodsSiphonCommissionRate !=''">
                AND t.goods_siphon_commission_rate = #{goodsSiphonCommissionRate}
            </if>
			<if test="sellerSiphonCommissionRate != null and sellerSiphonCommissionRate !=''">
                AND t.seller_siphon_commission_rate = #{sellerSiphonCommissionRate}
            </if>
			<if test="sellerName != null and sellerName !=''">
                AND t.seller_name = #{sellerName}
            </if>
			<if test="goodsPropDetailList != null and goodsPropDetailList !=''">
                AND t.goods_prop_detail_list = #{goodsPropDetailList}
            </if>
			<if test="url != null and url !=''">
                AND t.url = #{url}
            </if>
			<if test="sevenDayAfterSale != null and sevenDayAfterSale !=''">
                AND t.seven_day_after_sale = #{sevenDayAfterSale}
            </if>
			<if test="spiderFetchTime != null and spiderFetchTime !=''">
                AND t.spider_fetch_time = #{spiderFetchTime}
            </if>
			<if test="createTime != null and createTime !=''">
                AND t.create_time = #{createTime}
            </if>
			<if test="ds != null and ds !=''">
                AND t.ds = #{ds}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="categoryName != null">
                    t.category_name = #{categoryName},
                </if>
                <if test="category1 != null">
                    t.category1 = #{category1},
                </if>
                <if test="category2 != null">
                    t.category2 = #{category2},
                </if>
                <if test="category3 != null">
                    t.category3 = #{category3},
                </if>
                <if test="backCategoryName != null">
                    t.back_category_name = #{backCategoryName},
                </if>
                <if test="competitor != null">
                    t.competitor = #{competitor},
                </if>
                <if test="skuCode != null">
                    t.sku_code = #{skuCode},
                </if>
                <if test="goodsName != null">
                    t.goods_name = #{goodsName},
                </if>
                <if test="babyName != null">
                    t.baby_name = #{babyName},
                </if>
                <if test="standardPrice != null">
                    t.standard_price = #{standardPrice},
                </if>
                <if test="finalStandardPrice != null">
                    t.final_standard_price = #{finalStandardPrice},
                </if>
                <if test="lastTimeStandardPrice != null">
                    t.last_time_standard_price = #{lastTimeStandardPrice},
                </if>
                <if test="finalUnitPriceCatty != null">
                    t.final_unit_price_catty = #{finalUnitPriceCatty},
                </if>
                <if test="unitPriceCatty != null">
                    t.unit_price_catty = #{unitPriceCatty},
                </if>
                <if test="goodsType != null">
                    t.goods_type = #{goodsType},
                </if>
                <if test="specification != null">
                    t.specification = #{specification},
                </if>
                <if test="unit != null">
                    t.unit = #{unit},
                </if>
                <if test="grossWeight != null">
                    t.gross_weight = #{grossWeight},
                </if>
                <if test="netWeight != null">
                    t.net_weight = #{netWeight},
                </if>
                <if test="monthSale != null">
                    t.month_sale = #{monthSale},
                </if>
                <if test="goodsSiphonCommissionRate != null">
                    t.goods_siphon_commission_rate = #{goodsSiphonCommissionRate},
                </if>
                <if test="sellerSiphonCommissionRate != null">
                    t.seller_siphon_commission_rate = #{sellerSiphonCommissionRate},
                </if>
                <if test="sellerName != null">
                    t.seller_name = #{sellerName},
                </if>
                <if test="goodsPropDetailList != null">
                    t.goods_prop_detail_list = #{goodsPropDetailList},
                </if>
                <if test="url != null">
                    t.url = #{url},
                </if>
                <if test="sevenDayAfterSale != null">
                    t.seven_day_after_sale = #{sevenDayAfterSale},
                </if>
                <if test="spiderFetchTime != null">
                    t.spider_fetch_time = #{spiderFetchTime},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="ds != null">
                    t.ds = #{ds},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="appPopBiaoguoProductsDfResultMap" >
        SELECT <include refid="appPopBiaoguoProductsDfColumns" />
        FROM app_pop_biaoguo_products_df t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity" >
        SELECT
            t.id id,
            t.category_name categoryName,
            t.category1 category1,
            t.category2 category2,
            t.category3 category3,
            t.back_category_name backCategoryName,
            t.competitor competitor,
            t.sku_code skuCode,
            t.goods_name goodsName,
            t.baby_name babyName,
            t.standard_price standardPrice,
            t.final_standard_price finalStandardPrice,
            t.last_time_standard_price lastTimeStandardPrice,
            t.final_unit_price_catty finalUnitPriceCatty,
            t.unit_price_catty unitPriceCatty,
            t.goods_type goodsType,
            t.specification specification,
            t.unit unit,
            t.gross_weight grossWeight,
            t.net_weight netWeight,
            t.month_sale monthSale,
            t.goods_siphon_commission_rate goodsSiphonCommissionRate,
            t.seller_siphon_commission_rate sellerSiphonCommissionRate,
            t.seller_name sellerName,
            t.goods_prop_detail_list goodsPropDetailList,
            t.url url,
            t.seven_day_after_sale sevenDayAfterSale,
            t.spider_fetch_time spiderFetchTime,
            t.create_time createTime,
            t.ds ds
        FROM app_pop_biaoguo_products_df t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam" resultMap="appPopBiaoguoProductsDfResultMap" >
        SELECT <include refid="appPopBiaoguoProductsDfColumns" />
        FROM app_pop_biaoguo_products_df t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO app_pop_biaoguo_products_df
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="categoryName != null">
				  category_name,
              </if>
              <if test="category1 != null">
				  category1,
              </if>
              <if test="category2 != null">
				  category2,
              </if>
              <if test="category3 != null">
				  category3,
              </if>
              <if test="backCategoryName != null">
				  back_category_name,
              </if>
              <if test="competitor != null">
				  competitor,
              </if>
              <if test="skuCode != null">
				  sku_code,
              </if>
              <if test="goodsName != null">
				  goods_name,
              </if>
              <if test="babyName != null">
				  baby_name,
              </if>
              <if test="standardPrice != null">
				  standard_price,
              </if>
              <if test="finalStandardPrice != null">
				  final_standard_price,
              </if>
              <if test="lastTimeStandardPrice != null">
				  last_time_standard_price,
              </if>
              <if test="finalUnitPriceCatty != null">
				  final_unit_price_catty,
              </if>
              <if test="unitPriceCatty != null">
				  unit_price_catty,
              </if>
              <if test="goodsType != null">
				  goods_type,
              </if>
              <if test="specification != null">
				  specification,
              </if>
              <if test="unit != null">
				  unit,
              </if>
              <if test="grossWeight != null">
				  gross_weight,
              </if>
              <if test="netWeight != null">
				  net_weight,
              </if>
              <if test="monthSale != null">
				  month_sale,
              </if>
              <if test="goodsSiphonCommissionRate != null">
				  goods_siphon_commission_rate,
              </if>
              <if test="sellerSiphonCommissionRate != null">
				  seller_siphon_commission_rate,
              </if>
              <if test="sellerName != null">
				  seller_name,
              </if>
              <if test="goodsPropDetailList != null">
				  goods_prop_detail_list,
              </if>
              <if test="url != null">
				  url,
              </if>
              <if test="sevenDayAfterSale != null">
				  seven_day_after_sale,
              </if>
              <if test="spiderFetchTime != null">
				  spider_fetch_time,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="ds != null">
				  ds,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="categoryName != null">
				#{categoryName,jdbcType=VARCHAR},
              </if>
              <if test="category1 != null">
				#{category1,jdbcType=VARCHAR},
              </if>
              <if test="category2 != null">
				#{category2,jdbcType=VARCHAR},
              </if>
              <if test="category3 != null">
				#{category3,jdbcType=VARCHAR},
              </if>
              <if test="backCategoryName != null">
				#{backCategoryName,jdbcType=VARCHAR},
              </if>
              <if test="competitor != null">
				#{competitor,jdbcType=VARCHAR},
              </if>
              <if test="skuCode != null">
				#{skuCode,jdbcType=VARCHAR},
              </if>
              <if test="goodsName != null">
				#{goodsName,jdbcType=VARCHAR},
              </if>
              <if test="babyName != null">
				#{babyName,jdbcType=VARCHAR},
              </if>
              <if test="standardPrice != null">
				#{standardPrice,jdbcType=VARCHAR},
              </if>
              <if test="finalStandardPrice != null">
				#{finalStandardPrice,jdbcType=VARCHAR},
              </if>
              <if test="lastTimeStandardPrice != null">
				#{lastTimeStandardPrice,jdbcType=VARCHAR},
              </if>
              <if test="finalUnitPriceCatty != null">
				#{finalUnitPriceCatty,jdbcType=VARCHAR},
              </if>
              <if test="unitPriceCatty != null">
				#{unitPriceCatty,jdbcType=VARCHAR},
              </if>
              <if test="goodsType != null">
				#{goodsType,jdbcType=VARCHAR},
              </if>
              <if test="specification != null">
				#{specification,jdbcType=VARCHAR},
              </if>
              <if test="unit != null">
				#{unit,jdbcType=VARCHAR},
              </if>
              <if test="grossWeight != null">
				#{grossWeight,jdbcType=VARCHAR},
              </if>
              <if test="netWeight != null">
				#{netWeight,jdbcType=VARCHAR},
              </if>
              <if test="monthSale != null">
				#{monthSale,jdbcType=VARCHAR},
              </if>
              <if test="goodsSiphonCommissionRate != null">
				#{goodsSiphonCommissionRate,jdbcType=VARCHAR},
              </if>
              <if test="sellerSiphonCommissionRate != null">
				#{sellerSiphonCommissionRate,jdbcType=VARCHAR},
              </if>
              <if test="sellerName != null">
				#{sellerName,jdbcType=VARCHAR},
              </if>
              <if test="goodsPropDetailList != null">
				#{goodsPropDetailList,jdbcType=VARCHAR},
              </if>
              <if test="url != null">
				#{url,jdbcType=VARCHAR},
              </if>
              <if test="sevenDayAfterSale != null">
				#{sevenDayAfterSale,jdbcType=VARCHAR},
              </if>
              <if test="spiderFetchTime != null">
				#{spiderFetchTime,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=VARCHAR},
              </if>
              <if test="ds != null">
				#{ds,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf" >
        UPDATE app_pop_biaoguo_products_df t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf" >
        DELETE FROM app_pop_biaoguo_products_df
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <select id="exist" resultType="int">
        SELECT EXISTS (
            SELECT 1
            FROM app_pop_biaoguo_products_df t
            <include refid="whereColumnBySelect"></include>
        )
    </select>

    <select id="count" resultType="int">
        SELECT count(1)
        FROM app_pop_biaoguo_products_df t
        <include refid="whereColumnBySelect"></include>
    </select>
</mapper>