#!/bin/bash

# Fetch the latest changes from the remote repository
git fetch origin

# Get the list of changed files
changedFiles=$(git diff --name-only origin/master...HEAD)

# Convert the list to a comma-separated string
inclusions=$(echo "$changedFiles" | grep -v "Test*.java" | grep ".java" | tr '\n' ',' | sed 's/,$//')

echo -e "about to sonar scan for these files:\n$inclusions"

# Check if the user wants to recompile the source code
recompile=""
if [[ "$1" == "compile" ]]; then
  echo "do recompile first..."
  recompile="clean compile"
fi
# Run the SonarQube analysis with the specified plugin version, recompiling source code
mvn $recompile org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar -Dsonar.inclusions="$inclusions" \
  -Dsonar.exclusions="**/Test*.java,**/IT*.java,**/Test*.class,**/IT*.class"