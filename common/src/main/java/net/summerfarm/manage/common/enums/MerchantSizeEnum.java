package net.summerfarm.manage.common.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum MerchantSizeEnum {

    SINGLE_SHOP(0,"单店"),
    MAJOR_CUSTOMER(1,"大客户")
    ;
    private Integer code;

    private String value;

    MerchantSizeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MerchantSizeEnum c : MerchantSizeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
