package net.summerfarm.manage.common.input.wx;

import lombok.Data;

import java.util.Map;

/**
 * @ClassName WxTemplate
 * @Description
 * <AUTHOR>
 * @Date 13:45 2024/2/20
 * @Version 1.0
 **/
@Data
public class WxTemplate {

    private Map<String,String> miniProgram ;

    /**
     * 模板消息id
     */
    private String template_id;

    /**
     * 用户openId
     */
    private String touser;

    /**
     * URL置空，则在发送后，点击模板消息会进入一个空白页面（ios），或无法点击（android）
     */
    private String url;

    /**
     * 标题颜色
     */
    private String topcolor;

    /**
     * 详细内容
     */
    private Map<String, TemplateData> data;
}
