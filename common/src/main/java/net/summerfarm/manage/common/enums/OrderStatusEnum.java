package net.summerfarm.manage.common.enums;

/**
 * @Package: net.summerfarm.common.enums
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/11
 */
public enum OrderStatusEnum {

    NO_PAYMENT(1,"待支付"),
    WAIT_DELIVERY(2,"待配送"),
    DELIVERING(3,"待收货"),
    RECEIVED(6,"已收货"),
    APPLY_FOR_DRAWBACK(7,"申请退款订单"),
    DRAWBACK(8,"已退款订单"),
    PAY_FAILED(9,"支付失败订单"),
    CLOSED(10,"支付中断超时关闭订单"),
    CANCEL(11,"已撤销订单"),
//    NO_PAYMENT_REMAIN(12,"待支付尾款"),
//    CANCEL_REMAIN(13,"尾款支付超时,订单关闭"),
    MANUAL_CLOSED(14, "手动关闭订单"),
    MANUAL_HANDLE(15,"人工退款中");


    private int id;

    private String statusName;

    OrderStatusEnum(int id, String statusName) {
        this.id = id;
        this.statusName = statusName;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public static OrderStatusEnum getEnum(int id){
        for (OrderStatusEnum enums : OrderStatusEnum.values()){
            if (enums.getId() == id){
                return enums;
            }
        }
        return null;
    }
}
