package net.summerfarm.manage.common.enums.order;

/**
 * @Description: 订单优惠明细类型枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum OrderItemPreferentialTypeEnum {
    /**
     * 订单优惠明细枚举
     */
    FULL_RETURN(20,"满返")
    ;
    private Integer code;

    private String value;

    OrderItemPreferentialTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (OrderItemPreferentialTypeEnum c : OrderItemPreferentialTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
