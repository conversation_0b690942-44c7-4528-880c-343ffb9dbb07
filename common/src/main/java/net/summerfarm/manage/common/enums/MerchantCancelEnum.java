package net.summerfarm.manage.common.enums;

/**
 * @Description:  门店注销申请记录状态
 * @Author: lzh
 * @Time: 2023/4/20 0010 09:59
 * @ModifyBy:
 */

public enum MerchantCancelEnum {

    TO_BE_CANCELLED(1,"待注销"),
    CLOSED(2,"已关闭"),
    CANCELLED(3,"已注销")
    ;
    private Integer code;

    private String value;

    MerchantCancelEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MerchantCancelEnum c : MerchantCancelEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
