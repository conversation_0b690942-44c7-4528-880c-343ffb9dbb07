package net.summerfarm.manage.common.enums;

/**
 * @Package: net.summerfarm.common.enums
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/7/4
 */
public enum AfterSaleOrderStatusEnum {

    WAIT_HANDLE(0,"审核中"),
    IN_HAND(1,"处理中"),
    SUCCESS(2,"成功"),
    FAIL(3,"失败"),
    RE_COMMIT(4,"补充凭证"),
    CANCEL(11,"取消"),
    IN_PAYMENT(12,"退款中");

    private int status;

    private String state;

    AfterSaleOrderStatusEnum(int status, String state) {
        this.status = status;
        this.state = state;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
