package net.summerfarm.manage.common.enums.download;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 20:59
 */
@Getter
@AllArgsConstructor
public enum FileDownloadTypeEnum {


    // 报价单导出
    XIANMU_MAJOR_PRICE_DOWNLOAD(110, "鲜沐大客户报价单导出"),

    XIANMU_MAJOR_PRICE_BY_AREA_IMPORT(88, "鲜沐大客户报价单按运营区域导入"),

    ;

    /**
     * type
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
}
