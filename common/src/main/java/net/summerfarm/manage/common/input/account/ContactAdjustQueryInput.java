package net.summerfarm.manage.common.input.account;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.util.Date;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
@Data
public class ContactAdjustQueryInput extends BasePageInput implements Serializable{
	/**
	 * 
	 */
	private Long id;

	/**
	 * 添加时间
	 */
	private Date addTime;

	
	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 用户id
	 */
	private Integer mId;

	/**
	 * 地址表id
	 */
	private Integer contactId;

	/**
	 * 新poi
	 */
	private String newPoi;

	/**
	 * 状态 0 待审核, 1 审核通过 ,2 拒绝重新交, 3 审核失败
	 */
	private Integer status;

	/**
	 * 省
	 */
	private String newProvince;

	/**
	 * 市
	 */
	private String newCity;

	/**
	 * 区域
	 */
	private String newArea;

	/**
	 * 详细地址
	 */
	private String newAddress;

	/**
	 * 门牌号
	 */
	private String newHouseNumber;



}