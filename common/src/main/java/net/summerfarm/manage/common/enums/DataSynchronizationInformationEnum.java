package net.summerfarm.manage.common.enums;

/**
 * 离线数据库信息同步表:表名枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/18 10:22
 */
public enum DataSynchronizationInformationEnum {

    CRM_BD_DAY_GMV("crm_bd_day_gmv"),
    CRM_BD_MONTH_GMV("crm_bd_month_gmv"),
    CRM_MERCHANT_DAY_GMV("crm_merchant_day_gmv"),
    CRM_MERCHANT_MONTH_GMV("crm_merchant_month_gmv"),
    WAREHOUSE_ESTIMATED_CONSUMPTION("warehouse_estimated_consumption"),
    WAREHOUSE_PATH_TIME("warehouse_path_time"),
    STOCK_DASHBOARD_HISTORY("stock_dashboard_history"),
    STOCK_DASHBOARD_FUTURE("stock_dashboard_future"),
    CRM_MERCHANT_DAY_LABEL("crm_merchant_day_label"),
    CRM_BD_BIG_CUST_MONTH_GMV("crm_bd_big_cust_month_gmv"),
    CRM_MERCHANT_DAY_ATTRIBUTE("crm_merchant_day_attribute"),
    TEMPORARY_INSTANCE_RISK("temporary_insurance_risk"),
    SKU_SALES_VOLUME("sku_sales_volume"),
    ;

    private String tableName;

    DataSynchronizationInformationEnum(String tableName){
        this.tableName = tableName;
    }

    DataSynchronizationInformationEnum() {

    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
