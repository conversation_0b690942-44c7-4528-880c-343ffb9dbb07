package net.summerfarm.manage.common.input.invoiceConfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InvoiceConfigVO implements Serializable {
    private static final long serialVersionUID = 5038623509015185178L;


    /**
     * 0:门店自有抬头；1:大客户门店下的抬头
     */
    private Integer type;


    /**
     * 门店ids
     */
    private List<Long> mIds;


    /**
     *  0:生效中（默认), 1:(失效)
     *
     */
    private Integer validStatus;

}
