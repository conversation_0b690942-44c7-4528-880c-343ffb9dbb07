package net.summerfarm.manage.common.valueobject;

/**
 * Created by wjd on 2017/12/21.
 */
public class MQData {
    /**
     * 方法类型,方法的唯一标识
     */
    private String type;
    /**
     * 消息内容
     */
    private Object data;

    /**
     * 执行什么业务? excel,钉钉消息,业务解耦...
     */
    private String business;

    public MQData() {
    }

    public MQData(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }
}
