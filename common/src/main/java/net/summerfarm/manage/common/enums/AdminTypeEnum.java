package net.summerfarm.manage.common.enums;

import lombok.extern.slf4j.Slf4j;

import net.xianmu.common.exception.BizException;

/**
 * admin的类型：1代表鲜沐员工，2代表大客户
 */
@Slf4j
public enum AdminTypeEnum {

    MAJOR_CUSTOMER(0, "普通大客户--SaaS客户一类"), XIANMU_EMPLOYEE(1, "鲜沐员工"), MAJOR_CUSTOMER_RESELLER(2, "批发大客户");

    private final int type;

    private final String desc;


    private AdminTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static AdminTypeEnum getByType(Integer type) {
        if (type == null) {
            log.warn("Invalid AdminTypeEnum:null");
            return null;
        }
        for (AdminTypeEnum adminTypeEnum : AdminTypeEnum.values()) {
            if (adminTypeEnum.getType() == type) {
                return adminTypeEnum;
            }
        }
        log.error("Invalid AdminTypeEnum:{}", type);
        return null;
    }
}
