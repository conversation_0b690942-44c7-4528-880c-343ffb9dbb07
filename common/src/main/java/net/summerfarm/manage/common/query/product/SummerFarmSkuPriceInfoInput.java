package net.summerfarm.manage.common.query.product;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Data
public class SummerFarmSkuPriceInfoInput implements Serializable {

    private static final long serialVersionUID = 4828435395291816349L;

    /**
     * 城市报价单主键Id
     */
    private Long citySupplyPriceId;

    /**
     * skuIds
     */
    private Long skuId;

    /**
     * 大客户Id
     */
    private Long adminId;

    /**
     * 城市
     */
    private List<String> cityName;

}
