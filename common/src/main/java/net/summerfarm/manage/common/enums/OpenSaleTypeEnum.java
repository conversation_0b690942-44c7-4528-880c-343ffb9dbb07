package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
@Getter
@AllArgsConstructor
public enum OpenSaleTypeEnum {
    TURN_ON(0,"上架"),
    STOCK_TURN_ON(1,"有库存时上架"),
    TURN_ON_TIMER(2,"定时上架"),
    STOCK_TURN_ON_FOREVER(3,"有库存定时上架-永久生效");
    ;
    private final Integer type;
    private final String operator;

    public static OpenSaleTypeEnum ofByOperator(String operator) {
        for (OpenSaleTypeEnum e: values()) {
            if (e.getOperator ().equals(operator)) {
                return e;
            }
        }
        return null;
    }
}