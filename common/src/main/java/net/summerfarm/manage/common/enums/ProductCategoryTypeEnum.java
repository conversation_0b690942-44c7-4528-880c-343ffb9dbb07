package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2024/1/29
 */
@Getter
@AllArgsConstructor
public enum ProductCategoryTypeEnum {

    /**
     * all
     */
    ALL(1,"all"),
    /**
     * 乳制品
     */
    DAIRY(2,"乳制品"),
    /**
     * 非乳制品
     */
    NOT_DAIRY(3,"非乳制品"),
    /**
     * 水果
     */
    FRUIT(4,"水果"),
    ;

    private Integer code;

    private String des;
}
