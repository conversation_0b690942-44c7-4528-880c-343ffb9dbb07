package net.summerfarm.manage.common.msg;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.common.input.wx.ActivityNotice;
import net.summerfarm.manage.common.input.wx.TemplateData;

import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2025/5/22 17:59
 * @PackageName:net.summerfarm.manage.common.msg
 * @ClassName: ActivityNoticeMsg
 * @Description: TODO
 * @Version 1.0
 */
public class ActivityNoticeMsg {

    //public static String DEV_TEMPLATE_ID = "JgOm62bwo53d7RrqggbDo-QJIGZLJxajcLpsyM1X2YY";

    public static String DEV_TEMPLATE_ID = "l5NtkQNRH3GgNVuZ9aQlquiJKXx3wYRRRK1LMziEcGA";

    public static String PRO_TEMPLATE_ID = "psUQWZKzsz7BwIkArxzfLTpj2G0xYbBNXRTWyJL2sos";

    private static final Integer MAX_LENGTH = 10;

    public static String templateMessage(ActivityNotice notice) {
        HashMap<String, TemplateData> templateData = new HashMap<>();

        TemplateData msgData = new TemplateData();
        msgData.setValue("产品预订成功通知\n");
        templateData.put("first", msgData);

        TemplateData name = new TemplateData();
        String skuName = notice.getPdName() + "" + notice.getWeight();
        if (skuName.length() > MAX_LENGTH) {
            skuName = skuName.substring(0, MAX_LENGTH - 3) + "...";
        }
        name.setValue(skuName + ",特价" + notice.getActivityPrice());
        //templateData.put("thing3", name);
        templateData.put("thing4", name);

        TemplateData time4 = new TemplateData();
        time4.setValue(StringUtils.isBlank(BaseDateUtils.localDateTimeToStringFour(notice.getActivityStartTime()))
                ? BaseDateUtils.localDateTimeToStringFour(LocalDateTime.now()) : BaseDateUtils.localDateTimeToStringFour(notice.getActivityStartTime()));
        //templateData.put("time4", time4);
        templateData.put("time1", time4);

        /*TemplateData remake = new TemplateData();
        remake.setValue("活动时间有限，赶紧去商城采购吧！");
        templateData.put("remake", remake);*/

        /*TemplateData keyword1 = new TemplateData();
        keyword1.setValue(notice.getSku());
        templateData.put("keyword1", keyword1);

        TemplateData keyword2 = new TemplateData();
        keyword2.setValue(notice.getActivityName());
        templateData.put("keyword2", keyword2);

        TemplateData keyword4 = new TemplateData();
        keyword4.setValue(notice.getPdName());
        templateData.put("keyword4", keyword4);

        TemplateData keyword5 = new TemplateData();
        keyword5.setValue(notice.getPdName());
        templateData.put("keyword5", keyword5);*/

        return JSON.toJSONString(templateData);
    }

}
