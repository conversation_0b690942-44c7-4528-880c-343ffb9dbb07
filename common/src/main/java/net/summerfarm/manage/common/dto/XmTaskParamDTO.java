package net.summerfarm.manage.common.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 15:22
 */
@Data
public class XmTaskParamDTO {

    /**
     * 起始id
     */
    private Long startId;

    /**
     * 结束id
     */
    private Long endId;

    /**
     * 单个批次的处理量
     */
    private Integer offset;

    /**
     * 并发数
     */
    private Integer threadNum;

    /**
     * 日期 =离线任务执行日期减1天
     */
    private String dateTag;

    /**
     * 每个批次的睡眠时间
     */
    private Integer sleep;

    /**
     * 刷新用户当月等级标记（用于手动刷数据）
     */
    private boolean updateMerchantCurrentGradeFlag;
}
