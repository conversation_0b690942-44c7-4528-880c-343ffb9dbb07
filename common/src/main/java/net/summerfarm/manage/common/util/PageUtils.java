package net.summerfarm.manage.common.util;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;


import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2022/8/1 11:28
 * @Version 1.0
 */
public class PageUtils {

    public static Integer DEFAULT_PAGE_SIZE = 200;
    public static Integer DEFAULT_PAGE_NO = 1;

    /**
     * 手动分页类
     *
     * @param dataList
     * @param pageSize
     * @param pageNo
     * @param <T>
     * @return
     */
    public static <T> List<T> getPageSizeDataForRelations(List<T> dataList, int pageSize, int pageNo) {
        //起始截取数据位置
        int startNum = (pageNo - 1) * pageSize + 1;
        if (startNum > dataList.size()) {
            return new ArrayList<T>();
        }
        List<T> res = new ArrayList<>();
        int rum = dataList.size() - startNum;
        if (rum < 0) {
            new ArrayList<T>();
        }
        //说明正好是最后一个了
        if (rum == 0) {
            int index = dataList.size() - 1;
            res.add(dataList.get(index));
            return res;
        }
        //剩下的数据还够1页，返回整页的数据
        if (rum / pageSize >= 1) {
            //截取从startNum开始的数据
            for (int i = startNum; i < startNum + pageSize; i++) {
                res.add(dataList.get(i - 1));
            }
            return res;
            //不够一页，直接返回剩下数据
        } else if ((rum / pageSize == 0) && rum > 0) {
            for (int j = startNum; j <= dataList.size(); j++) {
                res.add(dataList.get(j - 1));
            }
            return res;
        } else {
            return new ArrayList<T>();
        }
    }


    public static <T> PageInfo<T> getPage(Integer page, Integer size, List<T> data) {
        PageInfo<T> list = new PageInfo<>();
        Integer startIndex = (page - 1) * size;
        Integer endIndex = Math.min(page * size, data.size());
        int total = data.size();
        int pages = total % size == 0 ? total / size : (total / size) + 1;
        data = data.stream()
          .skip(startIndex)
          .limit(endIndex - startIndex)
          .collect(Collectors.toList());
        list.setNavigateFirstPage(page >= 6 ? (page - 4) : 1);
        list.setNavigateLastPage(total < size ? 1 : list.getNavigateFirstPage() + 7);
        list.setTotal(total);
        int pnums[] = null;
        int navigateFirstPage = list.getNavigateFirstPage();
        if (pages > 8) {
            pnums = new int[8];
            for (int i = 0; i < 8; i++) {
                pnums[i] = navigateFirstPage;
                navigateFirstPage++;
            }
        } else {
            pnums = new int[pages];
            for (int i = 0; i < pages; i++) {
                pnums[i] = i + 1;
                list.setHasNextPage(!(page >= pages));
                list.setIsFirstPage(page == 1);
                list.setIsLastPage(page == pages);
                list.setList(data);
                list.setPageNum(page);
                list.setPageSize(size);
                list.setPages(pages);
                list.setNavigatePages(8);
                list.setNextPage(page >= pages ? 0 : page + 1);
                list.setPrePage(page - 1);
                //  list.setHasPreviousPage(!(page==1);
                list.setStartRow((page - 1) * size + 1);
                list.setEndRow(total < size ? total : page * size);
                list.setSize(data.size());
                list.setNavigatepageNums(pnums);
            }
        }
        return list;
    }

    public static <T> PageInfo<T> startPage(int pageIndex, int pageSize, Supplier<List<T>> supplier) {
        try {
            PageHelper.startPage(pageIndex, pageSize);
            List<T> resultList = supplier.get();
            return PageInfo.of(resultList);
        } finally {
            PageHelper.clearPage();
        }
    }
}
