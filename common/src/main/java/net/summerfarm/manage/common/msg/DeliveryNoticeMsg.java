package net.summerfarm.manage.common.msg;

import com.alibaba.fastjson.JSON;
import net.summerfarm.manage.common.input.wx.DeliveryNotice;
import net.summerfarm.manage.common.input.wx.TemplateData;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DeliveryNoticeMsg
 * @Description
 * <AUTHOR>
 * @Date 14:11 2024/2/20
 * @Version 1.0
 **/
public class DeliveryNoticeMsg {

    public static String TEMPLATE_ID = "EBGqHVn7YGQp1AHs04WMWZVjkBxG2M7LH3w8QZ5b-7I";

    public static String TIMING_DELIVERY_TEMPLATE_ID = "7vE9wQo0M_IWJP07MsSYrSWK4nc0Kvn2NCTOY-bjIu0";

    public static String templateMessage(DeliveryNotice notice) {
        String color = "#000000";
        String tem = "您购买的商品已打包，将尽快送达\n";
        String remarkMsg = "点击下方按钮可查看订单详情，也可查看司机配送进度。";

        HashMap<String, TemplateData> templateData = new HashMap<>();

        TemplateData msgData = new TemplateData();
        msgData.setColor(color);
        msgData.setValue(tem);
        templateData.put("first", msgData);

        TemplateData orderNoData = new TemplateData();
        orderNoData.setColor(color);
        orderNoData.setValue(notice.getOrderNo());
        templateData.put("keyword1", orderNoData);

        TemplateData timeData = new TemplateData();
        timeData.setColor(color);
        timeData.setValue(String.valueOf(notice.getDeliveryTime()));
        templateData.put("keyword2", timeData);

        TemplateData contactData = new TemplateData();
        contactData.setColor(color);
        contactData.setValue(notice.getContact());
        templateData.put("keyword3", contactData);

        TemplateData remakeData = new TemplateData();
        remakeData.setColor(color);
        remakeData.setValue(remarkMsg);
        templateData.put("remark", remakeData);

        return JSON.toJSONString(templateData);
    }

    public static String timingDeliveryTemplateMessage(DeliveryNotice notice) {
        String color = "#000000";

        Map<String, TemplateData> dataMap = new HashMap<>();
        TemplateData first = new TemplateData();
        first.setColor(color);
        first.setValue("您好，您的省心送配送计划将在后天送达！");
        dataMap.put("first", first);

        TemplateData contact = new TemplateData();
        contact.setColor(color);
        contact.setValue(notice.getContact());
        dataMap.put("keyword1", contact);

        TemplateData phone = new TemplateData();
        phone.setColor(color);
        phone.setValue(notice.getPhone());
        dataMap.put("keyword2", phone);

        TemplateData pdName = new TemplateData();
        pdName.setColor(color);
        pdName.setValue(notice.getPdName());
        dataMap.put("keyword3", pdName);

        TemplateData quantity = new TemplateData();
        quantity.setColor(color);
        quantity.setValue(notice.getQuantity().toString());
        dataMap.put("keyword4", quantity);

        TemplateData deliveryTime = new TemplateData();
        deliveryTime.setColor(color);
        deliveryTime.setValue(String.valueOf(notice.getDeliveryTime()));
        dataMap.put("keyword5", deliveryTime);

        TemplateData remark = new TemplateData();
        remark.setColor(color);
        remark.setValue("如需要修改配送计划，请您在今晚22点之前完成，点击“详情”可直达修改页面。");
        dataMap.put("remark", remark);

        return JSON.toJSONString(dataMap);
    }
}
