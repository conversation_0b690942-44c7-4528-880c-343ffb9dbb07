package net.summerfarm.manage.facade.wms;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.tenant.TenantProvider;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.inventory.CabinetInventoryProvider;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryExistQueryReq;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryExistQueryResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2025/5/6 14:13
 * @<AUTHOR>
 */
@Component
@Slf4j
public class CabinetInventoryFacade {

    @DubboReference
    private CabinetInventoryProvider cabinetInventoryProvider;

    public Map<String, String> querySkuCabinetCodeMap(Integer warehouseNo, List<String> skuList) {
        // 如果仓库号和skuList为空，则返回空
        if (warehouseNo == null || CollectionUtils.isEmpty(skuList)) {
            return new HashMap<>();
        }
        try {
            CabinetInventoryExistQueryReq queryReq = new CabinetInventoryExistQueryReq();
            queryReq.setWarehouseNo(warehouseNo);
            queryReq.setSkuList(skuList);
            DubboResponse<List<CabinetInventoryExistQueryResp>> response = cabinetInventoryProvider.queryExistCabinetInventory(queryReq);
            // 如果response为空或者response的list为空，则返回空，并且打印日志
            if (response == null || !response.isSuccess()) {
                log.error("invoke cabinetInventoryProvider.queryExistCabinetInventory fail request:{}, response:{}", JSON.toJSONString(queryReq), JSON.toJSONString(response));
                return new HashMap<>();
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                return new HashMap<>();
            }

            List<CabinetInventoryExistQueryResp> cabinetInventoryList = response.getData();
            // 将cabinetInventoryList按照sku分组，取出每个sku对应qualityDate最晚的cabinetCode，并组装成map，key是sku，value是cabinetCode
            Map<String, String> result = cabinetInventoryList.stream()
                    .collect(Collectors.groupingBy(
                            CabinetInventoryExistQueryResp::getSku,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(CabinetInventoryExistQueryResp::getQualityDate)),
                                    optional -> optional.map(CabinetInventoryExistQueryResp::getCabinetCode).orElse(null)
                            )
                    ));
            log.info("querySkuCabinetCodeMap result:{}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("invoke cabinetInventoryProvider.querySkuCabinetCodeMap fail warehouseNo:{}, skuList:{}", warehouseNo, skuList, e);
            return new HashMap<>();
        }

    }

}
