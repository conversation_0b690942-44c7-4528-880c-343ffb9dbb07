package net.summerfarm.manage.facade.merchant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreChangeLogQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreChangeLogQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;

@Component
public class MerchantQueryFacade {
    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;
    @DubboReference
    private MerchantExtendQueryProvider merchantExtendQueryProvider;
    @DubboReference
    private MerchantStoreChangeLogQueryProvider changeLogQueryProvider;

    @DubboReference
    private MerchantStoreExtQueryProvider merchantStoreExtQueryProvider;
    /**
     * 分页查询
     *
     * @param input
     * @return
     */
    public PageInfo<XmMerchantStorePageResp> pageQuery(MerchantStorePageQueryReq input) {
        input.setTenantId(XM_TENANTID);
        DubboResponse<PageInfo<XmMerchantStorePageResp>> merchantStorePageForXM = merchantExtendQueryProvider.getMerchantStorePageForXM(input);
        if (merchantStorePageForXM.isSuccess()) {
            return merchantStorePageForXM.getData();
        }
        throw new BizException(merchantStorePageForXM.getMsg());
    }



    /**
     *  批量查询门店信息(默认最多查200条)
     *
     * @param mId m id
     */
    public MerchantStoreAndExtendResp getMerchantExtendsByMid(Long mId) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMId(mId);
        req.setQueryManageAccount(true);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantStoreAndExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return null;
        }
        return merchantExtends.get(0);
    }



    /**
     *  批量查询门店信息(默认最多查200条)
     *
     * @param mId m id
     */
    public List<MerchantStoreAndExtendResp> getMerchantExtendsByMid(List<Long> mId) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mId);
        req.setQueryManageAccount(true);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantStoreAndExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return new ArrayList<>();
        }
        return merchantExtends;
    }





    /**
     * 多个条件查询
     *
     * @param input
     * @return
     */
    public List<MerchantStoreAndExtendResp> getMerchantStoreAndExtends(MerchantStoreQueryReq input) {
        input.setTenantId(XM_TENANTID);
        DubboResponse<List<MerchantStoreAndExtendResp>> merchantStoreAndExtends = merchantExtendQueryProvider.getMerchantStoreAndExtendsByPrimaryKeys(input);
        if (merchantStoreAndExtends.isSuccess()) {
            return merchantStoreAndExtends.getData();
        }
        throw new BizException(merchantStoreAndExtends.getMsg());
    }



    /**
     * 根据store_id查询门店的客户来源（是哪个门店推荐的）
     *
     * @param storeId
     * @return
     */
    public MerchantStoreResultResp getMerchantSource(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(XM_TENANTID);
        req.setOpType(0);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        String channelCode = data.get(0).getMerchantChannelCode();
        if(StringUtils.isBlank(channelCode)) {
            return null;
        }

        // 根据channelCode获取指定的merchant
        MerchantStoreQueryReq queryReq = new MerchantStoreQueryReq();
        queryReq.setTenantId(XM_TENANTID);
        queryReq.setChannelCode(channelCode);
        DubboResponse<List<MerchantStoreResultResp>> stores = merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(queryReq);
        if (!stores.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreResultResp> storesData = stores.getData();
        return CollUtil.isEmpty(storesData) ? null : storesData.get(0);
    }

    /**
     * 根据store_id查询门店的客户来源（是哪个门店推荐的）
     *
     * @param storeId
     * @return
     */
    public MerchantStoreChangeLogResultResp getMerchantBackMsg(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(XM_TENANTID);
        req.setOpType(3);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        List<MerchantStoreChangeLogResultResp> collect = data.stream().sorted(Comparator.comparing(MerchantStoreChangeLogResultResp::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
        return collect.get(0);
    }


    public String queryMerchantPropertiesExtValue(Long mid, String proKey) {
        if (mid == null || mid <= 0) {
            throw new BizException("参数错误");
        }
        if (StringUtils.isEmpty(proKey)) {
            throw new BizException("参数错误");
        }
        MerchantStoreExtQueryReq req = new MerchantStoreExtQueryReq();
        req.setMIds(Collections.singletonList(mid));
        req.setProKey(proKey);
        DubboResponse<List<MerchantStoreExtResp>> response = merchantStoreExtQueryProvider.getMerchantStoreExtByQuery(req);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            return "";
        }
        return response.getData().get(0).getProValue();
    }
}
