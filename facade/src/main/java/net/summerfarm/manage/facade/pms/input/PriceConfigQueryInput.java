package net.summerfarm.manage.facade.pms.input;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/20 14:28
 * @PackageName:net.summerfarm.manage.facade.pms.input
 * @ClassName: PriceConfigQueryInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class PriceConfigQueryInput {

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku列表
     */
    private List<String> skuList;
}
