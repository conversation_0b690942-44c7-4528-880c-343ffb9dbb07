package net.summerfarm.manage.facade.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.client.provider.WechatProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class AuthWechatQueryFacade {

    @DubboReference
    private WechatProvider wechatProvider;

    public String queryChannelToken(String channelCode) {
        if (StringUtils.isEmpty(channelCode)) {
            throw new BizException("token查询渠道码为空！");
        }

        DubboResponse<String> dubboResponse = wechatProvider.queryWeChatToken(channelCode);
        if (Objects.isNull(dubboResponse) || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())) {
            log.error("queryCloseTime[]getInfo[]error[]queryCloseTime:{}", JSON.toJSONString(dubboResponse));
            throw new BizException(dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

}
