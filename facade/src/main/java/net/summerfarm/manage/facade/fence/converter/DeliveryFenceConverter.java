package net.summerfarm.manage.facade.fence.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.manage.facade.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;

/**
 * @author: <EMAIL>
 * @create: 2023/11/15
 */
public class DeliveryFenceConverter {


    private DeliveryFenceConverter() {
        // 无需实现
    }

    public static List<DeliveryFenceDTO> toDeliveryFenceDTOList(
            List<DeliveryFenceResp> deliveryFenceRespList) {
        if (deliveryFenceRespList == null) {
            return Collections.emptyList();
        }
        List<DeliveryFenceDTO> deliveryFenceDTOList = new ArrayList<>();
        for (DeliveryFenceResp deliveryFenceResp : deliveryFenceRespList) {
            deliveryFenceDTOList.add(toDeliveryFenceDTO(deliveryFenceResp));
        }
        return deliveryFenceDTOList;
    }

    public static DeliveryFenceDTO toDeliveryFenceDTO(DeliveryFenceResp deliveryFenceResp) {
        if (deliveryFenceResp == null) {
            return null;
        }
        DeliveryFenceDTO deliveryFenceDTO = new DeliveryFenceDTO();
        deliveryFenceDTO.setFenceId(deliveryFenceResp.getFenceId());
        deliveryFenceDTO.setFenceName(deliveryFenceResp.getFenceName());
        deliveryFenceDTO.setStoreNo(deliveryFenceResp.getStoreNo());
        deliveryFenceDTO.setAreaNo(deliveryFenceResp.getAreaNo());
        deliveryFenceDTO.setCityName(deliveryFenceResp.getCityName());
        return deliveryFenceDTO;
    }
}
