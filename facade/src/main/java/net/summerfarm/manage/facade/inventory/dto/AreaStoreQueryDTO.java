package net.summerfarm.manage.facade.inventory.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 11:14
 * @Version 1.0
 */
@Data
public class AreaStoreQueryDTO {

    /**
     * 货品SKU编码，最多50个
     */
    public List<String> skuCodeList;

    /**
     * 门店ID
     */
    public Long mId;

    /**
     *
     */
    private Integer source;

    /**
     * 是否加单 true 加单 false不加单
     */
    private Boolean addOrderFlag;

    /**
     * 联系人id
     */
    public Long contactId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 城配仓编号
     */
    private  Integer storeNo;

    /**
     * POI
     */
    private String poiNote;
}
