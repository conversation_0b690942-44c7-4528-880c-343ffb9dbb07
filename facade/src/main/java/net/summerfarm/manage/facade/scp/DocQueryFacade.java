package net.summerfarm.manage.facade.scp;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.scp.converter.DocConverter;
import net.summerfarm.manage.facade.scp.dto.DocInfoDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.scp.client.provider.doc.DocQueryProvider;
import net.xianmu.scp.client.req.doc.DocQueryReq;
import net.xianmu.scp.client.resp.doc.DocInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DocQueryFacade {

    @DubboReference
    private DocQueryProvider docQueryProvider;

    public DocInfoDTO queryDocInfo(String sku, Integer warehouseNo) {
        DocQueryReq req= new DocQueryReq();
        req.setSku(sku);
        req.setWarehouseNo(warehouseNo);
        DubboResponse<DocInfoResp> dubboResponse = docQueryProvider.queryDocInfo(req);
        if (null == dubboResponse || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())) {
            throw new ProviderException("当前doc查询失败");
        }
        return DocConverter.toDocInfoResp(dubboResponse.getData());
    }
}
