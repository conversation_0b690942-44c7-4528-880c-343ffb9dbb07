package net.summerfarm.manage.facade.pms.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/20 14:32
 * @PackageName:net.summerfarm.manage.facade.pms.dto
 * @ClassName: PriceConfigQueryDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class PriceConfigQueryDTO {

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 生效时间
     */
    private LocalDateTime effectStartDate;

    /**
     * 失效时间
     */
    private LocalDateTime effectEndDate;

    /**
     * 状态
     */
    private String effectStatus;

    /**
     * 价格类型（佣金）
     */
    private String priceType;

    /**
     * 阶梯价
     */
    private List<PriceConfigStepDTO> stepPriceList;
}
