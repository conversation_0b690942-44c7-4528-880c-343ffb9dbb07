package net.summerfarm.manage.facade.deliivery.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ContactBelongFenceReq
 * @Description
 * <AUTHOR>
 * @Date 16:59 2024/3/5
 * @Version 1.0
 **/
@Data
public class ContactBelongFenceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址信息
     */
    private List<ContactFenceQueryInput> contacts;

    /**
     * 当前客户的渠道类型
     */
    private String fenceChannelType;
}
