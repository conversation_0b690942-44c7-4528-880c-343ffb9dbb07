package net.summerfarm.manage.facade.inventory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.common.util.ValidateUtil;
import net.summerfarm.manage.facade.inventory.convert.ProductCostConvert;
import net.summerfarm.manage.facade.inventory.dto.ProductCostQueryDto;
import net.summerfarm.manage.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.productcost.ProductCostQueryProvider;
import net.xianmu.inventory.client.productcost.dto.req.ProductCostBatchQueryReq;
import net.xianmu.inventory.client.productcost.dto.req.ProductCostBatchQueryV2Req;
import net.xianmu.inventory.client.productcost.dto.req.ProductCostQueryReq;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/17 11:35
 */

@Slf4j
@Component
public class ProductCostQueryFacade {

    @DubboReference
    private ProductCostQueryProvider productCostQueryProvider;

    @Autowired
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Resource
    private AreaStoreMapper areaStoreMapper;

    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public BigDecimal selectCycleCost(String sku, Integer warehouseNo){
        ProductCostQueryResp productCostQueryResp = selectBySku(sku, warehouseNo);
        return productCostQueryResp == null ? null : productCostQueryResp.getCurrentCost();
    }


    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public ProductCostQueryResp selectBySku(String sku, Integer warehouseNo){
        if(StringUtils.isBlank(sku) || warehouseNo == null) {
            log.warn("参数异常：sku:{}, warehouseNo:{}", sku, warehouseNo);
            return null;
        }
        ProductCostQueryReq req = new ProductCostQueryReq();
        req.setSku(sku);
        req.setWarehouseNo(warehouseNo);
        DubboResponse<ProductCostQueryResp> response = productCostQueryProvider.queryProductCost(req);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException(response.getMsg());
    }



    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public Map<String, BigDecimal> selectCostMapBySkus( Integer warehouseNo, List<String> skuList){
        if(CollUtil.isEmpty(skuList) || warehouseNo == null) {
            log.warn("参数异常：skuList:{}, warehouseNo:{}", JSON.toJSONString(skuList), warehouseNo);
            return new HashMap<>();
        }

        Map<String, BigDecimal> productCostMap = new HashMap<>();
        List<List<String>> split = ListUtil.split(skuList, 200);
        split.forEach(skus -> {
            List<ProductCostQueryResp> productCostQueryResps = innerQueryBySkus(skus, warehouseNo);
            Map<String, BigDecimal> collect = productCostQueryResps.stream().collect(Collectors.toMap(e -> e.getWarehouseNo() + e.getSku(), ProductCostQueryResp::getCurrentCost));
            productCostMap.putAll(collect);
        });
        return productCostMap;
    }



    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public List<ProductCostQueryResp> selectBySkus(List<String> skuList, Integer warehouseNo){
        if(CollUtil.isEmpty(skuList) || warehouseNo == null) {
            log.warn("参数异常：skuList:{}, warehouseNo:{}", JSON.toJSONString(skuList), warehouseNo);
            return Collections.emptyList();
        }

        List<ProductCostQueryResp> resultList = new ArrayList<>();
        List<List<String>> split = ListUtil.split(skuList, 200);
        split.forEach(skus -> resultList.addAll(innerQueryBySkus(skus, warehouseNo)));
        return resultList;
    }


    /**
     * 获取成本价（area+sku, cost）
     * @return
     */
//    public Map<String, ProductCostQueryResp> selectMapBySkuAndAreaNos(List<ProductCostQueryDto> list){
//        if(CollUtil.isEmpty(list)) {
//            log.warn("请求参数为空!");
//            return new HashMap<>();
//        }
//        list = list.stream().filter(dto -> StringUtils.isNotBlank(dto.getSku()) && dto.getAreaNo() != null).collect(Collectors.toList());
//
//        // 先查询仓
//        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq ();
//        List<WarehouseBySkuAreaNoDataReq> areaSkuReqs = new ArrayList<> ();
//        list.stream().collect(Collectors.groupingBy(ProductCostQueryDto::getSku)).forEach ((sku, areaSkuList)->{
//            WarehouseBySkuAreaNoDataReq reqData = new WarehouseBySkuAreaNoDataReq ();
//            reqData.setSku(sku);
//            reqData.setAreaNoList (areaSkuList.stream().map (ProductCostQueryDto::getAreaNo).collect(Collectors.toList()));
//            areaSkuReqs.add (reqData);
//        });
//        req.setAreaSkuList (areaSkuReqs);
//        List<WarehouseBySkuAreaNoResp> resps = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo(req);
//        Map<String, String> areaSkuWarehouseMap = resps.stream().collect(Collectors.toMap(item -> item.getAreaNo() + item.getSku(), item -> item.getWarehouseNo() + item.getSku()));
//
//        // 获取成本集合
//        List<ProductCostQueryDto> reqList = ProductCostConvert.toProductCostQueryDtoList(resps);
//        Map<String, ProductCostQueryResp> costQueryRespMap = selectMapBySkuAndWarehouseNos(reqList);
//
//        Map<String, ProductCostQueryResp> resultMap = new HashMap<>();
//        list.forEach(item -> {
//            String areaNoCompositeKey = item.getAreaNo() + "|" + item.getSku();
//            String warehouseNoAndSku = areaSkuWarehouseMap.get(areaNoCompositeKey);
//            ProductCostQueryResp productCostQueryResp = costQueryRespMap.get(warehouseNoAndSku);
//            resultMap.put(areaNoCompositeKey, productCostQueryResp);
//        });
//        return resultMap;
//    }

    /**
     * 获取成本价（sku_areano, cost）
     * @return
     */
    public Map<String, ProductCostQueryResp> selectMapBySkuAndAreaNosAndWarehouseNoMap(Map<String, Set<Integer>> availableAreaMap, Map<String, Integer> skuAreaWarehouseNoMap){
        log.info("开始成本查询 availableAreaMap：{}, skuAreaWarehouseNoMap:{}", JSON.toJSONString(availableAreaMap), JSON.toJSONString(skuAreaWarehouseNoMap));
        if(CollUtil.isEmpty(availableAreaMap) || CollUtil.isEmpty(skuAreaWarehouseNoMap)) {
            log.warn("请求参数为空!");
            return new HashMap<>();
        }

        List<ProductCostQueryDto> reqList = new ArrayList<> ();
        availableAreaMap.forEach ((sku, areaList)-> areaList.forEach (area ->{
            ProductCostQueryDto dto = new ProductCostQueryDto ();
            dto.setSku (sku);
            String key = sku + "_" + area;
            if(skuAreaWarehouseNoMap.containsKey (key)) {
                dto.setWarehouseNo (skuAreaWarehouseNoMap.get (key));
                reqList.add (dto);
            }
        }));
        Map<String, ProductCostQueryResp> costQueryRespMap = selectMapBySkuAndWarehouseNos(reqList);

        Map<String, ProductCostQueryResp> resultMap = new HashMap<>();

        availableAreaMap.forEach ((sku, areaList)-> areaList.forEach (area ->{
            Integer warehouseNo = skuAreaWarehouseNoMap.get (sku + "_" + area);
            log.info("sku：{}, areaNo ：{} 映射到的仓为 warehouseNo：{}", sku, area, warehouseNo);
            if(warehouseNo != null) {
                ProductCostQueryResp productCostQueryResp = costQueryRespMap.get (warehouseNo + "|" + sku);
                if(productCostQueryResp != null) {
                    resultMap.put(sku + "_" + area, productCostQueryResp);
                } else {
                    // 设置兜底
                    productCostQueryResp = this.selectLastBatchBySkuAndWarehouseNo(warehouseNo, sku);
                    if(productCostQueryResp != null) {
                        resultMap.put(sku + "_" + area, productCostQueryResp);
                    }
                }
            }
        }));
        return resultMap;
    }

    private ProductCostQueryResp selectLastBatchBySkuAndWarehouseNo(Integer warehouseNo, String sku){
        CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(warehouseNo, sku);
        log.info("日周期成本为空,进入兜底逻辑查询最新批次成本。sku：{}, warehouseNo:{}, costChangeVo:{}", sku, warehouseNo, JSON.toJSONString(costChangeVo));
        if (Objects.nonNull(costChangeVo) && costChangeVo.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
            ProductCostQueryResp productCostQueryResp = new ProductCostQueryResp();
            productCostQueryResp.setSku(sku);
            productCostQueryResp.setWarehouseNo(warehouseNo);
            productCostQueryResp.setCurrentCost(costChangeVo.getCostPrice());
            return productCostQueryResp;
        }
        return null;
    }



    /**
     * 获取成本价
     *      * key :WarehouseNo + | + Sku
     *      * value: ProductCostQueryResp
     * @return
     */
    public Map<String, ProductCostQueryResp> selectMapBySkuAndWarehouseNos(List<ProductCostQueryDto> list){
        if(CollUtil.isEmpty(list)) {
            log.info("请求参数为空！");
            return new HashMap<>();
        }
        list = list.stream().filter(ValidateUtil.distinctByKeys(p -> Arrays.asList(p.getSku(), p.getWarehouseNo()))).collect(Collectors.toList());
        List<ProductCostQueryResp> productCostResps = selectBySkuAndWarehouseNos(list);
        return productCostResps.stream()
                .collect(Collectors.toMap(
                        item -> item.getWarehouseNo() + "|" + item.getSku(),
                        Function.identity()
                ));
    }


    /**
     * 获取成本价(查不到时根据最新批次兜底)
     * key :WarehouseNo + | + Sku
     * value: ProductCostQueryResp
     * @return
     */
    public Map<String, ProductCostQueryResp> selectMapBySkuAndWarehouseNosWithLastRecord(List<ProductCostQueryDto> list){
        if(CollUtil.isEmpty(list)) {
            log.info("请求参数为空！");
            return new HashMap<>();
        }
        list = list.stream().filter(ValidateUtil.distinctByKeys(p -> Arrays.asList(p.getSku(), p.getWarehouseNo()))).collect(Collectors.toList());
        List<ProductCostQueryResp> productCostResps = selectBySkuAndWarehouseNos(list);
        Map<String, ProductCostQueryResp> respMap = productCostResps.stream()
                .collect(Collectors.toMap(
                        item -> item.getWarehouseNo() + "|" + item.getSku(),
                        Function.identity()
                ));

        // 根据兜底策略补全
        list.forEach(dto -> {
            String key = dto.getWarehouseNo() + "|" + dto.getSku();
            ProductCostQueryResp costQueryResp = respMap.get(key);
            if(costQueryResp == null) {
                ProductCostQueryResp lastBatchCost = this.selectLastBatchBySkuAndWarehouseNo(dto.getWarehouseNo(), dto.getSku());
                if(lastBatchCost != null) {
                    respMap.put(key, lastBatchCost);
                }
            }
        });
        return respMap;
    }


    /**
     * 获取成本价
     * @return
     */
    public List<ProductCostQueryResp> selectBySkuAndWarehouseNos(List<ProductCostQueryDto> list){
        List<ProductCostQueryResp> resultList = new ArrayList<>();
        List<List<ProductCostQueryDto>> split = ListUtil.split(list, 200);
        split.forEach(skus -> resultList.addAll(innerQueryBySkuAndWarehouseNos(skus)));
        return resultList;
    }


    /**
     * inventory那边最大支持查200条
     * @param skuList
     * @param warehouseNo
     * @return
     */
    private List<ProductCostQueryResp> innerQueryBySkus(List<String> skuList, Integer warehouseNo){
        ProductCostBatchQueryReq req = new ProductCostBatchQueryReq();
        req.setSkuList(skuList);
        req.setWarehouseNo(warehouseNo);
        DubboResponse<List<ProductCostQueryResp>> response = productCostQueryProvider.batchQueryProductCost(req);
        if (response.isSuccess()) {
            List<ProductCostQueryResp> data = response.getData();
            return data == null ? Collections.emptyList() : data;
        }
        throw new ProviderException(response.getMsg());
    }

    /**
     * inventory那边最大支持查200条
     * @param skuList
     * @param warehouseNo
     * @return
     */
    private List<ProductCostQueryResp> innerQueryBySkuAndWarehouseNos(List<ProductCostQueryDto> list){
        if(CollUtil.isEmpty(list)) {
            log.warn("请求参数为空!");
            return Collections.emptyList();
        }

        ProductCostBatchQueryV2Req req = new ProductCostBatchQueryV2Req();
        req.setWarehouseAndSkuQueryList(ProductCostConvert.toWarehouseAndSkuQueryList(list));
        DubboResponse<List<ProductCostQueryResp>> response = productCostQueryProvider.batchQueryProductCostV2(req);
        if (response.isSuccess()) {
            List<ProductCostQueryResp> data = response.getData();
            return data == null ? Collections.emptyList() : data;
        }
        throw new ProviderException(response.getMsg());
    }



}
