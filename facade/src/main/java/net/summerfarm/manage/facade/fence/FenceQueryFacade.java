package net.summerfarm.manage.facade.fence;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.enums.FenceStatusEnum;
import net.summerfarm.manage.facade.fence.converter.DeliveryFenceConverter;
import net.summerfarm.manage.facade.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.warehouseMapping.WarehouseInventoryMappingQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.DeliveryFenceByCityQueryReq;
import net.summerfarm.wnc.client.req.fence.FenceQueryReq;
import net.summerfarm.wnc.client.req.warehouseMapping.SkuAreaQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.fence.FenceResp;
import net.summerfarm.wnc.client.resp.warehouseMapping.AreaWarehouseResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 围栏查询服务
 *
 * @author: <EMAIL>
 * @create: 2023/11/15
 */
@Slf4j
@Component
public class FenceQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;
    @DubboReference
    private WarehouseInventoryMappingQueryProvider warehouseInventoryMappingQueryProvider;

    public static final Integer MAX_CITY_NUM = 200;

    public List<DeliveryFenceDTO> queryFenceCity(List<String> cityNames) {
        List<DeliveryFenceDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(cityNames)) {
            log.warn("invoke FenceQueryFacade.queryFenceCity error:cityNames is empty");
            return list;
        }
        //先去重
        cityNames = cityNames.stream().distinct().collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(cityNames, MAX_CITY_NUM);
        partition.forEach(subList -> {
            try {
                DeliveryFenceByCityQueryReq cityQueryReq = new DeliveryFenceByCityQueryReq();
                cityQueryReq.setCityNames(subList);
                DubboResponse<List<DeliveryFenceResp>> dubboResponse =
                        deliveryFenceQueryProvider.batchQueryDeliveryFenceByCity(cityQueryReq);
                if (dubboResponse == null ||!dubboResponse.isSuccess()) {
                    log.warn("invoke FenceQueryFacade.queryFenceCity error:{}", dubboResponse);
                    return;
                }
                list.addAll(DeliveryFenceConverter.toDeliveryFenceDTOList(dubboResponse.getData()));
            } catch (Exception e) {
                //不做处理，保证查询成功的数据依旧有效
                log.warn("invoke FenceQueryFacade.queryFenceCity error:{}", Throwables.getStackTraceAsString(e));
            }
        });
        return list;
    }

    public AreaQueryResp getArea(String city, String area){
        AreaQueryReq req = new AreaQueryReq();
        req.setArea(area);
        req.setCity(city);
        DubboResponse<AreaQueryResp> areaQueryResp = deliveryFenceQueryProvider.queryAreaByAddress(req);
        if (areaQueryResp.isSuccess()) {
            return areaQueryResp.getData();
        }
        throw new BizException(areaQueryResp.getMsg());
    }


    public Integer getWarehouseNo(String sku, Integer areaNo){
        SkuAreaQueryReq req = new SkuAreaQueryReq();
        req.setSku(sku);
        req.setAreaNo(areaNo);
        DubboResponse<AreaWarehouseResp> response = warehouseInventoryMappingQueryProvider.queryWarehouseBySkuAndAreaNo(req);
        if (response.isSuccess()) {
            AreaWarehouseResp data = response.getData();
            List<Integer> warehouseNos = data.getWarehouseNos();
            return CollUtil.isEmpty(warehouseNos) ? null : warehouseNos.get(0);
        }
        throw new BizException(response.getMsg());
    }

    public Set<Integer> queryAllAreaNoWithOpenFence(){
        FenceQueryReq req = new FenceQueryReq ();
        req.setStatus (FenceStatusEnum.OPEN.getCode ());
        DubboResponse<List<FenceResp>> response = deliveryFenceQueryProvider.queryFenceListWithArea (req);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<FenceResp> data = response.getData();
        if(CollectionUtil.isNotEmpty (data)){
            return data.stream ().map (FenceResp::getAreaNo).collect (Collectors.toSet ());
        }
        return Collections.emptySet ();
    }

    public List<Integer> queryStoreNosByAreaNo(Integer areaNo){
        FenceQueryReq req = new FenceQueryReq ();
        req.setStatus (FenceStatusEnum.OPEN.getCode ());
        req.setAreaNo (areaNo);
        DubboResponse<List<FenceResp>> response = deliveryFenceQueryProvider.queryFenceListWithArea (req);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<FenceResp> data = response.getData();
        if(CollectionUtil.isNotEmpty (data)){
            return data.stream ().map (FenceResp::getStoreNo).collect (Collectors.toList ());
        }
        return Collections.emptyList ();
    }
}
