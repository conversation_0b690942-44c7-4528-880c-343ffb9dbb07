package net.summerfarm.manage.facade.deliivery.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2024/1/4 14:59:15
 */
@Data
public class ContactFenceQueryInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;
}
