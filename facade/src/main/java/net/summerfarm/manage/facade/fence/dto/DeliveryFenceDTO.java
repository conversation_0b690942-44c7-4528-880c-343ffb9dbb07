package net.summerfarm.manage.facade.fence.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/11/15
 */
@Data
public class DeliveryFenceDTO implements Serializable {

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 城市名称
     */
    private String cityName;

}
