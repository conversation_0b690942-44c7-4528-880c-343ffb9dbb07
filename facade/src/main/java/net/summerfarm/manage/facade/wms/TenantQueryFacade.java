package net.summerfarm.manage.facade.wms;

import cn.hutool.json.JSONUtil;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.wms.converter.TenantConverter;
import net.summerfarm.manage.facade.wms.dto.TenantDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * @Date 2023/4/18 14:18
 * @<AUTHOR>
 */
@Component
@Slf4j
public class TenantQueryFacade {

    @DubboReference
    private TenantProvider tenantProvider;

    public List<TenantDTO> listSaasTenant() {
        try {
            TenantQueryReq queryReq = new TenantQueryReq();
            DubboResponse<List<TenantResp>> response = tenantProvider.list(queryReq);
            if (!response.isSuccess()) {
                log.error("invoke tenantProvider.list fail request:{}, msg:{}", JSONUtil.toJsonStr(queryReq), response.getMsg());
                return Lists.newArrayList();
            }
            return TenantConverter.tenantRespList2TenantDTOList(response.getData());
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }
}
