package net.summerfarm.manage.facade.pms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.pms.converter.PriceConfigConverter;
import net.summerfarm.manage.facade.pms.dto.PriceConfigQueryDTO;
import net.summerfarm.manage.facade.pms.input.PriceConfigQueryInput;
import net.summerfarm.pms.client.provider.PriceConfigQueryProvider;
import net.summerfarm.pms.client.req.PriceConfigQueryReq;
import net.summerfarm.pms.client.resp.PriceConfigQueryResp;
import net.summerfarm.pms.client.resp.dto.PriceConfigDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/20 14:25
 * @PackageName:net.summerfarm.manage.facade.pms
 * @ClassName: PriceConfigQueryFacade
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class PriceConfigQueryFacade {
    
    @DubboReference
    private PriceConfigQueryProvider priceConfigQueryProvider;

    /**
    * @description 获取sku佣金比例
    * @params [input]
    * @return java.util.Map<java.lang.String,net.summerfarm.manage.facade.pms.dto.PriceConfigQueryDTO>
    * <AUTHOR>
    * @date  2025/6/20 14:41
    */
    public Map<String, List<PriceConfigQueryDTO>> queryPriceConfig(PriceConfigQueryInput input) {
        PriceConfigQueryReq req = PriceConfigConverter.toPriceConfigQueryReq(input);
        DubboResponse<PriceConfigQueryResp> dubboResponse = priceConfigQueryProvider.queryPriceConfig(req);
        if (null == dubboResponse || !dubboResponse.isSuccess()) {
            log.error("Warehouse查询失败");
            return Collections.emptyMap();
        }
        return PriceConfigConverter.toMap(dubboResponse.getData());
    }
}
