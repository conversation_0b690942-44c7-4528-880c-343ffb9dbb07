package net.summerfarm.manage.facade.wnc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseSkuAreaNoQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description
 * @date 2023/6/30 15:22:39
 */
@Service
@Slf4j
public class WarehouseSkuAreaNoQueryFacade {

    @DubboReference
    private WarehouseSkuAreaNoQueryProvider warehouseSkuAreaNoQueryProvider;

    public List<WarehouseBySkuAreaNoResp> queryBySkuAreNo(WarehouseBySkuAreaNoQueryReq queryReq) {
        if(CollectionUtils.isEmpty(queryReq.getAreaSkuList())) {
            log.warn("WarehouseSkuAreaNoQueryFacade[]queryBySkuAreNo[],请求参数为空！");
            return Collections.emptyList();
        }
        List<WarehouseBySkuAreaNoDataReq> areaSkuList = queryReq.getAreaSkuList();
        List<List<WarehouseBySkuAreaNoDataReq>> partition = ListUtil.partition(areaSkuList, 50);
        List<WarehouseBySkuAreaNoResp> resultList = new ArrayList<>();
        partition.forEach(list -> {
            WarehouseBySkuAreaNoQueryReq innerQueryReq = new WarehouseBySkuAreaNoQueryReq();
            innerQueryReq.setAreaSkuList(list);
            resultList.addAll(innerQueryBySkuAreNo(innerQueryReq));
        });
        return resultList;
    }


    private List<WarehouseBySkuAreaNoResp> innerQueryBySkuAreNo(WarehouseBySkuAreaNoQueryReq queryReq) {
        log.info("WarehouseSkuAreaNoQueryFacade[]queryBySkuAreNo[]queryReq:{}", JSON.toJSONString(queryReq));
        DubboResponse<List<WarehouseBySkuAreaNoResp>> response = warehouseSkuAreaNoQueryProvider.queryBySkuAreNo(queryReq);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("WarehouseSkuAreaNoQueryFacade[]queryBySkuAreNo[]error cause:{}", JSON.toJSONString(response));
            throw new BizException(response.getMsg());
        }
        log.info("WarehouseSkuAreaNoQueryFacade[]queryBySkuAreNo[]response:{}", JSON.toJSONString(response));
        return response.getData() == null ? Collections.emptyList() : response.getData();
    }
}
