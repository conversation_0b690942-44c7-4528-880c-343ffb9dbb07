package net.summerfarm.manage.facade.message;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.provider.MessageSendProviderV2;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.facade.message.input.SendMessageInput;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName WxSendMessageFacade
 * @Description 发送微信公众号消息
 * <AUTHOR>
 * @Date 15:37 2024/2/20
 * @Version 1.0
 **/
@Slf4j
@Component
public class WxSendMessageFacade {

    @DubboReference
    private MessageSendProviderV2 messageSendProviderV2;

    private static final int MAX_SIZE = 50;
    
    /***
     * @author: lzh
     * @description: 
     * @date: 2024/2/20 15:45
     * @param: [msg]
     * @return: void
     **/
    public void sendMessage(SendMessageInput sendMessageInput){
        if (Objects.isNull(sendMessageInput)) {
            return;
        }

        MessageBodyReq messageBodyReq = new MessageBodyReq();
        messageBodyReq.setContentType(MessageContentTypeEnum.NORMAL.getType());

        //模版ID
        messageBodyReq.setTemplateId(sendMessageInput.getTemplateId());

        //模版数据
        messageBodyReq.setData(sendMessageInput.getMessage());

        //openId列表，单次最大50条
        List<String> openIdList = Lists.newArrayList(sendMessageInput.getOpenId());

        //微信公众号code
        String channelCode = WxOfficialAccountsChannelEnum.XM_MALL.channelCode;

        //需要跳转时传入该参数
        messageBodyReq.setJumpUrlTypeEnum(JumpUrlTypeEnum.PAGE);
        messageBodyReq.setJumpUrl(sendMessageInput.getJumpUrl());

        Long tenantId = Global.XM_TENANTID;

        log.info("WxSendMessageFacade[]batchSendMessageByThridUid[]start[]messageBodyReq:{}", JSON.toJSONString(messageBodyReq));
        DubboResponse<List<MsgSendLogResp>> response = messageSendProviderV2.batchSendMessageByThridUid(tenantId,
                ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, channelCode, null, openIdList, messageBodyReq, null);
        if(Objects.isNull(response)|| !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.warn("WxSendMessageFacade[]batchSendMessageByThridUid[]is null or error sendMessageInput:{}", JSON.toJSONString(sendMessageInput));
        }
    }
}
