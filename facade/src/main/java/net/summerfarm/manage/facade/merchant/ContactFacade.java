package net.summerfarm.manage.facade.merchant;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.enums.DistributionRulesTypeEnum;
import net.summerfarm.manage.facade.deliivery.converter.ContactConverter;
import net.summerfarm.manage.facade.deliivery.converter.DistributionRulesConverter;
import net.summerfarm.manage.facade.deliivery.dto.ContactBelongFenceDTO;
import net.summerfarm.manage.facade.deliivery.dto.DeliveryFeeRuleDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;
import net.summerfarm.manage.facade.deliivery.input.ContactBelongFenceReq;
import net.summerfarm.manage.facade.deliivery.input.DistributionRulesInfoInput;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.fence.ContactAddressBatchQueryReq;
import net.summerfarm.wnc.client.req.fence.ContactAddressQueryReq;
import net.summerfarm.wnc.client.resp.fence.ContactAddressBelongFenceResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.freight.provider.DeliveryFeeRuleQueryProvider;
import net.xianmu.marketing.center.client.freight.provider.DistributionRulesProvider;
import net.xianmu.marketing.center.client.freight.req.DeliveryFeeRuleQueryReq;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesDetailReq;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesInfoReq;
import net.xianmu.marketing.center.client.freight.resp.DeliveryFeeRuleInfoResp;
import net.xianmu.marketing.center.client.freight.resp.DistributionRulesDetailResp;
import net.xianmu.usercenter.client.merchant.enums.MerchantAddressEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;

@Component
@Slf4j
public class ContactFacade {
    @DubboReference
    private MerchantAddressQueryProvider addressQueryProvider;

    @DubboReference
    private DistributionRulesProvider distributionRulesProvider;

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    @DubboReference
    private DeliveryFeeRuleQueryProvider deliveryFeeRuleQueryProvider;

    private static final int MAX_SIZE = 50;



    /**
     * 查询地址-联系人列表
     * @param storeIds
     * @return
     */
    public List<MerchantAddressDomainResp> queryNormalContact(List<Long> storeIds) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setStoreIdList(storeIds);
        req.setTenantId(XM_TENANTID);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        final DubboResponse<List<MerchantAddressDomainResp>> contacts = addressQueryProvider.getAddressAndContacts(req);
        if (contacts.isSuccess()) {
            return contacts.getData();
        }
        throw new BizException(contacts.getMsg());
    }


    /**
     * @description: 查询运费规则信息
     * @author: lzh
     * @date: 2023/10/12 16:48
     * @param: [infoReq]
     * @return: net.summerfarm.facade.marketing.dto.DistributionRulesDTO
     **/
    public DistributionRulesDTO getInfo(DistributionRulesInfoInput infoReq) {
        DistributionRulesInfoReq req = new DistributionRulesInfoReq();
        req.setType(infoReq.getType());
        req.setTypeId(infoReq.getTypeId());
        DubboResponse<DistributionRulesDetailResp> response = distributionRulesProvider.getInfo(req);
        if(Objects.isNull(response)|| !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("ContactFacade[]getInfo[]error,infoReq:{}", JSON.toJSONString(infoReq));
            return null;
        }
        DistributionRulesDetailResp rulesDetailResp = response.getData();
        DistributionRulesDTO rulesDTO = DistributionRulesConverter.toDistributionRulesDTO(rulesDetailResp);
        return rulesDTO;
    }

    /**
     * @description: 根据地址查询围栏状态
     * @author: lzh
     * @date: 2024/1/4 15:08
     * @param: [inputs]
     * @return: java.util.Map<java.lang.Long,net.summerfarm.manage.facade.app.dto.ContactBelongFenceDTO>
     **/
    public Map<Long, ContactBelongFenceDTO> batchQueryContactAddressBelongFence(ContactBelongFenceReq contactBelongFenceReq) {
        log.info("ContactFacade[]batchQueryContactAddressBelongFence[]start contactBelongFenceReq:{}",
                JSON.toJSONString(contactBelongFenceReq));
        if (Objects.isNull(contactBelongFenceReq) || CollectionUtils.isEmpty(contactBelongFenceReq.getContacts())) {
            return Maps.newHashMap();
        }
        String fenceChannelType = contactBelongFenceReq.getFenceChannelType();
        List<ContactAddressQueryReq> contactAddressQueryReqs = ContactConverter.contactFenceQueryInputToBatchQueryReq(contactBelongFenceReq.getContacts());
        Map<Long, ContactBelongFenceDTO> map = new HashMap<>(contactAddressQueryReqs.size());

        ContactAddressBatchQueryReq batchQueryReq = new ContactAddressBatchQueryReq();
        if (contactAddressQueryReqs.size() > MAX_SIZE) {
            for (int i = 0; i * MAX_SIZE < contactAddressQueryReqs.size(); i++) {
                List<ContactAddressQueryReq> queryReqs = contactAddressQueryReqs.subList(MAX_SIZE * i, Math.min(MAX_SIZE * (i + 1), contactAddressQueryReqs.size()));
                batchQueryReq.setContactAddressQueryReqList(queryReqs);
                DubboResponse<List<ContactAddressBelongFenceResp>> dubboResponse = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(batchQueryReq);
                log.info("ContactFacade[]batchQueryContactAddressBelongFence[]batchQueryContactAddressBelongFence[]dubboResponse:{}",
                        JSON.toJSONString(dubboResponse));
                if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
                    log.error("ContactFacade[]batchQueryContactAddressBelongFence[]batchQueryContactAddressBelongFence[]empty!");
                    continue;
                }
                ContactConverter.toContactBelongFenceDTOMap(fenceChannelType, map, dubboResponse.getData());
            }
        } else {
            batchQueryReq.setContactAddressQueryReqList(contactAddressQueryReqs);
            DubboResponse<List<ContactAddressBelongFenceResp>> dubboResponse = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(batchQueryReq);
            log.info("ContactFacade[]batchQueryContactAddressBelongFence[]batchQueryContactAddressBelongFence[]dubboResponse:{}",
                    JSON.toJSONString(dubboResponse));
            if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
                log.error("ContactFacade[]batchQueryContactAddressBelongFence[]batchQueryContactAddressBelongFence[]empty!");
                return Maps.newHashMap();
            }
            ContactConverter.toContactBelongFenceDTOMap(fenceChannelType, map, dubboResponse.getData());
        }
        return map;
    }

    public DistributionRulesDTO getDetail(Long adminId, Long contactId, Integer areaNo) {
        DistributionRulesDetailReq req = new DistributionRulesDetailReq();
        List<DistributionRulesDetailReq.DetailReq> detailReqs = new ArrayList<>();
        if (areaNo == null){
            areaNo = 0;
        }
        if (adminId != null) {
            DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
            detailReq.setType(DistributionRulesTypeEnum.ADMIN.getCode());
            detailReq.setTypeId(adminId);
            detailReq.setAreaNo(areaNo);
            detailReqs.add(detailReq);
        }
        if (contactId != null) {
            DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
            detailReq.setType(DistributionRulesTypeEnum.MERCHANT.getCode());
            detailReq.setTypeId(contactId);
            detailReqs.add(detailReq);
        }
        DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
        detailReq.setType(DistributionRulesTypeEnum.AREA.getCode());
        detailReq.setTypeId(areaNo.longValue());
        detailReqs.add(detailReq);
        req.setDetailReqs(detailReqs);

        DubboResponse<DistributionRulesDetailResp> response = distributionRulesProvider.getDetail(req);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("ContactFacade[]getInfo[]error,infoReq:{}", JSON.toJSONString(req));
            return null;
        }
        DistributionRulesDetailResp rulesDetailResp = response.getData();
        DistributionRulesDTO rulesDTO = DistributionRulesConverter.toDistributionRulesDTO(rulesDetailResp);
        return rulesDTO;
    }

    public List<DeliveryFeeRuleDTO> getDeliveryFeeRule(Long contactId, Long adminId, Integer areaNo, String province, String city) {
        // 查询支持阶梯价的运费规则必须传入省、市
        if (StringUtils.isBlank(province) || StringUtils.isBlank(city)) {
            return new ArrayList<>();
        }
        DeliveryFeeRuleQueryReq req = new DeliveryFeeRuleQueryReq();
        req.setTenantId(XM_TENANTID);
        if (contactId != null) {
            req.setContactId(contactId.intValue());
        }
        req.setXmAdminId(adminId != null ? adminId.intValue() : null);
        req.setAreaNo(areaNo);
        req.setProvince(province);
        req.setCity(city);
        DubboResponse<DeliveryFeeRuleInfoResp> response = deliveryFeeRuleQueryProvider.queryDeliveryFeeRule(req);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("获取运费规则失败, req:{}", JSON.toJSONString(req));
            return new ArrayList<>();
        }
        return DistributionRulesConverter.toDeliveryFeeRuleDTOList(response.getData());
    }
}
