package net.summerfarm.manage.application.service.merchant.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantCancelAssembler;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelPageQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.OperatorVO;
import net.summerfarm.manage.application.service.merchant.MerchantCancelQueryService;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.enums.CommonStatus;
import net.summerfarm.manage.common.enums.MerchantCancelEnum;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantCancelQueryRepository;
import net.summerfarm.manage.facade.merchant.MerchantAccountFacade;
import net.summerfarm.manage.facade.merchant.MerchantCancelFacade;
import net.summerfarm.manage.facade.merchant.MerchantQueryFacade;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2023-12-27 14:45:17
* @version 1.0
*
*/
@Service
public class MerchantCancelQueryServiceImpl implements MerchantCancelQueryService {

    @Autowired
    private MerchantCancelQueryRepository merchantCancelQueryRepository;
    @Autowired
    private AreaQueryRepository areaQueryRepository;
    @Autowired
    private AdminQueryRepository adminQueryRepository;
    @Resource
    private FollowUpRelationRepository followUpRelationRepository;
    @Resource
    private MerchantAccountFacade merchantAccountFacade;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private MerchantCancelFacade merchantCancelFacade;


    @Override
    public PageInfo<MerchantCancelVO> getPage(MerchantCancelPageQueryInput input) {
        PageInfo<MerchantCancelEntity> entityPageInfo = merchantCancelQueryRepository.getPage(MerchantCancelAssembler.toMerchantCancelQueryParam(input));
        PageInfo<MerchantCancelVO> page = PageInfoConverter.toPageResp(entityPageInfo, MerchantCancelAssembler::toMerchantCancelVO);
        List<MerchantCancelVO> merchantCancelVOList = page.getList();
        if(CollUtil.isEmpty(merchantCancelVOList)) {
            return page;
        }

        // 组装门店信息
        List<Long> collect = merchantCancelVOList.stream().map(MerchantCancelVO::getMId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantList = merchantQueryFacade.getMerchantExtendsByMid(collect);
        MerchantCancelVO.wrapVoByMerchant(merchantList, merchantCancelVOList);

/*        // 补充已注销店铺的店长信息
        List<MerchantStoreAccountResultResp> accountList = merchantAccountFacade.queryDeleteMerchantManager(collect);
        MerchantCancelVO.wrapVoByAccount(accountList, merchantCancelVOList);*/

        //获取地推人员信息和运营区域
        merchantCancelVOList.stream().forEach(e -> {

            FollowUpRelationEntity follow = followUpRelationRepository.selectOne(FollowUpRelationEntity.builder().mId(e.getMId()).build());
            if (Objects.nonNull(follow)) {
                e.setAdminRealName(follow.getReassign() ? "默认邀请码" : follow.getAdminName());
            }
            String areaNo = e.getAreaNo();
            if(StrUtil.isNotBlank(areaNo)) {
                Area area = areaQueryRepository.selectByAreaNo(Integer.valueOf(areaNo));
                if (Objects.nonNull(area)) {
                    e.setAreaName(area.getAreaName());
                }
            }
        });
        return page;
    }

    @Override
    public MerchantCancelVO getDetail(MerchantCancelQueryInput input) {
        if (Objects.isNull(input) || Objects.isNull(input.getId())) {
            throw new BizException("门店申请注销ID不能为空！");
        }
        MerchantCancelEntity merchantCancel = merchantCancelQueryRepository.selectById(input.getId());
        if (Objects.isNull(merchantCancel)) {
            return null;
        }
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setId(merchantCancel.getId());
        merchantCancelVO.setMId(merchantCancel.getMId());
        merchantCancelVO.setPhone(merchantCancel.getPhone());
        merchantCancelVO.setResource(merchantCancel.getResource());
        merchantCancelVO.setRemake(merchantCancel.getRemake());
        merchantCancelVO.setStatus(merchantCancel.getStatus());
        merchantCancelVO.setCertificate(merchantCancel.getCertificate());

        //获取申请人信息 0-商城  1-后台
        OperatorVO creator = new OperatorVO();
        if(Objects.equals(CommonStatus.YES.getCode(), merchantCancel.getResource())) {
            AdminEntity adminEntity = adminQueryRepository.selectByPrimaryKey(merchantCancel.getCreator());
            creator.setRealname(adminEntity.getRealname());
        } else {
            MerchantStoreAccountResultResp merchantSubAccount = merchantAccountFacade.selectMerchantAccountByAccountId(merchantCancel.getCreator());
            creator.setRealname(merchantSubAccount.getAccountName());
        }
        merchantCancelVO.setCreator(creator);
        merchantCancelVO.setCreateTime(merchantCancel.getCreateTime());
        if (Objects.equals(merchantCancel.getStatus(), MerchantCancelEnum.CANCELLED.getCode())) {
            merchantCancelVO.setUpdateTime(merchantCancel.getUpdateTime());
        }
        return merchantCancelVO;
    }

    @Override
    public MerchantCancelVO check(MerchantCancelQueryInput input) {
        if (Objects.isNull(input) || Objects.isNull(input.getMId())) {
            throw new BizException("门店申请注销编号不能为空！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setMId(input.getMId());
        List<String> check = merchantCancelFacade.check(merchantCancelInputReq);
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setCause(check);
        return merchantCancelVO;
    }

    @Override
    public MerchantCancelVO getInfo(MerchantCancelQueryInput input) {
        if (Objects.isNull(input)) {
            return null;
        }
        MerchantCancelQueryParam param = new MerchantCancelQueryParam();
        param.setPhone(input.getPhone());
        param.setStatus(input.getStatus());
        List<MerchantCancelEntity> cancelList = merchantCancelQueryRepository.selectByCondition(param);
        if (CollUtil.isEmpty(cancelList)) {
            return null;
        }
        MerchantCancelEntity merchantCancel = cancelList.get(0);
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setMId(merchantCancel.getMId());
        merchantCancelVO.setRemake(merchantCancel.getRemake());
        merchantCancelVO.setPhone(merchantCancel.getPhone());
        merchantCancelVO.setCertificate(merchantCancel.getCertificate());
        merchantCancelVO.setUpdateTime(merchantCancel.getUpdateTime());
        merchantCancelVO.setCreateTime(merchantCancel.getCreateTime());
        merchantCancelVO.setId(merchantCancel.getId());
        return merchantCancelVO;
    }
}