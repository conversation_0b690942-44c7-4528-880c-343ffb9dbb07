package net.summerfarm.manage.application.inbound.controller.customization.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * 定制需求查询输入参数
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizationRequestsQueryInput extends BasePageInput {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 主键Id列表（批量操作使用）
     */
    private List<Long> ids;

    /**
     * 客户名称
     */
    private String storeName;

    /**
     * 门店设计账号
     */
    private String accountName;

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 主订单编号
     */
    private String masterOrderNo;

    /**
     * 状态，0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)
     */
    private Integer status;
}
