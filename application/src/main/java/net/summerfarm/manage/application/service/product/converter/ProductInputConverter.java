package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.input.MarketBaseQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductPageInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductUpdateInput;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import net.summerfarm.manage.domain.product.param.query.MarketBaseQueryParam;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
public class ProductInputConverter {


    private ProductInputConverter() {
        // 无需实现
    }

    public static ProductPageQueryParam toProductPageQueryParam(ProductPageInput productPageInput) {
        if (productPageInput == null) {
            return null;
        }
        ProductPageQueryParam productPageQueryParam = new ProductPageQueryParam();
        productPageQueryParam.setSku(productPageInput.getSku());
        productPageQueryParam.setSkuList(productPageInput.getSkuList());
        productPageQueryParam.setName(productPageInput.getName());
        productPageQueryParam.setCategoryIds(productPageInput.getCategoryIds());
        productPageQueryParam.setSubType(productPageInput.getSubType());
        productPageQueryParam.setPageIndex(productPageInput.getPageIndex());
        productPageQueryParam.setPageSize(productPageInput.getPageSize());
        productPageQueryParam.setSortList(productPageInput.getSortList());
        productPageQueryParam.setOutdated(productPageInput.getOutdated());
        productPageQueryParam.setType(productPageInput.getType());
        productPageQueryParam.setGrandCategoryId(productPageInput.getGrandCategoryId());
        productPageQueryParam.setParentCategoryId(productPageInput.getParentCategoryId());
        productPageQueryParam.setPdNo(productPageInput.getPdNo());
        productPageQueryParam.setCategoryId(productPageInput.getCategoryId());
        productPageQueryParam.setBuyerId (productPageInput.getBuyerId ());
        return productPageQueryParam;
    }

    public static MarketBaseQueryParam toMarketBaseQueryParam(MarketBaseQueryInput input) {
        MarketBaseQueryParam marketBaseQueryParam = new MarketBaseQueryParam ();
        marketBaseQueryParam.setSpuTitleLike(input.getSpuTitleLike ());
        marketBaseQueryParam.setItemCodeLike(input.getItemCodeLike ());
        marketBaseQueryParam.setItemCode (input.getItemCode ());
        marketBaseQueryParam.setCharacters(input.getCharacters ());
        marketBaseQueryParam.setAdminId(input.getAdminId ());
        marketBaseQueryParam.setPageIndex(input.getPageIndex ());
        marketBaseQueryParam.setPageSize(input.getPageSize ());
        marketBaseQueryParam.setOutId(input.getPdId());

        return marketBaseQueryParam;
    }
    public static ProductsCommandParam inputToCommonParam(ProductUpdateInput input) {
        ProductsCommandParam param = new ProductsCommandParam();
        param.setPdId(input.getPdId());
        param.setCategoryId(input.getCategoryId());
        param.setBrandId(input.getBrandId());
        param.setPdName(input.getPdName());
        param.setPddetail(input.getPddetail());
        param.setDetailPicture(input.getDetailPicture());
        param.setAfterSaleTime(input.getAfterSaleTime());
        param.setAfterSaleType(input.getAfterSaleType());
        param.setAfterSaleUnit(input.getAfterSaleUnit());
        param.setCreateTime(input.getCreateTime());
        param.setOutdated(input.getOutdated());
        param.setStorageLocation(input.getStorageLocation());

        param.setPdNo(input.getPdNo());
        param.setOrigin(input.getOrigin());
        param.setStorageMethod(input.getStorageMethod());
        param.setSlogan(input.getSlogan());

        param.setOtherSlogan(input.getOtherSlogan());
        param.setPicturePath(input.getPicturePath());
        param.setRefundType(input.getRefundType());
        param.setQualityTime(input.getQualityTime());
        param.setQualityTimeUnit(input.getQualityTimeUnit());
        param.setWarnTime(input.getWarnTime());
        param.setCreateType(input.getCreateType());
        param.setCreator(input.getCreator());
        param.setRealName(input.getRealName());

        param.setCreateRemark(input.getCreateRemark());
        param.setAuditStatus(input.getAuditStatus());
        param.setAuditTime(input.getAuditTime());
        param.setProductIntroduction(input.getProductIntroduction());
        param.setAuditor(input.getAuditor());
        param.setQualityTimeType(input.getQualityTimeType());

        return param;
    }
}
