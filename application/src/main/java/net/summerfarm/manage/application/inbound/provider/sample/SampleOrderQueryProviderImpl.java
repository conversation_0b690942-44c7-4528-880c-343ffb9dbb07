package net.summerfarm.manage.application.inbound.provider.sample;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.sample.SampleOrderQueryProvider;
import net.summerfarm.client.req.sample.SampleOrderDeliveryDetailQueryReq;
import net.summerfarm.client.resp.sample.SampleOrderDeliveryInfoResp;
import net.summerfarm.manage.application.inbound.provider.sample.converter.SampleOrderConverter;
import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject;
import net.summerfarm.manage.domain.sampleApply.repository.SampleApplyQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 样品单查询<br/>
 * date: 2024/12/30 15:42<br/>
 *
 * <AUTHOR> />
 */
@DubboService
@Slf4j
public class SampleOrderQueryProviderImpl implements SampleOrderQueryProvider {

    @Resource
    private SampleApplyQueryRepository sampleApplyQueryRepository;

    @Override
    public DubboResponse<List<SampleOrderDeliveryInfoResp>> queryValidSampleOrderDeliveryDetail(@Valid SampleOrderDeliveryDetailQueryReq req) {
        List<SampleOrderFlatObject> flatObjectList = sampleApplyQueryRepository.queryValidSampleOrderDeliveryDetail(req.getSampleIdList());
        return DubboResponse.getOK(flatObjectList.stream().map(SampleOrderConverter::flatObjectToSampleOrderDeliveryInfoResp).collect(Collectors.toList()));
    }
}
