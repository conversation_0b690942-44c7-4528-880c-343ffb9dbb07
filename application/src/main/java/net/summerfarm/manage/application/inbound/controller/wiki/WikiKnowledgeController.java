package net.summerfarm.manage.application.inbound.controller.wiki;

import net.summerfarm.manage.facade.wiki.WikiKnowledgeFacade;
import net.summerfarm.manage.facade.wiki.dto.SearchResultItem;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Wiki知识库控制器
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/wiki")
public class WikiKnowledgeController {

    @Resource
    private WikiKnowledgeFacade wikiKnowledgeFacade;

    /**
     * 搜索Wiki知识库
     * @param text 搜索文本
     * @param datasetId 数据集ID（可选）
     * @param limit 返回结果限制（可选，默认5000）
     * @return 搜索结果
     */
    @GetMapping("/search")
    public CommonResult<List<SearchResultItem>> searchWiki(
            @RequestParam String text,
            @RequestParam(required = false) String datasetId,
            @RequestParam(required = false, defaultValue = "5000") Integer limit) {
        
        List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki(text, datasetId, limit);
        return CommonResult.ok(results);
    }

    /**
     * 简单搜索Wiki知识库
     * @param text 搜索文本
     * @return 搜索结果
     */
    @PostMapping("/search")
    public CommonResult<List<SearchResultItem>> searchWikiSimple(@RequestBody String text) {
        List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki(text);
        return CommonResult.ok(results);
    }
}
