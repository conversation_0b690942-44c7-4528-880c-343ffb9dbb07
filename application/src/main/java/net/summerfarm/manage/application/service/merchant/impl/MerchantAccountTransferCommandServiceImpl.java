package net.summerfarm.manage.application.service.merchant.impl;


import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantTransferCheckVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.service.merchant.MerchantAccountTransferCommandService;
import net.summerfarm.manage.application.service.merchant.MerchantAccountTransferQueryService;
import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantSubAccountCommandRepository;
import net.summerfarm.manage.domain.merchant.service.MerchantAccountTransferCommandDomainService;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;
import net.summerfarm.manage.application.inbound.controller.merchant.input.command.MerchantAccountTransferCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantAccountTransferAssembler;
import net.summerfarm.manage.facade.merchant.MerchantCancelFacade;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-10 14:07:22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantAccountTransferCommandServiceImpl implements MerchantAccountTransferCommandService {

    @Autowired
    private MerchantAccountTransferQueryService merchantAccountTransferQueryService;
    @Resource
    private MerchantCancelFacade merchantCancelFacade;
    @Autowired
    private MerchantAccountTransferCommandDomainService merchantAccountTransferCommandDomainService;
    @Autowired
    private MerchantSubAccountCommandRepository accountCommandRepository;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transfer(MerchantAccountTransferCommandInput input) {
        //交验主账号
        MerchantTransferCheckVO mainMerchant = merchantAccountTransferQueryService.check(input.getMId(), true);
        List<Long> transferMIds = input.getTransferMIds();
        for (Long transferMId : transferMIds) {
            MerchantTransferCheckVO check = merchantAccountTransferQueryService.check(transferMId, false);
            transfer(mainMerchant, check);
        }
        return true;
    }


    public void transfer(MerchantTransferCheckVO mainMerchant, MerchantTransferCheckVO transferMerchant) {
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setMId(transferMerchant.getMId());
        if (merchantCancelFacade.transferCancel(merchantCancelInputReq)) {
            //被迁移的主账号的mid 改为主账号的子账号
            accountCommandRepository.updateMain2Base(mainMerchant.getMId(), transferMerchant.getMId());
            merchantAccountTransferCommandDomainService.insert(buildCreateParam(mainMerchant, transferMerchant));
        }
    }

    public static MerchantAccountTransferCommandParam buildCreateParam(MerchantTransferCheckVO mainMerchant, MerchantTransferCheckVO transferMerchant) {
        MerchantAccountTransferCommandParam merchantAccountTransfer = new MerchantAccountTransferCommandParam();
        merchantAccountTransfer.setCreateTime(LocalDateTime.now());
        merchantAccountTransfer.setUpdateTime(LocalDateTime.now());
        merchantAccountTransfer.setMId(mainMerchant.getMId());
        merchantAccountTransfer.setMname(mainMerchant.getMname());
        merchantAccountTransfer.setTransferMId(transferMerchant.getMId());
        merchantAccountTransfer.setOperatorName(UserInfoHolder.getUserRealName());
        merchantAccountTransfer.setAreaNo((mainMerchant.getAreaNo() == null) ? 0L : mainMerchant.getAreaNo().longValue());
        merchantAccountTransfer.setAreaName(mainMerchant.getAreaName());
        merchantAccountTransfer.setAddr(transferMerchant.getProvince() + transferMerchant.getCity()
                + transferMerchant.getArea() + transferMerchant.getAddress());
        merchantAccountTransfer.setBdName(mainMerchant.getAdminName());
        merchantAccountTransfer.setTransferMname(transferMerchant.getMname());
        merchantAccountTransfer.setTransferBdName(transferMerchant.getAdminName());
        merchantAccountTransfer.setTransferPhone(transferMerchant.getPhone());
        merchantAccountTransfer.setPhone(mainMerchant.getPhone());
        return merchantAccountTransfer;
    }
}