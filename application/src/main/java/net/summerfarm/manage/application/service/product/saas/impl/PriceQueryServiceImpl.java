package net.summerfarm.manage.application.service.product.saas.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.product.saas.PriceQueryService;
import net.summerfarm.manage.application.service.product.saas.dto.MallPrice4SaasDTO;
import net.summerfarm.manage.common.enums.MajorDirectEnum;
import net.summerfarm.manage.common.enums.MajorPriceTypeEnum;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
@Service
public class PriceQueryServiceImpl implements PriceQueryService {
    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;
    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;

    public MallPrice4SaasDTO queryMallPriceInfo4Saas(Integer areaNo, String sku, Long adminId){
        MallPrice4SaasDTO reslut = new MallPrice4SaasDTO();
        reslut.setSkuCode(sku);
        AreaSkuEntity areaSku = areaSkuQueryRepository.selectValidAndOnSale (areaNo, sku);
        if (areaSku == null) {
            log.warn("sku已下架或者已删除,sku:{},areaNo:{}", sku, areaNo);
            return reslut;
        }
        reslut.setSkuId(areaSku.getSkuId());
        //报价单优先级：账期报价单、现结报价单
        MajorPriceEntity billMajorPrice = majorPriceQueryRepository.selectMajorPrice(adminId, MajorDirectEnum.PERIOD.getType(),areaNo,sku);
        MajorPriceEntity nowMajorPrice = majorPriceQueryRepository.selectMajorPrice(adminId, MajorDirectEnum.CASH.getType(),areaNo,sku);
        reslut.setValidType(1);

        if (billMajorPrice != null && MajorPriceTypeEnum.MALL_RELATED.contains (billMajorPrice.getPriceType())) {
            //商城价相关
            BigDecimal price = PriceCalculator.calculateMajorPriceByType (null, areaSku.getPrice(), billMajorPrice.getPriceAdjustmentValue (), billMajorPrice.getPriceType ());
            billMajorPrice.setPrice(price);
        }

        if (nowMajorPrice != null && MajorPriceTypeEnum.MALL_RELATED.contains (nowMajorPrice.getPriceType())) {
            //商城价相关
            BigDecimal price = PriceCalculator.calculateMajorPriceByType (null, areaSku.getPrice(), nowMajorPrice.getPriceAdjustmentValue (), nowMajorPrice.getPriceType ());
            nowMajorPrice.setPrice(price);
        }

        boolean isNowMajor = true;
        boolean majorExist = true;
        if (billMajorPrice == null && nowMajorPrice != null) {

        } else if (nowMajorPrice == null && billMajorPrice != null) {
            isNowMajor = false;
        } else if (billMajorPrice != null && nowMajorPrice != null) {
            isNowMajor = nowMajorPrice.getPrice().compareTo(billMajorPrice.getPrice()) > 0;
        } else {
            majorExist = false;
        }

        if (isNowMajor && majorExist) {
            reslut.setPrice(nowMajorPrice.getPrice());
            reslut.setValidTime(nowMajorPrice.getValidTime());
            reslut.setInvalidTime(nowMajorPrice.getInvalidTime());
            return reslut;
        } else if (!isNowMajor && majorExist) {
            reslut.setPrice(billMajorPrice.getPrice());
            reslut.setValidTime(billMajorPrice.getValidTime());
            reslut.setInvalidTime(billMajorPrice.getInvalidTime());
            return reslut;
        }
        reslut.setValidType(0);
        reslut.setPrice(areaSku.getPrice());
        // 如果没有创建报价或者都是商城价，使用原价
        return reslut;
    }
}
