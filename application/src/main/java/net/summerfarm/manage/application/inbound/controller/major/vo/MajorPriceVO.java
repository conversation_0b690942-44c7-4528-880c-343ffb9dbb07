package net.summerfarm.manage.application.inbound.controller.major.vo;

import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemByAreaListVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MajorPriceVO {

    /**
     * 报价单id
     */
    private Integer id;


    /**
     * 1是账期 2是现结
     */
    private Integer direct;

    /**
     * 运营大区
     */
    private Integer largeAreaNo;

    /**
     * 运营大区name
     */
    private String largeAreaName;


    /**
     * sku编号
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;


    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）
     */
    private Integer priceType;

    /**
     * 毛利率
     */
    private BigDecimal interestRate;

    /**
     * 毛利率的固定价
     */
    private BigDecimal fixedPrice;

    /**
     * 商城价浮动/加减值
     */
    private BigDecimal priceAdjustmentValue;


    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 上期价格
     */
    private MajorPriceLogVO priceLog;


    /**
     * 报价单的生效时间
     */
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 毛利率的成本价
     */
    private BigDecimal cost;


    /**
     * 商城售价
     */
    private BigDecimal salePrice;


    /**
     * 商城是否展示 0 展示 1 不展示 类似上下架
     */
    private Integer mallShow;


    /**
     * 状态：0=保存, 1=提交
     */
    private Integer status;

    /**
     * 性质 1:代销不入仓;2:代销入仓;3:经销;4:代仓;
     */
    private Integer subType;

    /**
     * 0下架 1上架
     */
    private Integer onSale;


    /**
     * 仓库(实际)库存
     */
    private Integer storeQuantity;

    /**
     * 库存仓-上架时指定
     */
    private Integer warehouseNo;

    /**
     * 大客户价格策略：金额
     */
    private BigDecimal majorRebateNumber;

    /**
     * 大客户价格策略类型：1 代表定额 2代表比例
     */
    private Integer majorRebateType;

    /**
     * 品类id
     */
    private Long categoryId;

    /**
     * 产地
     */
    private String origin;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商品id
     */
    private Long pdId;


}
