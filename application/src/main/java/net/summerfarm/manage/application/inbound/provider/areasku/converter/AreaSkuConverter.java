package net.summerfarm.manage.application.inbound.provider.areasku.converter;

import net.summerfarm.client.req.areasku.AreaSkuPriceCommandReq;
import net.summerfarm.client.resp.areasku.AreaSkuResp;
import net.summerfarm.manage.application.inbound.controller.product.vo.AreaSkuVO;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AreaSkuConverter {
    AreaSkuConverter INSTANCE = Mappers.getMapper(AreaSkuConverter.class);

    AreaSkuPriceDTO areaSkuPriceCommandReq2AreaSkuPriceDTO(AreaSkuPriceCommandReq req);

    List<AreaSkuResp> vo2AreaSkuRespList(List<AreaSkuEntity> areaSkuVOS);
}