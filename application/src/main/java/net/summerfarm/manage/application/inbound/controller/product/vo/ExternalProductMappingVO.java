package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Data
public class ExternalProductMappingVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 映射类型 1-sku 2-类目
	 */
	private Integer type;

	/**
	 * 鲜沐内部值
	 */
	private String internalValue;

	/**
	 * 外部平台值
	 */
	private String externalValue;



}