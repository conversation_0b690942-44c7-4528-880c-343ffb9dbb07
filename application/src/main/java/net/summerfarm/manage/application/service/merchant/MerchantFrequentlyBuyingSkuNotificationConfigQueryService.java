package net.summerfarm.manage.application.service.merchant;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryInput;

/**
 *
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
public interface MerchantFrequentlyBuyingSkuNotificationConfigQueryService {

    /**
     * @description: 新增
     * @return MerchantFrequentlyBuyingSkuNotificationConfigEntity
     **/
    PageInfo<MerchantFrequentlyBuyingSkuNotificationConfigEntity> getPage(MerchantFrequentlyBuyingSkuNotificationConfigQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    MerchantFrequentlyBuyingSkuNotificationConfigEntity getDetail(Long id);

    /**
     * @description: 特价活动提醒
     * @return:
     **/
    void notificationToMerchant(Long activityId);
}