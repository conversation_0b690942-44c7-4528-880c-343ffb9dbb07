package net.summerfarm.manage.application.service.account;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.AccountChangeVO;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;

import java.util.List;


/**
 * 多地址待审核
 */
public interface AccountChangeService {
    /**
     * 多地址待审核列表
     *
     * @param selectKeys
     * @return
     */
    PageInfo<AccountChangeVO> queryPage(MerchantQueryInput selectKeys);

    AccountChangeVO changeDetail(MerchantQueryInput selectKeys);


    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    List<AccountChangeEntity> getQueryInput(MerchantQueryInput input);
}
