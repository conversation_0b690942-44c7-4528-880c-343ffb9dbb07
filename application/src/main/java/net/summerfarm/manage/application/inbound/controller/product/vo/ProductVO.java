package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2024/1/25
 */
@Data
public class ProductVO implements Serializable {

    private String sku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目类型 4-鲜果 其他-非鲜果
     */
    private Integer categoryType;

    /**
     * 规格
     */
    private String weight;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 关键属性，和商城一致
     */
    private List<ProductsPropertyValueVO> keyValueList;


    /**
     * 同商城搜索页商品名称展示一致
     */
    private String mallSkuName;

    /**
     * 同商城搜索页商品图片展示一致
     */
    private String mallSkuPic;

    /**
     * sku集合
     */
    private List<InventoryDetailVO> inventoryDetailVOS;

    /**
     * pdNo
     */
    private String pdNo;

    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 发源地
     */
    private String origin;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 上新状态：0、待审核 1、上新完成 2、上新失败
     */
    private Integer createStatus;

    /**
     * SPU生命周期：-1、上新中 0、有效 1、已删除
     */
    private Integer outdated;

    /**
     * 销售属性
     */
    private List<ProductsPropertyVO> salePropertyList;

    /**
     * 品牌
     */
    private Integer brandId;

    /**
     * 商品描述
     */
    private String pddetail;

    /**
     * 详情图片
     */
    private String detailPicture;

    /**
     * 浏览次数
     */
    private Long viewCount;

    /**
     *
     */
    private Integer priority;

    /**
     * 从截单开始的售后时间？？
     */
    private Integer afterSaleTime;

    /**
     * 售后类型
     */
    private String afterSaleType;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 上架时间
     */
    private LocalDateTime createTime;

    /**
     * 下架时间
     */
    private LocalDateTime expireTime;

    /**
     * 仓储区域
     */
    private Integer storageLocation;

    /**
     *
     */
    private String storageMethod;

    /**
     *
     */
    private String slogan;

    /**
     *
     */
    private String otherSlogan;


    /**
     * 退款原因
     */
    private String refundType;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期时长单位
     */
    private String qualityTimeUnit;

    /**
     * 临保预警时长
     */
    private Integer warnTime;

    /**
     * 创建时间
     */
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 上新类型：0、平台 1、大客户
     */
    private Integer createType;

    /**
     * 商品实物名
     */
    private String realName;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 商品介绍信息
     */
    private String productIntroduction;

    /**
     * 操作人adminId
     */
    private Integer auditor;

    /**
     * 保质期时长类型, 0 固定时长, 1 到期时间
     */
    private Integer qualityTimeType;
}
