package net.summerfarm.manage.application.service.product.mall;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.inbound.controller.product.vo.*;

import java.util.List;

/**
 * @ClassName InventoryQueryService
 * @Description
 * <AUTHOR>
 * @Date 16:15 2024/4/30
 * @Version 1.0
 **/
public interface InventoryQueryService {

    /***
     * @author: lzh
     * @description: 商品校验-spu、sku编辑
     * @date: 2024/4/30 16:40
     * @param: [input]
     * @return: void
     **/
    void vaild(InventoryVaildInput input);

    /***
     * @author: lzh
     * @description: 商品基本信息查询
     * @date: 2024/5/8 16:00
     * @param: [input]
     * @return: net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO
     **/
    ProductVO queryInfo(ProductInfoInput input);

    /**
     * 查询sku基础信息
     *
     * <AUTHOR>
     * @date 2025/1/14 15:25
     */
    List<InventoryBaseVO> listSkuBaseInfo(InventoryBaseQueryInput input);

    /**
     * 查询运营大区售卖列表 - 大客户报价选品
     * @param input
     * @return
     */
    PageInfo<MarketItemByLargeAreaListVO> pageSkuByLargeArea(SkuByLargeAreaQueryInput input);

    /**
     * 分页查询商品sku信息
     *
     * <AUTHOR>
     * @date 2025/3/27 18:50
     * @param queryInput
     * @return com.github.pagehelper.PageInfo<net.summerfarm.manage.application.inbound.controller.product.vo.InventoryBaseVO>
     */
    PageInfo<InventoryBaseVO> pageQueryInventory(InventoryBaseQueryInput queryInput);

    /**
     * 查询待关联分类商品
     *
     * <AUTHOR>
     * @date 2025/3/27 18:50
     * @param input
     * @return java.util.List<net.summerfarm.manage.application.inbound.controller.product.vo.BackCategoryVO>
     */
    PageInfo<PendingAssociationProductVO> pagePendingAssociationProduct(PendingAssociationProductQueryInput input);

    /**
     * 待关联分类列表
     *
     * <AUTHOR>
     * @date 2025/3/27 18:50
     * @param input
     * @return java.util.List<net.summerfarm.manage.application.inbound.controller.product.vo.BackCategoryVO>
     */
    List<BackCategoryVO> listPendingAssociationCategory(PendingAssociationCategoryQueryInput input);
}
