package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ProductLabelValueInput
 * @Description
 * <AUTHOR>
 * @Date 10:43 2024/5/7
 * @Version 1.0
 **/
@Data
public class ProductLabelValueInput implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    @NotNull(message = "请填写标签SKU", groups = Add.class)
    private String sku;

    /**
     * 标签字段名
     */
    @NotNull(message = "请填写标签ID", groups = Add.class)
    private Long labelId;

    /**
     * 标签值
     */
    private Integer labelValue;

    /**
     * 标签字段
     */
    private String labelField;

    /**
     * 标签名称
     */
    private String labelName;
}
