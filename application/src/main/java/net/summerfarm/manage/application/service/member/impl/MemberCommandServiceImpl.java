package net.summerfarm.manage.application.service.member.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.area.AreaQueryService;
import net.summerfarm.manage.application.service.member.MemberCommandService;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.member.MemberCommandRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/1  14:36
 */
@Slf4j
@Service
public class MemberCommandServiceImpl implements MemberCommandService {
    @Resource
    AreaQueryRepository areaQueryRepository;
    @Resource
    MemberCommandRepository memberCommandRepository;

    @Override
    public Boolean calculateMemberGrade() {
        List<Area> areaList = areaQueryRepository.queryAllValidArea();

        for (Area area : areaList) {
            memberCommandRepository.calculateMemberGrade(area.getAreaNo(), area.getMemberRule());
        }

        return true;
    }
}
