package net.summerfarm.manage.application.inbound.controller.major.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MajorPriceMallShowBatchInput implements Serializable{
	/**
	 * 大客户id
	 */
	@NotNull(message = "大客户ID不能为空")
	private Integer adminId;
	/**
	 * 1是账期 2是现结
	 */
	@NotNull(message = "direct不能为空")
	private Integer direct;
	/**
	 * 商城是否展示 0 展示 1 不展示 类似上下架
	 */
	@NotNull(message = "商城是否展示不能为空")
	private Integer mallShow;
}