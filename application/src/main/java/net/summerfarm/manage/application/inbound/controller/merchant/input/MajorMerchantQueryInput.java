package net.summerfarm.manage.application.inbound.controller.merchant.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.List;

@Data
public class MajorMerchantQueryInput extends BasePageInput implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 大客户id
     */
    private Long adminId;
    /**
     * 要排除的集合列表
     */

    private List<Integer> excludeStatusList;

    /**
     * `status` 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店 4、拉黑 5、注销'
     */
    private Integer status;
    /**
     * 1 账期 2  现结
     */
    private Integer direct;

    /**
     * 客户类型：1-大客户，2-单店',
     */
    private Integer size;

    /**
     * 门店类型：0-直营店 1-加盟店 2-托管店 3-个人店 4-连锁店 5-未知',
     */
    private Integer type;
}
