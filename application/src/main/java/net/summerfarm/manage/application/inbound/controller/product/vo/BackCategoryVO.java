package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2024/11/18 16:40
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackCategoryVO {
    /**
     * 类目id
     */
    private Integer id;

    /**
     * 父类id
     */
    private Integer parentId;

    /**
     * 类目名称
     */
    private String category;

    /**
     * 标记位-过时的品类  1代表过时，商品被删除
     */
    private Integer outdated;

    /**
     * 1 全部,2乳制品,3非乳制品,4水果
     */
    private Integer type;
}
