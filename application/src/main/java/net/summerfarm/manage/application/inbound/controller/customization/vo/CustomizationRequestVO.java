package net.summerfarm.manage.application.inbound.controller.customization.vo;

import lombok.Data;
import net.summerfarm.manage.common.enums.CustomizationRequestStatusEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定制需求视图对象
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class CustomizationRequestVO {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 客户名称
     */
    private String storeName;

    /**
     * 门店设计账号
     */
    private String accountName;

    /**
     * 客户id
     */
    private Long mId;


    /**
     * 颜色数量
     */
    private Integer colorCount;

    /**
     * 提交日期-下单时间
     */
    private LocalDateTime designSubmitTime;

    /**
     * 设计参考图-客户上传
     */
    private String referenceImage;

    /**
     * logo原文件-客户上传
     */
    private String logoImage;

    /**
     * logo大小:小,中,大
     */
    private String logoSize;

    /**
     * 样杯-客户上传
     */
    private String sampleImage;

    /**
     * 设计效果图-设计师上传
     */
    private String designImage;

    /**
     * 状态，0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)
     */
    private Integer status;

    /**
     * 不通过原因
     */
    private String refuseReason;

    /**
     * 门店 / 用户备注
     */
    private String storeRemark;

    /**
     * 设计师备注
     */
    private String designerRemark;
    /**
     * 定制内容
     */
    private List<CustomizationRequestRelationOrderVO> relationOrderVOList;
}
