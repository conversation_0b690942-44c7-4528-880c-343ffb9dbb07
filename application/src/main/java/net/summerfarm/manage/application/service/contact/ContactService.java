package net.summerfarm.manage.application.service.contact;

import net.summerfarm.manage.application.inbound.controller.contact.input.ContactFrequencyQueryInput;
import net.summerfarm.manage.application.inbound.controller.contact.input.ContactInsertInput;
import net.summerfarm.manage.application.inbound.controller.contact.vo.ContactVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/13 17:13:55
 */
public interface ContactService {

    /**
     * 查询联系人频率
     * @param input
     * @return
     */
    ContactVO queryFrequencyContact(ContactFrequencyQueryInput input);


    void addContactWithoutAudit(ContactInsertInput input);
}
