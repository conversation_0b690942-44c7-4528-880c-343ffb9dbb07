package net.summerfarm.manage.application.inbound.controller.job.assembler;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.summerfarm.manage.application.inbound.controller.job.input.command.CrmJobMerchantDetailCommandInput;
import net.summerfarm.manage.domain.job.dto.MerchantJobDto;
import net.summerfarm.manage.domain.job.param.command.CrmJobMerchantDetailCommandParam;
import net.xianmu.jobsdk.model.dto.CrmJobMerchantDetailDto;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-18 15:57:19
 */
public class CrmJobMerchantDetailAssembler {

    private CrmJobMerchantDetailAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------

    public static CrmJobMerchantDetailCommandParam buildCreateParam(CrmJobMerchantDetailCommandInput crmJobMerchantDetailCommandInput) {
        if (crmJobMerchantDetailCommandInput == null) {
            return null;
        }
        CrmJobMerchantDetailCommandParam crmJobMerchantDetailCommandParam = new CrmJobMerchantDetailCommandParam();
        crmJobMerchantDetailCommandParam.setId(crmJobMerchantDetailCommandInput.getId());
        crmJobMerchantDetailCommandParam.setCreateTime(crmJobMerchantDetailCommandInput.getCreateTime());
        crmJobMerchantDetailCommandParam.setUpdateTime(crmJobMerchantDetailCommandInput.getUpdateTime());
        crmJobMerchantDetailCommandParam.setJobId(crmJobMerchantDetailCommandInput.getJobId());
        crmJobMerchantDetailCommandParam.setMId(crmJobMerchantDetailCommandInput.getMId());
        crmJobMerchantDetailCommandParam.setStatus(crmJobMerchantDetailCommandInput.getStatus());
        crmJobMerchantDetailCommandParam.setMerchantProductList(crmJobMerchantDetailCommandInput.getMerchantProductList());
        crmJobMerchantDetailCommandParam.setFollowUpRecordId(crmJobMerchantDetailCommandInput.getFollowUpRecordId());
        crmJobMerchantDetailCommandParam.setMerchantProductCnt(crmJobMerchantDetailCommandInput.getMerchantProductCnt());
        crmJobMerchantDetailCommandParam.setClaimingStatus(crmJobMerchantDetailCommandInput.getClaimingStatus());
        crmJobMerchantDetailCommandParam.setClaimingTime(crmJobMerchantDetailCommandInput.getClaimingTime());
        crmJobMerchantDetailCommandParam.setCompleteTime(crmJobMerchantDetailCommandInput.getCompleteTime());
        crmJobMerchantDetailCommandParam.setOrderNo(crmJobMerchantDetailCommandInput.getOrderNo());
        crmJobMerchantDetailCommandParam.setCategoryIdList(crmJobMerchantDetailCommandInput.getCategoryIdList());
        return crmJobMerchantDetailCommandParam;
    }


    public static CrmJobMerchantDetailCommandParam buildUpdateParam(CrmJobMerchantDetailCommandInput crmJobMerchantDetailCommandInput) {
        if (crmJobMerchantDetailCommandInput == null) {
            return null;
        }
        CrmJobMerchantDetailCommandParam crmJobMerchantDetailCommandParam = new CrmJobMerchantDetailCommandParam();
        crmJobMerchantDetailCommandParam.setId(crmJobMerchantDetailCommandInput.getId());
        crmJobMerchantDetailCommandParam.setCreateTime(crmJobMerchantDetailCommandInput.getCreateTime());
        crmJobMerchantDetailCommandParam.setUpdateTime(crmJobMerchantDetailCommandInput.getUpdateTime());
        crmJobMerchantDetailCommandParam.setJobId(crmJobMerchantDetailCommandInput.getJobId());
        crmJobMerchantDetailCommandParam.setMId(crmJobMerchantDetailCommandInput.getMId());
        crmJobMerchantDetailCommandParam.setStatus(crmJobMerchantDetailCommandInput.getStatus());
        crmJobMerchantDetailCommandParam.setMerchantProductList(crmJobMerchantDetailCommandInput.getMerchantProductList());
        crmJobMerchantDetailCommandParam.setFollowUpRecordId(crmJobMerchantDetailCommandInput.getFollowUpRecordId());
        crmJobMerchantDetailCommandParam.setMerchantProductCnt(crmJobMerchantDetailCommandInput.getMerchantProductCnt());
        crmJobMerchantDetailCommandParam.setClaimingStatus(crmJobMerchantDetailCommandInput.getClaimingStatus());
        crmJobMerchantDetailCommandParam.setClaimingTime(crmJobMerchantDetailCommandInput.getClaimingTime());
        crmJobMerchantDetailCommandParam.setCompleteTime(crmJobMerchantDetailCommandInput.getCompleteTime());
        crmJobMerchantDetailCommandParam.setOrderNo(crmJobMerchantDetailCommandInput.getOrderNo());
        crmJobMerchantDetailCommandParam.setCategoryIdList(crmJobMerchantDetailCommandInput.getCategoryIdList());
        return crmJobMerchantDetailCommandParam;
    }


// ------------------------------- response ----------------------------

    public static List<MerchantJobDto> toMerchantJobDtoList(List<CrmJobMerchantDetailDto> crmJobMerchantDetailDtoList) {
        if (crmJobMerchantDetailDtoList == null) {
            return Collections.emptyList();
        }
        List<MerchantJobDto> merchantJobDtoList = new ArrayList<>();
        for (CrmJobMerchantDetailDto crmJobMerchantDetailDto : crmJobMerchantDetailDtoList) {
            merchantJobDtoList.add(toMerchantJobDto(crmJobMerchantDetailDto));
        }
        return merchantJobDtoList;
    }

    public static MerchantJobDto toMerchantJobDto(CrmJobMerchantDetailDto crmJobMerchantDetailDto) {
        if (crmJobMerchantDetailDto == null) {
            return null;
        }
        MerchantJobDto merchantJobDto = new MerchantJobDto();
        merchantJobDto.setId(crmJobMerchantDetailDto.getId());
        merchantJobDto.setJobName(crmJobMerchantDetailDto.getJobName());
        merchantJobDto.setDescription(crmJobMerchantDetailDto.getDescription());
        merchantJobDto.setType(crmJobMerchantDetailDto.getType());
        merchantJobDto.setStartTime(crmJobMerchantDetailDto.getStartTime());
        merchantJobDto.setEndTime(crmJobMerchantDetailDto.getEndTime());
        merchantJobDto.setJobId(crmJobMerchantDetailDto.getJobId());
        merchantJobDto.setMId(crmJobMerchantDetailDto.getMId());
        merchantJobDto.setStatus(crmJobMerchantDetailDto.getStatus());
        merchantJobDto.setAchievedStatus(crmJobMerchantDetailDto.getAchievedStatus());
        merchantJobDto.setClaimingStatus(crmJobMerchantDetailDto.getClaimingStatus());
        merchantJobDto.setClaimingTime(crmJobMerchantDetailDto.getClaimingTime());
        merchantJobDto.setCompleteTime(crmJobMerchantDetailDto.getCompleteTime());
        merchantJobDto.setOrderNoList(crmJobMerchantDetailDto.getOrderNoList());
        merchantJobDto.setMerchantProductList(crmJobMerchantDetailDto.getMerchantProductList());
        merchantJobDto.setCategoryList(crmJobMerchantDetailDto.getCategoryList());
        merchantJobDto.setRewardType(crmJobMerchantDetailDto.getRewardType());
        merchantJobDto.setRewardValue(crmJobMerchantDetailDto.getRewardValue());
        merchantJobDto.setCompletionType(crmJobMerchantDetailDto.getCompletionType());
        merchantJobDto.setCompletionValue(crmJobMerchantDetailDto.getCompletionValue());
        merchantJobDto.setCreateTime(crmJobMerchantDetailDto.getCreateTime());
        merchantJobDto.setUpdateTime(crmJobMerchantDetailDto.getUpdateTime());
        merchantJobDto.setSkuList(crmJobMerchantDetailDto.getMerchantProductList() == null ? null : JSON.parseArray(crmJobMerchantDetailDto.getMerchantProductList(), String.class));
        merchantJobDto.setCategoryIdList(parseCategoryList(crmJobMerchantDetailDto.getCategoryList()));
        merchantJobDto.setOrderTypeList(JSON.parseArray(crmJobMerchantDetailDto.getOrderTypeList(), Integer.class));
        return merchantJobDto;
    }

    private static List<Integer> parseCategoryList(String categoryList) {
        if (StringUtils.isBlank(categoryList)) {
            return null;
        }
        return JSON.parseObject(categoryList, new TypeReference<List<List<Integer>>>() {
                })
                .stream()
                .map(data -> CollectionUtil.get(data, -1)).distinct().collect(Collectors.toList());
    }
}
