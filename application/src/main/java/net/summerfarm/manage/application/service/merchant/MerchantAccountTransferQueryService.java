package net.summerfarm.manage.application.service.merchant;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.input.command.MerchantAccountTransferCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantTransferCheckInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantTransferCheckVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantAccountTransferQueryInput;

/**
 *
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
public interface MerchantAccountTransferQueryService {

    /**
     * @description: 新增
     * @return MerchantAccountTransferEntity
     **/
    PageInfo<MerchantAccountTransferEntity> getPage(MerchantAccountTransferQueryInput input);

    MerchantAccountTransferEntity getDetail(Long id);

    MerchantTransferCheckVO check(MerchantTransferCheckInput checkInput);

    MerchantTransferCheckVO check(Long mid, Boolean isMainMerchant);


}