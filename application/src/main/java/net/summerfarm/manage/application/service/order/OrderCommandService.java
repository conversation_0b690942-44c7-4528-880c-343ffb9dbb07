package net.summerfarm.manage.application.service.order;

import net.summerfarm.manage.common.input.order.AutoOrderSendCouponDTO;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/13 17:08:44
 */
public interface OrderCommandService {

    /***
     * @author: lzh
     * @description: 自动确认收货
     * @date: 2024/1/17 18:18
     * @param: []
     * @return: void
     **/
    void autoConfirm(String orderNo);

    /***
     * @author: lzh
     * @description: 自动确认收货真正执行逻辑
     * @date: 2024/1/23 19:13
     * @param: [autoConfirmOrder, merchant, ordersEntity, autoOrderSendCouponDTOS]
     * @return: void
     **/
    void startAutoConfirm(DeliveryPlanFlatObject autoConfirmOrder, MerchantEntity merchant, OrdersEntity ordersEntity,
                          List<AutoOrderSendCouponDTO> autoOrderSendCouponDTOS);

    /***
     * @author: lzh
     * @description: 未到货售后删除参与满返订单优惠券（支付完成后发放）
     * @date: 2024/1/17 18:18
     * @param: []
     * @return: void
     **/
    void deleteOrderFullReturnCoupon(String orderNo, String sku, Long mId);

    /**
    * @description 售后订单自动完成确认收货
    * @params [orderNo]
    * @return void
    * <AUTHOR>
    * @date  2024/12/3 17:12
    */
    void autoCompleteReceipt(String orderNo);
}
