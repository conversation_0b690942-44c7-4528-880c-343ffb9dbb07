package net.summerfarm.manage.application.service.area.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName MemberDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 11:33 2024/1/23
 * @Version 1.0
 **/
@Data
public class MemberDTO implements Serializable {

    /**
     *等级
     */
    private Integer grade;

    /**
     *门槛
     */
    private BigDecimal threshold;

    /**
     *极速退款额度
     */
    private BigDecimal refundAmount;

    /**
     *超时加单次数
     */
    private Integer outTimes;
}
