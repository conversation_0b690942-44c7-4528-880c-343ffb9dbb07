package net.summerfarm.manage.application.service.job.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.manage.application.inbound.controller.job.assembler.CrmJobMerchantDetailAssembler;
import net.summerfarm.manage.application.service.job.CrmJobMerchantDetailCommandService;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.MerchantCouponCommandParam;
import net.summerfarm.manage.domain.coupon.repository.CouponQueryRepository;
import net.summerfarm.manage.domain.coupon.repository.MerchantCouponCommandRepository;
import net.summerfarm.manage.domain.delivery.entity.DeliveryPlanEntity;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.job.dto.MerchantJobDto;
import net.summerfarm.manage.domain.merchant.repository.MerchantCommandRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.repository.AfterSaleOrderQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrdersQueryRepository;
import net.summerfarm.manage.domain.product.repository.CategoryQueryRepository;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.jobsdk.enums.CrmJobEnum;
import net.xianmu.jobsdk.enums.CrmJobMerchantDetailEnum;
import net.xianmu.jobsdk.mapper.CrmJobMerchantDetailMapper;
import net.xianmu.jobsdk.mapper.CrmJobRewardRecordMapper;
import net.xianmu.jobsdk.model.dto.CrmJobMerchantDetailDto;
import net.xianmu.jobsdk.model.po.CrmJobMerchantDetail;
import net.xianmu.jobsdk.model.po.CrmJobRewardRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-18 14:46:45
 */
@Service
@Slf4j
public class CrmJobMerchantDetailCommandServiceImpl implements CrmJobMerchantDetailCommandService {

    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;
    @Resource
    private OrdersQueryRepository ordersQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private CouponQueryRepository couponQueryRepository;
    @Resource
    private MerchantCouponCommandRepository merchantCouponCommandRepository;
    @Resource
    private CrmJobRewardRecordMapper crmJobRewardRecordMapper;
    @Resource
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;
    @Resource
    private AfterSaleOrderQueryRepository afterSaleOrderQueryRepository;
    @Resource
    private CategoryQueryRepository categoryQueryRepository;

    @Override
    public void finishJobAfterPaymentSucceeded(String orderNo) {
        OrdersEntity ordersEntity = ordersQueryRepository.selectByOrderNo(orderNo);
        List<CrmJobMerchantDetailDto> claimedJobList = crmJobMerchantDetailMapper.getClaimedPageByMidAndPayTime(ordersEntity.getMId(), ordersEntity.getOrderTime());
        if (CollectionUtil.isEmpty(claimedJobList)) {
            log.info("该门店{}暂无生效中、并且已领取的任务", ordersEntity.getMId());
            return;
        }

        // 参数转换
        List<MerchantJobDto> jobList = CrmJobMerchantDetailAssembler.toMerchantJobDtoList(claimedJobList);

        // 获取任务的订单项
        final List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.selectByOrderNo(ordersEntity.getOrderNo());
        for (MerchantJobDto jobDto : jobList) {
            this.handleJobAfterPaymentSucceeded(jobDto, ordersEntity, orderItemEntities);
        }
    }


    /**
     * 支付完成后处理任务
     * @param jobDto
     * @param ordersEntity
     * @param orderItemEntities
     */
    private void handleJobAfterPaymentSucceeded(MerchantJobDto jobDto, OrdersEntity ordersEntity, List<OrderItemEntity> orderItemEntities) {
        log.info("开始校验任务是否完成：jobDto：{}", JSON.toJSONString(jobDto));
        // 1. 任务类型校验
        if (!checkOrderTypeRule(jobDto, ordersEntity)) {
            log.info("任务未达成。该任务不支持此类订单！OrdersEntity：{}, job:{}", JSON.toJSONString(ordersEntity), JSON.toJSONString(jobDto));
            return ;
        }

        // 2. 判断具体的任务规则
        boolean isAchieved = false;
        if (CrmJobEnum.Type.PRODUCT_ORDER.getCode().equals(jobDto.getType())) {
            isAchieved = this.checkProductExpansionJobRule(orderItemEntities, jobDto, ordersEntity);
        } else if (CrmJobEnum.Type.CATEGORY_ORDER.getCode().equals(jobDto.getType())) {
            isAchieved = this.checkOrderJobRule(orderItemEntities, jobDto, ordersEntity);
        }

        // 3. 任务达成，更新任务状态、
        this.updateMerchantJobDetailAfterPaymentSucceeded(isAchieved, jobDto, ordersEntity);

    }


    private void updateMerchantJobDetailAfterPaymentSucceeded(boolean isAchieved, MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        if (!isAchieved) {
            log.info("任务未达成。任务详情jobDto：{}， ordersEntity：{}", jobDto, ordersEntity);
            return;
        }
        log.info("任务达成，开始更新任务状态。原任务详情jobDto：{}， ordersEntity：{}", jobDto, ordersEntity);
        List<String> orderNoList = StringUtils.isBlank(jobDto.getOrderNoList()) ? new ArrayList<>() : JSON.parseArray(jobDto.getOrderNoList(), String.class);
        orderNoList.add(ordersEntity.getOrderNo());
        CrmJobMerchantDetail update = new CrmJobMerchantDetail();
        update.setId(jobDto.getId());
        update.setStatus(CrmJobMerchantDetailEnum.TaskCompletionStatus.COMPLETED_UNAWARDED.getCode());
        update.setOrderNoList(JSON.toJSONString(orderNoList));
        update.setCompleteTime(ordersEntity.getOrderTime());
        crmJobMerchantDetailMapper.updateSelectiveById(update);
    }



    /**
     * 校验下单任务
     *
     * @param orderItemEntities
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkOrderJobRule(List<OrderItemEntity> orderItemEntities, MerchantJobDto jobDto, OrdersEntity ordersEntity) {

        // 获取所有的未到货售后
        List<AfterSaleOrderEntity> afterSaleOrderEntities = afterSaleOrderQueryRepository.getNotDeliveryAfterSaleInfoByOrderNo(ordersEntity.getOrderNo());

        // 获取每个sku未到货售后的数量
        Map<String, Integer> skuAfterSaleMap = afterSaleOrderEntities.stream()
                .collect(Collectors.groupingBy(AfterSaleOrderEntity::getSku,
                        Collectors.summingInt(AfterSaleOrderEntity::getQuantity)));
        List<Integer> sourceCategoryIdList = jobDto.getCategoryIdList();
        BigDecimal countPrice = BigDecimal.ZERO;

        if (CollectionUtil.isEmpty(sourceCategoryIdList) || sourceCategoryIdList.contains(-1)) {
            countPrice = orderItemEntities.stream()
                    .map(item -> {
                        Integer afterSaleQuantity = skuAfterSaleMap.get(item.getSku());
                        Integer quantity = item.getAmount();
                        if (afterSaleQuantity != null) {
                            quantity = quantity - afterSaleQuantity;
                        }
                        return item.getOriginalPrice().multiply(BigDecimal.valueOf(quantity));
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            // 指定类目
            // 解析获得所有的三级类目
            List<Integer> categoryIdList = categoryQueryRepository.getSubLevelCategoryIds(sourceCategoryIdList);
            countPrice = orderItemEntities.stream()
                    .filter(item -> categoryIdList.contains(item.getCategoryId()))
                    .map(item -> {
                        Integer afterSaleQuantity = skuAfterSaleMap.get(item.getSku());
                        Integer quantity = item.getAmount();
                        if (afterSaleQuantity != null) {
                            quantity = quantity - afterSaleQuantity;
                        }
                        return item.getOriginalPrice().multiply(BigDecimal.valueOf(quantity));
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), countPrice.toString());
    }

    /**
     * 校验拓品任务
     *
     * @param orderItemEntities
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkProductExpansionJobRule(List<OrderItemEntity> orderItemEntities, MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        List<String> skuList = jobDto.getSkuList();
        List<AfterSaleOrderEntity> afterSaleOrderEntities = afterSaleOrderQueryRepository.getNotDeliveryAfterSaleInfoByOrderNo(ordersEntity.getOrderNo());
        // 获取每个sku未到货售后的金额
        Map<String, Integer> skuAfterSalePriceMap = afterSaleOrderEntities.stream()
                .collect(Collectors.groupingBy(AfterSaleOrderEntity::getSku,
                        Collectors.summingInt(AfterSaleOrderEntity::getQuantity)));

        for (OrderItemEntity orderItemEntity : orderItemEntities) {
            // 比较每一个满足任务配置的订单项
            if (skuList.contains(orderItemEntity.getSku())) {
                // 去除sku发生未到货售后的部分
                Integer amount = orderItemEntity.getAmount();
                Integer afterSaleAmount = skuAfterSalePriceMap.get(orderItemEntity.getSku());
                if(afterSaleAmount != null) {
                    amount = amount - afterSaleAmount;
                }

                if(checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), String.valueOf(amount))){
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 判断任务所支持的订单类型
     *
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkOrderTypeRule(MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        List<Integer> orderTypeList = jobDto.getOrderTypeList();
        if (CollectionUtil.isEmpty(orderTypeList)) {
            log.info("任务支持的订单类型为空！");
            return false;
        }

        return orderTypeList.contains(ordersEntity.getType());
    }

    private boolean checkJobCompletionRule(Integer completionType, String completionValue, String sourceData) {
        log.info("开始规则比对：规则类型:{}, 规则的阈值:{}, 订单数据：{}", completionType, completionValue, sourceData);
        // 商品件数
        if (CrmJobEnum.CompletionCriteriaType.SINGLE_ORDER_PRODUCT_QUANTITY.getCode().equals(completionType)) {
            return Integer.parseInt(sourceData) >= Integer.parseInt(completionValue);
        } else if (CrmJobEnum.CompletionCriteriaType.SINGLE_ORDER_AMOUNT.getCode().equals(completionType)) {
            // 下单金额
            BigDecimal orderAmount = new BigDecimal(sourceData);
            BigDecimal targetAmount = new BigDecimal(completionValue);
            return orderAmount.compareTo(targetAmount) >= 0;
        }
        return false;
    }


    @Override
    public void updateJobAfterSale(String afterSaleNo, String orderNo) {
        OrdersEntity ordersEntity = ordersQueryRepository.selectByOrderNo(orderNo);
        List<CrmJobMerchantDetailDto> achievedList = crmJobMerchantDetailMapper.getAchievedList(ordersEntity.getMId());
        if (CollectionUtil.isEmpty(achievedList)) {
            log.info("该门店{}暂无已完成的任务", ordersEntity.getMId());
            return;
        }

        List<MerchantJobDto> jobList = CrmJobMerchantDetailAssembler.toMerchantJobDtoList(achievedList);

        for (MerchantJobDto dto : jobList) {
            List<String> orderNoList = JSON.parseArray(dto.getOrderNoList(), String.class);
            if (orderNoList.contains(orderNo)) {
                // 如果命中，那么需要再次判断任务是否达标
                this.handleJobAfterSale(dto, ordersEntity);
            }
        }

    }

    private void handleJobAfterSale(MerchantJobDto dto, OrdersEntity ordersEntity) {
        // 获取任务的订单项
        final List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.selectByOrderNo(ordersEntity.getOrderNo());
        // 判断售后了是否依然达标
        boolean isAchieved = false;
        if (CrmJobEnum.Type.PRODUCT_ORDER.getCode().equals(dto.getType())) {
            isAchieved = this.checkProductExpansionJobRule(orderItemEntities, dto, ordersEntity);
        } else if (CrmJobEnum.Type.CATEGORY_ORDER.getCode().equals(dto.getType())) {
            isAchieved = this.checkOrderJobRule(orderItemEntities, dto, ordersEntity);
        }
        this.updateMerchantJobDetailAfterSale(isAchieved, dto, ordersEntity);
    }

    private void updateMerchantJobDetailAfterSale(boolean isAchieved, MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        if (isAchieved) {
            log.info("发生未到货退款后依然满足达成任务条件，不做处理");
            return;
        }
        log.info("发生未到货退款后不再满足达成任务条件。原任务详情：jobDto：{}， ordersEntity：{}", jobDto, ordersEntity);
        List<String> orderNoList = StringUtils.isBlank(jobDto.getOrderNoList()) ? new ArrayList<>() : JSON.parseArray(jobDto.getOrderNoList(), String.class);
        orderNoList.remove(ordersEntity.getOrderNo());
        CrmJobMerchantDetail update = new CrmJobMerchantDetail();
        update.setId(jobDto.getId());
        if (CollectionUtil.isEmpty(orderNoList)) {
            update.setStatus(CrmJobMerchantDetailEnum.TaskCompletionStatus.UNCOMPLETED.getCode());
        }
        update.setOrderNoList(JSON.toJSONString(orderNoList));
        crmJobMerchantDetailMapper.updateSelectiveById(update);
    }

    @Override
    public void awardRewardAfterDelivery(String orderNo) {
        OrdersEntity ordersEntity = ordersQueryRepository.selectByOrderNo(orderNo);
        List<CrmJobMerchantDetailDto> achievedList = crmJobMerchantDetailMapper.getAchievedList(ordersEntity.getMId());
        if (CollectionUtil.isEmpty(achievedList)) {
            log.info("【奖励发放】该门店{}暂无已完成的任务", ordersEntity.getMId());
            return;
        }
        List<MerchantJobDto> jobList = CrmJobMerchantDetailAssembler.toMerchantJobDtoList(achievedList);
        for (MerchantJobDto dto : jobList) {
            this.handleJobForAfterDelivery(dto, ordersEntity);
        }
    }


    /**
     *
     * @param jobDto
     * @param ordersEntity
     */
    public void handleJobForAfterDelivery(MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        log.info("开始判断任务是否能发奖。jobDto:{}, ordersEntity:{}", JSON.toJSONString(jobDto), JSON.toJSONString(ordersEntity));
        if (!this.checkAwardRewardRule(jobDto, ordersEntity)) {
            log.info("暂未达到发奖规则!");
            return;
        }

        try {
            CrmJobMerchantDetailCommandServiceImpl bean = SpringContextUtil.getBean("crmJobMerchantDetailCommandServiceImpl", CrmJobMerchantDetailCommandServiceImpl.class);
            bean.handleJobForAwardReward(jobDto, ordersEntity);
        } catch (Exception e) {
            log.info("履约完成发奖失败! 任务详情：jobDto：{}， 发奖订单：ordersEntity：{}", jobDto, ordersEntity, e);
        }

    }


    /**
     * 判断是否达到奖励发放的条件
     *
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkAwardRewardRule(MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        List<String> orderNoList = JSON.parseArray(jobDto.getOrderNoList(), String.class);
        if (orderNoList.contains(ordersEntity.getOrderNo())) {
            log.info("履约的订单命中任务，开始判断能否达标发奖。jobDto:{}, ordersEntity:{}", JSON.toJSONString(jobDto), JSON.toJSONString(ordersEntity));
            // 普通订单如果在任务的已完成列表，即可发放奖励
            if (!OrderTypeEnum.TIMING.getId().equals(ordersEntity.getType())) {
                log.info("订单履约完成，任务达标。jobDto：{}, ordersEntity:{}", JSON.toJSONString(jobDto), JSON.toJSONString(ordersEntity));
                return true;
            } else {
                // 省心送订单判断累计履约的量是否达标

                // 1. 获取省心送订单项
                List<OrderItemEntity> entities = orderItemQueryRepository.selectByOrderNo(ordersEntity.getOrderNo());
                if (CollectionUtil.isEmpty(entities)) {
                    log.info("定单项不存在!ordersEntity:{}", JSON.toJSONString(ordersEntity));
                    return false;
                }
                // 省心送只有一个订单项
                OrderItemEntity itemEntity = entities.get(0);
                if (itemEntity.getPrice() == null) {
                    log.info("定单项金额异常!订单项:{}", JSON.toJSONString(itemEntity));
                    return false;
                }

                // 2. 获取已经配送的所有配送计划
                List<DeliveryPlanEntity> planEntityList = deliveryPlanQueryRepository.getDeliveryPlanByOrderNo(ordersEntity.getOrderNo());
                final int sum = planEntityList.stream().mapToInt(DeliveryPlanEntity::getQuantity).sum();

                // 3. 判断已履约的累计
                boolean isAchieved = false;
                if (CrmJobEnum.Type.PRODUCT_ORDER.getCode().equals(jobDto.getType())) {
                    isAchieved = this.checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), String.valueOf(sum));
                } else if (CrmJobEnum.Type.CATEGORY_ORDER.getCode().equals(jobDto.getType())) {
                    BigDecimal sumPrice = itemEntity.getPrice().multiply(BigDecimal.valueOf(sum));
                    isAchieved = this.checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), String.valueOf(sumPrice));
                }
                log.info("判断省心送订单能否完成任务结果：{}。ordersEntity：{}", isAchieved, JSON.toJSONString(ordersEntity));
                return isAchieved;
            }
        }
        return false;
    }


    /**
     * 加锁发奖，避免重复发放
     *
     * @param jobDto
     * @param ordersEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleJobForAwardReward(MerchantJobDto jobDto, OrdersEntity ordersEntity) {
        log.info("履约完成开始发奖。任务详情：jobDto：{}， 发奖订单：ordersEntity：{}", jobDto, ordersEntity);
        String rewardValue = jobDto.getRewardValue();
        Integer couponId = Integer.valueOf(rewardValue);
        CouponEntity coupon = couponQueryRepository.selectById(Long.valueOf(couponId));
        if (coupon == null) {
            throw new BizException("任务奖励发放失败,优惠券" + rewardValue + "不存在!");
        }

        CrmJobMerchantDetail update = new CrmJobMerchantDetail();
        update.setId(jobDto.getId());
        update.setStatus(CrmJobMerchantDetailEnum.TaskCompletionStatus.AWARD_ISSUED.getCode());
        crmJobMerchantDetailMapper.updateSelectiveById(update);
        CrmJobRewardRecord rewardRecord = new CrmJobRewardRecord();
        rewardRecord.setJobDetailId(jobDto.getId());
        rewardRecord.setJobId(jobDto.getJobId());
        rewardRecord.setMId(jobDto.getMId());
        rewardRecord.setRewardType(jobDto.getRewardType());
        rewardRecord.setOrderNoList(JSON.toJSONString(Collections.singletonList(ordersEntity.getOrderNo())));
        rewardRecord.setRewardValue(rewardValue);
        rewardRecord.setRewardAmount(coupon.getMoney());
        crmJobRewardRecordMapper.insertSelective(rewardRecord);
        // 查看该优惠劵在对应的时间属于哪个发放设置
        int couponType = coupon.getType();
        LocalDateTime validDate = null;
        LocalDateTime startTime = null;
        //0指固定时间间隔到期
        if (couponType == 0) {
            int validTime = coupon.getVaildTime();
            validDate = LocalDateTime.of(LocalDate.now().plusDays(validTime), LocalTime.of(23, 59, 59));
            startTime = coupon.getStartTime() == null ? LocalDateTime.now() : LocalDateTime.now().plusDays(coupon.getStartTime());
        } //1固定时间点到期
        else if (couponType == 1) {
            validDate = coupon.getVaildDate();
            startTime = coupon.getStartDate() == null ? LocalDateTime.now() : coupon.getStartDate();
        }
        MerchantCouponCommandParam merchantCoupon = new MerchantCouponCommandParam(jobDto.getMId(), couponId, validDate, "任务达成-系统自动发放", startTime, 0);
        merchantCouponCommandRepository.insertSelective(merchantCoupon);
    }
}