package net.summerfarm.manage.application.service.searchSynonym.impl;


import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryCommandService;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryQueryRepository;
import net.summerfarm.manage.domain.searchSynonym.service.ProductSearchSynonymDictionaryCommandDomainService;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.command.ProductSearchSynonymDictionaryCommandInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.assembler.ProductSearchSynonymDictionaryAssembler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductSearchSynonymDictionaryCommandServiceImpl implements ProductSearchSynonymDictionaryCommandService {

    @Autowired
    private ProductSearchSynonymDictionaryCommandDomainService productSearchSynonymDictionaryCommandDomainService;


    @Override
    public ProductSearchSynonymDictionaryEntity insert(ProductSearchSynonymDictionaryCommandInput input) {
        ProductSearchSynonymDictionaryCommandParam param = ProductSearchSynonymDictionaryAssembler.buildCreateParam(input);
        return productSearchSynonymDictionaryCommandDomainService.insert(param);
    }


    @Override
    public int update(ProductSearchSynonymDictionaryCommandInput input) {
        ProductSearchSynonymDictionaryCommandParam param = ProductSearchSynonymDictionaryAssembler.buildUpdateParam(input);
        return productSearchSynonymDictionaryCommandDomainService.update(param);
    }


    @Override
    public int delete(Integer id) {
        return productSearchSynonymDictionaryCommandDomainService.delete(id);
    }
}