package net.summerfarm.manage.application.service.area.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import net.summerfarm.manage.application.inbound.controller.area.vo.AreaVO;
import net.summerfarm.manage.application.inbound.controller.area.vo.LargeAreaWithSubAreaVO;
import net.summerfarm.manage.application.service.area.dto.AreaSimpleDTO;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.entity.LargeArea;
import org.springframework.util.CollectionUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/12/11
 */
public class AreaConverter {


    private AreaConverter() {
        // 无需实现
    }

    public static List<AreaSimpleEntity> toAreaSimpleEntityList(
      List<AreaSimpleDTO> areaSimpleDTOList) {
        if (areaSimpleDTOList == null) {
            return Collections.emptyList();
        }
        List<AreaSimpleEntity> areaSimpleEntityList = new ArrayList<>();
        for (AreaSimpleDTO areaSimpleDTO : areaSimpleDTOList) {
            areaSimpleEntityList.add(toAreaSimpleEntity(areaSimpleDTO));
        }
        return areaSimpleEntityList;
    }

    public static AreaSimpleEntity toAreaSimpleEntity(AreaSimpleDTO areaSimpleDTO) {
        if (areaSimpleDTO == null) {
            return null;
        }
        AreaSimpleEntity areaSimpleEntity = new AreaSimpleEntity();
        areaSimpleEntity.setAreaNo(areaSimpleDTO.getAreaNo());
        areaSimpleEntity.setAreaName(areaSimpleDTO.getAreaName());
        areaSimpleEntity.setAreaStatus(areaSimpleDTO.getAreaStatus());
        areaSimpleEntity.setLargeAreaNo(areaSimpleDTO.getLargeAreaNo());
        areaSimpleEntity.setLargeAreaName(areaSimpleDTO.getLargeAreaName());
        areaSimpleEntity.setLargeAreaStatus(areaSimpleDTO.getLargeAreaStatus());
        return areaSimpleEntity;
    }

    public static List<AreaSimpleDTO> toAreaSimpleDTOList(
      List<AreaSimpleEntity> areaSimpleEntityList) {
        if (areaSimpleEntityList == null) {
            return Collections.emptyList();
        }
        List<AreaSimpleDTO> areaSimpleDTOList = new ArrayList<>();
        for (AreaSimpleEntity areaSimpleEntity : areaSimpleEntityList) {
            areaSimpleDTOList.add(toAreaSimpleDTO(areaSimpleEntity));
        }
        return areaSimpleDTOList;
    }

    public static AreaSimpleDTO toAreaSimpleDTO(AreaSimpleEntity areaSimpleEntity) {
        if (areaSimpleEntity == null) {
            return null;
        }
        AreaSimpleDTO areaSimpleDTO = new AreaSimpleDTO();
        areaSimpleDTO.setAreaNo(areaSimpleEntity.getAreaNo());
        areaSimpleDTO.setAreaName(areaSimpleEntity.getAreaName());
        areaSimpleDTO.setAreaStatus(areaSimpleEntity.getAreaStatus());
        areaSimpleDTO.setLargeAreaNo(areaSimpleEntity.getLargeAreaNo());
        areaSimpleDTO.setLargeAreaName(areaSimpleEntity.getLargeAreaName());
        areaSimpleDTO.setLargeAreaStatus(areaSimpleEntity.getLargeAreaStatus());
        return areaSimpleDTO;
    }

    public static List<LargeAreaWithSubAreaVO> toLargeAreaVOList(List<LargeArea> largeAreaList) {
        if (CollectionUtils.isEmpty(largeAreaList)) {
            return Collections.EMPTY_LIST;
        }
        return largeAreaList.stream().map(largeArea -> {
            return new LargeAreaWithSubAreaVO().setLargeAreaNo(largeArea.getLargeAreaNo())
              .setLargeAreaName(largeArea.getLargeAreaName()).setStatus(largeArea.getStatus())
              .setManageAdminId(largeArea.getManageAdminId());
        }).collect(Collectors.toList());
    }

    public static AreaVO convertToAreaVO(Area area) {
        if (area == null) {
            return null;
        }

        AreaVO areaVO = new AreaVO();
        areaVO.setId(area.getId());
        areaVO.setAreaNo(area.getAreaNo());
        areaVO.setAreaName(area.getAreaName());
        areaVO.setAdminId(area.getAdminId());
        areaVO.setParentNo(area.getParentNo());
        areaVO.setDeliveryFee(area.getDeliveryFee());
        areaVO.setStatus(area.getStatus() != null ? (byte) (area.getStatus() ? 1 : 0) : null);
        areaVO.setAddress(area.getAddress());
        areaVO.setInfo(area.getInfo());
        areaVO.setExpressFee(area.getExpressFee());
        // Assuming children field is not part of AreaVO, otherwise need to handle its conversion too
        areaVO.setDeliveryRule(area.getDeliveryRule());
        areaVO.setMemberRule(area.getMemberRule());
        areaVO.setPayChannel(area.getPayChannel());
        areaVO.setCompanyAccountId(area.getCompanyAccountId());
        areaVO.setMapSection(area.getMapSection());
        areaVO.setFreeDay(area.getFreeDay());
        areaVO.setMailToAddress(area.getMailToAddress());
        // Assuming weChatNotify, notifyTitle, notifyContent, and notifyRemarks are not part of AreaVO
        areaVO.setOriginAreaNo(area.getOriginAreaNo());
        areaVO.setNextDeliveryDate(area.getNextDeliveryDate() != null ? java.sql.Date.valueOf(area.getNextDeliveryDate()) : null);
        areaVO.setChangeStoreNo(area.getChangeStoreNo());
        areaVO.setChangeStatus(area.getChangeStatus());
        // Assuming changStartTime and sortExpireTime are not part of AreaVO
        areaVO.setAdministrativeArea(area.getAdministrativeArea());
        areaVO.setSupportAddOrder(area.getSupportAddOrder() != null ? (byte) area.getSupportAddOrder().intValue() : null);
        areaVO.setUpdateSupportAddOrder(area.getUpdateSupportAddOrder() != null ? (byte) area.getUpdateSupportAddOrder().intValue() : null);
        areaVO.setLargeAreaNo(area.getLargeAreaNo());
        areaVO.setType(area.getType());
        areaVO.setGrade(area.getGrade());
        areaVO.setCreateTime(area.getCreateTime());
        // Assuming updateTime is handled elsewhere or is updated automatically

        return areaVO;
    }
}
