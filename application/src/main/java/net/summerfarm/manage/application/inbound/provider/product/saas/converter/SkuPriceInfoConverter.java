package net.summerfarm.manage.application.inbound.provider.product.saas.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.client.req.product.saas.SummerFarmSkuPriceInfoReq;
import net.summerfarm.client.resp.product.saas.MallPrice4SaasResp;
import net.summerfarm.client.resp.product.saas.SummerFarmSkuPriceInfoResp;
import net.summerfarm.manage.application.service.product.saas.dto.MallPrice4SaasDTO;
import net.summerfarm.manage.application.service.product.saas.dto.SummerFarmSkuPriceInfoDTO;
import net.summerfarm.manage.common.query.product.SummerFarmSkuPriceInfoInput;
import net.summerfarm.manage.domain.product.entity.SkuPriceInfoEntity;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public class SkuPriceInfoConverter {


    private SkuPriceInfoConverter() {
        // 无需实现
    }

    public static List<SummerFarmSkuPriceInfoInput> toSummerFarmSkuPriceInfoInputList(
            List<SummerFarmSkuPriceInfoReq> summerFarmSkuPriceInfoReqList) {
        if (summerFarmSkuPriceInfoReqList == null) {
            return Collections.emptyList();
        }
        List<SummerFarmSkuPriceInfoInput> summerFarmSkuPriceInfoInputList = new ArrayList<>();
        for (SummerFarmSkuPriceInfoReq summerFarmSkuPriceInfoReq : summerFarmSkuPriceInfoReqList) {
            summerFarmSkuPriceInfoInputList.add(
                    toSummerFarmSkuPriceInfoInput(summerFarmSkuPriceInfoReq));
        }
        return summerFarmSkuPriceInfoInputList;
    }

    public static SummerFarmSkuPriceInfoInput toSummerFarmSkuPriceInfoInput(
            SummerFarmSkuPriceInfoReq summerFarmSkuPriceInfoReq) {
        if (summerFarmSkuPriceInfoReq == null) {
            return null;
        }
        SummerFarmSkuPriceInfoInput summerFarmSkuPriceInfoInput = new SummerFarmSkuPriceInfoInput();
        summerFarmSkuPriceInfoInput.setCitySupplyPriceId(
                summerFarmSkuPriceInfoReq.getCitySupplyPriceId());
        summerFarmSkuPriceInfoInput.setSkuId(summerFarmSkuPriceInfoReq.getSkuId());
        summerFarmSkuPriceInfoInput.setAdminId(summerFarmSkuPriceInfoReq.getAdminId());
        summerFarmSkuPriceInfoInput.setCityName(summerFarmSkuPriceInfoReq.getCityName());
        return summerFarmSkuPriceInfoInput;
    }

    public static List<SummerFarmSkuPriceInfoReq> toSummerFarmSkuPriceInfoReqList(
            List<SummerFarmSkuPriceInfoInput> summerFarmSkuPriceInfoInputList) {
        if (summerFarmSkuPriceInfoInputList == null) {
            return Collections.emptyList();
        }
        List<SummerFarmSkuPriceInfoReq> summerFarmSkuPriceInfoReqList = new ArrayList<>();
        for (SummerFarmSkuPriceInfoInput summerFarmSkuPriceInfoInput : summerFarmSkuPriceInfoInputList)
        {
            summerFarmSkuPriceInfoReqList.add(
                    toSummerFarmSkuPriceInfoReq(summerFarmSkuPriceInfoInput));
        }
        return summerFarmSkuPriceInfoReqList;
    }

    public static SummerFarmSkuPriceInfoReq toSummerFarmSkuPriceInfoReq(
            SummerFarmSkuPriceInfoInput summerFarmSkuPriceInfoInput) {
        if (summerFarmSkuPriceInfoInput == null) {
            return null;
        }
        SummerFarmSkuPriceInfoReq summerFarmSkuPriceInfoReq = new SummerFarmSkuPriceInfoReq();
        summerFarmSkuPriceInfoReq.setCitySupplyPriceId(
                summerFarmSkuPriceInfoInput.getCitySupplyPriceId());
        summerFarmSkuPriceInfoReq.setSkuId(summerFarmSkuPriceInfoInput.getSkuId());
        summerFarmSkuPriceInfoReq.setAdminId(summerFarmSkuPriceInfoInput.getAdminId());
        summerFarmSkuPriceInfoReq.setCityName(summerFarmSkuPriceInfoInput.getCityName());
        return summerFarmSkuPriceInfoReq;
    }


    public static List<SummerFarmSkuPriceInfoDTO> toSummerFarmSkuPriceInfoDTOList(
            List<SkuPriceInfoEntity> skuPriceInfoEntityList) {
        if (skuPriceInfoEntityList == null) {
            return Collections.emptyList();
        }
        List<SummerFarmSkuPriceInfoDTO> summerFarmSkuPriceInfoDTOList = new ArrayList<>();
        for (SkuPriceInfoEntity skuPriceInfoEntity : skuPriceInfoEntityList) {
            summerFarmSkuPriceInfoDTOList.add(toSummerFarmSkuPriceInfoDTO(skuPriceInfoEntity));
        }
        return summerFarmSkuPriceInfoDTOList;
    }

    public static SummerFarmSkuPriceInfoDTO toSummerFarmSkuPriceInfoDTO(
            SkuPriceInfoEntity skuPriceInfoEntity) {
        if (skuPriceInfoEntity == null) {
            return null;
        }
        SummerFarmSkuPriceInfoDTO summerFarmSkuPriceInfoDTO = new SummerFarmSkuPriceInfoDTO();
        summerFarmSkuPriceInfoDTO.setCitySupplyPriceId(skuPriceInfoEntity.getCitySupplyPriceId());
        summerFarmSkuPriceInfoDTO.setSkuId(skuPriceInfoEntity.getSkuId());
        summerFarmSkuPriceInfoDTO.setMinPrice(skuPriceInfoEntity.getMinPrice());
        summerFarmSkuPriceInfoDTO.setMaxPrice(skuPriceInfoEntity.getMaxPrice());
        return summerFarmSkuPriceInfoDTO;
    }

    public static List<SkuPriceInfoEntity> toSkuPriceInfoEntityList(
            List<SummerFarmSkuPriceInfoDTO> summerFarmSkuPriceInfoDTOList) {
        if (summerFarmSkuPriceInfoDTOList == null) {
            return Collections.emptyList();
        }
        List<SkuPriceInfoEntity> skuPriceInfoEntityList = new ArrayList<>();
        for (SummerFarmSkuPriceInfoDTO summerFarmSkuPriceInfoDTO : summerFarmSkuPriceInfoDTOList) {
            skuPriceInfoEntityList.add(toSkuPriceInfoEntity(summerFarmSkuPriceInfoDTO));
        }
        return skuPriceInfoEntityList;
    }

    public static SkuPriceInfoEntity toSkuPriceInfoEntity(
            SummerFarmSkuPriceInfoDTO summerFarmSkuPriceInfoDTO) {
        if (summerFarmSkuPriceInfoDTO == null) {
            return null;
        }
        SkuPriceInfoEntity skuPriceInfoEntity = new SkuPriceInfoEntity();
        skuPriceInfoEntity.setCitySupplyPriceId(summerFarmSkuPriceInfoDTO.getCitySupplyPriceId());
        skuPriceInfoEntity.setSkuId(summerFarmSkuPriceInfoDTO.getSkuId());
        skuPriceInfoEntity.setMinPrice(summerFarmSkuPriceInfoDTO.getMinPrice());
        skuPriceInfoEntity.setMaxPrice(summerFarmSkuPriceInfoDTO.getMaxPrice());
        return skuPriceInfoEntity;
    }


    public static List<SummerFarmSkuPriceInfoResp> toSummerFarmSkuPriceInfoRespList(
            List<SummerFarmSkuPriceInfoDTO> summerFarmSkuPriceInfoDTOList) {
        if (summerFarmSkuPriceInfoDTOList == null) {
            return Collections.emptyList();
        }
        List<SummerFarmSkuPriceInfoResp> summerFarmSkuPriceInfoRespList = new ArrayList<>();
        for (SummerFarmSkuPriceInfoDTO summerFarmSkuPriceInfoDTO : summerFarmSkuPriceInfoDTOList) {
            summerFarmSkuPriceInfoRespList.add(
                    toSummerFarmSkuPriceInfoResp(summerFarmSkuPriceInfoDTO));
        }
        return summerFarmSkuPriceInfoRespList;
    }

    public static SummerFarmSkuPriceInfoResp toSummerFarmSkuPriceInfoResp(
            SummerFarmSkuPriceInfoDTO summerFarmSkuPriceInfoDTO) {
        if (summerFarmSkuPriceInfoDTO == null) {
            return null;
        }
        SummerFarmSkuPriceInfoResp summerFarmSkuPriceInfoResp = new SummerFarmSkuPriceInfoResp();
        summerFarmSkuPriceInfoResp.setCitySupplyPriceId(
                summerFarmSkuPriceInfoDTO.getCitySupplyPriceId());
        summerFarmSkuPriceInfoResp.setSkuId(summerFarmSkuPriceInfoDTO.getSkuId());
        summerFarmSkuPriceInfoResp.setMinPrice(summerFarmSkuPriceInfoDTO.getMinPrice());
        summerFarmSkuPriceInfoResp.setMaxPrice(summerFarmSkuPriceInfoDTO.getMaxPrice());
        return summerFarmSkuPriceInfoResp;
    }

    public static MallPrice4SaasResp toMallPrice4SaasResp(MallPrice4SaasDTO dto) {
        MallPrice4SaasResp mallPrice4SaasResp = new MallPrice4SaasResp ();
        mallPrice4SaasResp.setSkuId(dto.getSkuId ());
        mallPrice4SaasResp.setSkuCode(dto.getSkuCode ());
        mallPrice4SaasResp.setPrice(dto.getPrice ());
        mallPrice4SaasResp.setValidTime(dto.getValidTime ());
        mallPrice4SaasResp.setInvalidTime(dto.getInvalidTime ());
        mallPrice4SaasResp.setValidType(dto.getValidType ());
        return mallPrice4SaasResp;
    }
}
