package net.summerfarm.manage.application.inbound.provider.sample.converter;

import net.summerfarm.client.resp.sample.SampleOrderDeliveryInfoResp;
import net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject;

/**
 * Description: 转换类<br/>
 * date: 2025/1/2 14:57<br/>
 *
 * <AUTHOR> />
 */
public class SampleOrderConverter {

    public static SampleOrderDeliveryInfoResp flatObjectToSampleOrderDeliveryInfoResp(SampleOrderFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }

        SampleOrderDeliveryInfoResp resp = new SampleOrderDeliveryInfoResp();
        resp.setSampleId(flatObject.getSampleId());
        resp.setMname(flatObject.getMname());
        resp.setMsize(flatObject.getMsize());
        resp.setMId(flatObject.getMId());
        resp.setContactName(flatObject.getContactName());
        resp.setDeliveryTime(flatObject.getDeliveryTime());
        resp.setContactPhone(flatObject.getContactPhone());
        resp.setContactAddress(flatObject.getContactAddress());
        resp.setPdName(flatObject.getPdName());
        resp.setAmount(flatObject.getAmount());
        resp.setWeight(flatObject.getWeight());
        resp.setSku(flatObject.getSku());
        resp.setContactId(flatObject.getContactId());
        resp.setStoreNo(flatObject.getStoreNo());
        resp.setBrandName(flatObject.getBrandName());
        resp.setBigCustomerId(flatObject.getBigCustomerId());

        return resp;
    }
}
