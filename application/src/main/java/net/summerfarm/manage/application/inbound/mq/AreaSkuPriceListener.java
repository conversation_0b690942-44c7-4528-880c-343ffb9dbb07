package net.summerfarm.manage.application.inbound.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 2024.7.16 替换为rpc接口不在使用消息
 */
@Deprecated
@MqOrderlyListener(topic = "topic_orderly_pms_common",
        tag = "pop_price_effect",
        consumerGroup = "GID_sf_mall_manage_area_sku_price",maxReconsumeTimes = 5)
@Slf4j
public class AreaSkuPriceListener extends AbstractMqListener<AreaSkuPriceDTO> {
    @Autowired
    private AreaSkuCommandService areaSkuCommandService;

    @Override
    public void process(AreaSkuPriceDTO areaSkuPriceDTO) {
        if(ObjectUtil.isEmpty (areaSkuPriceDTO) || CollectionUtil.isEmpty (areaSkuPriceDTO.getAreaNoList ())|| ObjectUtil.isEmpty (areaSkuPriceDTO.getSku ())){
            return;
        }
        Integer successFlag = areaSkuCommandService.updateOrAddAreaSkuPrice (areaSkuPriceDTO);
        if(ObjectUtil.isEmpty (successFlag) || successFlag < 1){
            log.error ("pop_price_effect失败,sku={},areaNoList={}",areaSkuPriceDTO.getSku (),areaSkuPriceDTO.getAreaNoList ());
        }
    }
}
