package net.summerfarm.manage.application.service.price;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.application.inbound.controller.price.input.query.AreaSkuPriceMarkupConfigQueryInput;

/**
 *
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
public interface AreaSkuPriceMarkupConfigQueryService {

    /**
     * @description: 新增
     * @return AreaSkuPriceMarkupConfigEntity
     **/
    PageInfo<AreaSkuPriceMarkupConfigEntity> getPage(AreaSkuPriceMarkupConfigQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    AreaSkuPriceMarkupConfigEntity getDetail(Long id);

}