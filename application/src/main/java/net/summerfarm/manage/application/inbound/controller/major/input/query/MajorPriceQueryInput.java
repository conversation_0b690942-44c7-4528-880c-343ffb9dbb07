package net.summerfarm.manage.application.inbound.controller.major.input.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;


@EqualsAndHashCode(callSuper = true)
@Data
public class MajorPriceQueryInput extends BasePageInput {
    /**
     * adminId
     */
    @NotNull(message = "adminId is not null")
    private Integer adminId;

    /**
     * 1 账期 2  现结
     */
    @NotNull(message = "direct is not null")
    private Integer direct;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 库存仓-上架时指定
     */
    private Integer warehouseNo;

    /**
     * 报价单状态：0: 未生效， 1：已生效， 2：失效,3:待提交
     */
    @NotNull(message = "validStatus is not null")
    private Integer validStatus;

    /**
     * 性质 1:代销不入仓;2:代销入仓;3:经销;4:代仓;
     */
    private Integer subType;

    /**
     * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮 4商城价下浮 5商城价加价 6商城价减价
     */
    private Integer priceType;

    /**
     * 运营大区
     */
    private Integer largeAreaNo;

    /**
     * 城市编号
     */
    private Integer areaNo;

}
