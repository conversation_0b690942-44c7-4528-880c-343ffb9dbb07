package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @Description
 * @Date 2025/1/14 15:23
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBaseQueryInput extends BasePageInput {

    /**
     * sku
     */
    private String sku;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * sku列表
     */
    private List<String> skuList;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 一级类目
     */
    private Long firstCategoryId;
    /**
     * 二级类目
     */
    private Long secondCategoryId;

    /**
     * 商品类型
     */
    private Integer type;

    /**
     * 二级性质
     */
    private Integer subType;

    /**
     * 商品状态
     */
    private Integer outdated;

    /**
     * 属性id
     */
    private Long productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

}
