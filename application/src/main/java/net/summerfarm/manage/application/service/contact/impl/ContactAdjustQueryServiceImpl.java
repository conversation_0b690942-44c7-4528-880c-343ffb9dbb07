package net.summerfarm.manage.application.service.contact.impl;


import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactAdjustVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.application.service.contact.converter.ContactAdjustVOConverter;
import net.summerfarm.manage.application.service.contact.ContactAdjustService;
import net.summerfarm.manage.application.service.merchant.MerchantBaseService;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.common.util.PageUtils;
import net.summerfarm.manage.domain.account.repository.ContactAdjustQueryRepository;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-26 16:20:20
 */
@Service
public class ContactAdjustQueryServiceImpl extends MerchantBaseService implements ContactAdjustService {

    @Resource
    private ContactAdjustQueryRepository contactAdjustQueryRepository;


    @Override
    public PageInfo<ContactAdjustVO> getPage(MerchantQueryInput input) {
        return PageUtils.getPage(input.getPageIndex(), input.getPageSize() ,list(input));
    }

    @Override
    public ContactAdjustVO getDetail(MerchantQueryInput input) {
        if (input.getContactAdjustId() == null){
            throw new BizException("contactAdjustId 不能为空");
        }
        List<ContactAdjustVO> list = list(input);
        if(CollUtil.isEmpty(list)) {
            throw new BizException("地址不存在!");
        }

        ContactAdjustVO contactAdjustVO = list.get(0);

        // 填充待审核的地址
        // 填充原始地址
        List<ContactVO> voList = queryContactMap(contactAdjustVO.getStoreId(), contactAdjustVO.getMId());
        ContactVO contactVO = voList.stream().filter(u -> Objects.equals(contactAdjustVO.getContactId(), u.getContactId())).findFirst().orElse(null);
        contactAdjustVO.setContactVO(contactVO);
        return contactAdjustVO;
    }


    @Override
    public List<ContactAdjustVO> list(MerchantQueryInput input) {
        PageHelper.startPage(PageUtils.DEFAULT_PAGE_NO, PageUtils.DEFAULT_PAGE_SIZE);
        List<ContactAdjustEntity> contactAdjustEntities = contactAdjustQueryRepository.selectByCondition(input).stream().filter(it->it.getMId()!=null).collect(Collectors.toList());
        if(CollUtil.isEmpty(contactAdjustEntities)){
            return Collections.emptyList();
        }

        List<Long> mIds = contactAdjustEntities.stream().map(ContactAdjustEntity::getMId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mIds)) {
            return new ArrayList<>();
        }

        List<ContactAdjustVO> vos = ContactAdjustVOConverter.toContactAdjustVOList(contactAdjustEntities);

        // 拼接列表数据
        input.setMIds(mIds);
        input.setPageIndex(PageUtils.DEFAULT_PAGE_NO);
        input.setPageSize(mIds.size());
        //根据状态查询
        PageInfo<MerchantVO> page = getBasePage(input);
        List<MerchantVO> merchantStoreAndExtends = page.getList();
        //合并数据
        baseListMerge(merchantStoreAndExtends);
        ContactAdjustVOConverter.warpContactAdjustVOList(vos, merchantStoreAndExtends);
        return vos;
    }



}