package net.summerfarm.manage.application.service.area.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/12/8
 */
@Data
public class AreaSimpleDTO implements Serializable {

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 运营区域状态, 0: 停用, 1: 启用
     */
    private Integer areaStatus;

    /**
     * 大区编号
     */
    private Integer largeAreaNo;

    /**
     * 大区名称
     */
    private String largeAreaName;

    /**
     * 大区状态,  0: 停用, 1: 启用
     */
    private Integer largeAreaStatus;

}
