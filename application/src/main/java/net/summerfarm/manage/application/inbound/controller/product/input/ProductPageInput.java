package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2024/1/25
 */
@Data
public class ProductPageInput extends BasePageInput implements Serializable {

    /**
     * sku
     */
    private String sku;

    private List<String> skuList;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 三级类目id
     */
    private List<Long> categoryIds;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 二级类目id
     */
    private Integer parentCategoryId;

    /**
     * 一级类目id
     */
    private Integer grandCategoryId;

    /**
     * sku生命周期
     * -1、上新处理中 0、使用中 1、已删除
     */
    private Integer outdated;

    /**
     * 商品编码
     */
    private String pdNo;

    /**
     * 类型 0 自营 1 代仓
     */
    private Integer type;

    /**
     * 三级类目id
     */
    private Integer categoryId;
    /**
     * 买手ID
     */
    private Long buyerId;
}
