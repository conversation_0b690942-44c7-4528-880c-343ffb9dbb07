package net.summerfarm.manage.application.inbound.provider.merchant;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.client.provider.merchant.MerchantInfoQueryProvider;
import net.summerfarm.client.resp.merchant.MerchantQueryResp;
import net.summerfarm.client.resp.merchant.MerchantShowPriceResultResp;
import net.summerfarm.manage.application.inbound.provider.merchant.converter.MerchantConverter;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 *
 * @date 2025-01-08 10:14:32
 * @version 1.0
 *
 */
@DubboService
public class MerchantQueryProviderImpl implements MerchantInfoQueryProvider {
    @Autowired
    private MerchantQueryRepository merchantQueryRepository;


    @Override
    public DubboResponse<List<MerchantShowPriceResultResp>> queryMerchantShowPriceList(List<Long> midList) {
        if(CollectionUtil.isEmpty(midList)) {
            return DubboResponse.getError("PARAMS-DEFAULT_ERROR", "请求参数为空");
        }
        if(midList.size() > Global.TWO_HUNDRED) {
            return DubboResponse.getError("PARAMS-DEFAULT_ERROR", "请求数据量过大，批次数量最大为200");
        }

        List<MerchantEntity> merchantEntities = merchantQueryRepository.selectByIds(midList);
        return DubboResponse.getOK(MerchantConverter.toMerchantShowPriceResultRespList(merchantEntities));
    }

    @Override
    public DubboResponse<MerchantQueryResp> queryMerchantInfo(Long mid) {
        if (mid == null || mid <= 0){
            return DubboResponse.getError("PARAMS-DEFAULT_ERROR", "请求参数为空");
        }
        MerchantEntity merchantEntity = merchantQueryRepository.selectByIdForceMaster(mid);
        return DubboResponse.getOK(MerchantConverter.toMerchantQueryResp(merchantEntity));
    }
}
