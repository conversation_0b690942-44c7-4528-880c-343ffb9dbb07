package net.summerfarm.manage.application.inbound.mq.msgbody;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AreaSkuPriceDTO implements Serializable {
    /**
     * sku编号
     */
    @NotNull
    private String sku;

    /**
     * 城市编号
     */
    @NotNull
    private List<Integer> areaNoList;

    /**
     * 价格
     */
    @NotNull
    private BigDecimal price;
}
