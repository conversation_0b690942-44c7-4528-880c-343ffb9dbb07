package net.summerfarm.manage.application.service.product.saas;

import java.util.List;
import net.summerfarm.manage.application.service.product.saas.dto.SummerFarmSkuPriceInfoDTO;
import net.summerfarm.manage.common.query.product.SummerFarmSkuPriceInfoInput;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public interface ProductQueryService {

    /**
     * 查询报价单区间
     * @param inputList 报价单查询
     * @return
     */
    List<SummerFarmSkuPriceInfoDTO> queryAdminSkuPricingInfo(List<SummerFarmSkuPriceInfoInput> inputList);

}
