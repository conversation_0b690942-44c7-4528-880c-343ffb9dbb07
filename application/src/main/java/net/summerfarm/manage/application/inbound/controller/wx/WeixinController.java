package net.summerfarm.manage.application.inbound.controller.wx;


import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.manage.application.inbound.controller.price.vo.DocInfoVO;
import net.summerfarm.manage.application.inbound.mq.msgbody.WeixinShippingDTO;
import net.summerfarm.manage.application.service.wx.WeixinShippingServiceImpl;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信接口
 * <AUTHOR>
 * @Create 2020-09-21
 */
@RestController
@RequestMapping("/wx")
public class WeixinController {

    @Resource
    private WeixinShippingServiceImpl weixinShippingService;

    @PostMapping("/upsert/uploadShippingInfo")
    public CommonResult<Void> uploadShippingInfoByMasterOrderNo(@RequestBody WeixinShippingDTO dto) {
        List<String> orderNoList = dto.getOrderNoList();
        if(CollectionUtil.isEmpty(orderNoList)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数异常");
        }
        if(orderNoList.size() > 100) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数过大请分批处理");
        }
        orderNoList.forEach(orderNo -> weixinShippingService.uploadShippingInfoByOrderNo(orderNo, dto.getOrdersType()));
        return CommonResult.ok();
    }
}
