package net.summerfarm.manage.application.service.product.mall.impl;


import net.summerfarm.manage.application.service.product.mall.AppPopBiaoguoProductsDfQueryService;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoProductsDfQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoProductsDfQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopBiaoguoProductsDfAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-12-12 11:19:19
* @version 1.0
*
*/
@Service
public class AppPopBiaoguoProductsDfQueryServiceImpl implements AppPopBiaoguoProductsDfQueryService {

    @Autowired
    private AppPopBiaoguoProductsDfQueryRepository appPopBiaoguoProductsDfQueryRepository;

    @Override
    public PageInfo<AppPopBiaoguoProductsDfEntity> getPage(AppPopBiaoguoProductsDfQueryInput input) {
        AppPopBiaoguoProductsDfQueryParam queryParam = AppPopBiaoguoProductsDfAssembler.toAppPopBiaoguoProductsDfQueryParam(input);
        return appPopBiaoguoProductsDfQueryRepository.getPage(queryParam);
    }

    @Override
    public AppPopBiaoguoProductsDfEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return appPopBiaoguoProductsDfQueryRepository.selectById(id);
    }
}