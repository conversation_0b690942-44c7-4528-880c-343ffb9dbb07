package net.summerfarm.manage.application.inbound.controller.merchant.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销列表查询
 * @date 2023/4/20 16:33:52
 */
@Data
public class MerchantCancelPageQueryInput extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 运营区域
     */
    private Integer areaNo;

    /**
     * 注销状态
     * @see MerchantCancelEnum
     */
    private Integer status;

    /**
     * 申请开始时间
     */
    private Date startTime;

    /**
     * 申请结束时间
     */
    private Date endTime;
}
