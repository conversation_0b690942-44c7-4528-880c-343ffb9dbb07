package net.summerfarm.manage.application.service.job;

import net.summerfarm.manage.domain.job.entity.CrmJobMerchantDetailEntity;
import net.summerfarm.manage.application.inbound.controller.job.input.command.CrmJobMerchantDetailCommandInput;


/**
 * @date 2024-12-18 14:46:45
 * @version 1.0
 */
public interface CrmJobMerchantDetailCommandService {

    /**
     * 支付完成后处理任务
     // 1. 获取指定门店下客户所有生效中&已领取&（未完成+已完成未发奖）的任务
     // 拓品任务 ：不能凑品，即配置至少有一个sku能满足sku件数规则
     // 下单任务：在任务配置内的订单项的金额是否达标
     * @param orderNo
     * @param mid
     */
    void finishJobAfterPaymentSucceeded(String orderNo);

    /**
     * 退款后处理任务
     * @param orderNo
     * @param mid
     */
    void updateJobAfterSale(String afterSaleNo, String orderNo);


    /**
     * 配送完成后发放奖励
     * @param orderNo
     * @param mid
     */
    void awardRewardAfterDelivery(String orderNo);

}