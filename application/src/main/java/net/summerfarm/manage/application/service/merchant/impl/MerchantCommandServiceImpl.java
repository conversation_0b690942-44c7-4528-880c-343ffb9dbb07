package net.summerfarm.manage.application.service.merchant.impl;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantBatchVO;
import net.summerfarm.manage.application.service.merchant.MerchantBaseService;
import net.summerfarm.manage.application.service.merchant.MerchantCommandService;
import net.summerfarm.manage.common.util.ValidateUtil;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.crm.service.FollowUpRelationDomainService;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchant.service.MerchantDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-09-19 10:55:24
 */

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantCommandServiceImpl extends MerchantBaseService implements MerchantCommandService {

    @Autowired
    private MerchantQueryRepository merchantQueryRepository;
    @Autowired
    private AdminQueryRepository adminQueryRepository;

    @Autowired
    private MerchantDomainService merchantDomainService;
    @Autowired
    private FollowUpRelationDomainService followUpRelationDomainService;
    @Override
    public MerchantEntity addMerchant(MerchantEntity entity) {
        // 创建门店、账户
        entity = merchantDomainService.addMerchant(entity);

        // 后置处理
        followUpRelationDomainService.insertForAddMerchant(entity.getMId());

        return entity;
    }



    /**
     * 茶百道批量预注册
     *
     * @param
     * @return
     */
    @Override
    public CommonResult<String> saveBatchForCBD(MultipartFile file) {
        // 数据解析
        List<MerchantBatchVO> allList = this.getAllList(file);
        // 数据包装
        List<MerchantEntity> merchantEntities = this.converterToMerchantEntity(allList);
        // 落库
        String msg = this.batchAdd(merchantEntities);

        return StringUtils.isBlank(msg) ? CommonResult.ok() : CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "执行错误（手机号或店铺名重复）行数:" + msg);
    }

    private List<MerchantBatchVO> getAllList(MultipartFile file) {
        ExcelReader reader = null;
        try {
            reader = ExcelUtil.getReader(file.getInputStream(), 0);
        } catch (Exception ioException) {
            throw new BizException("读取excel表格失败！");
        }
        HashMap<String, String> headerAlias = new HashMap<>(8);
        headerAlias.put("门店名称", "mname");
        headerAlias.put("联系方式", "phone");
        headerAlias.put("所属总部(大客户编码)", "adminId");
        headerAlias.put("连锁类型", "enterpriseScale");
        headerAlias.put("合作模式", "type");
        reader.setHeaderAlias(headerAlias);
        List<MerchantBatchVO> allList = reader.readAll(MerchantBatchVO.class);
        if (allList.isEmpty()) {
            throw new BizException("需要导入的数据项为空！");
        }
        return allList;
    }


    private List<MerchantEntity> converterToMerchantEntity(List<MerchantBatchVO> voList) {
        List<MerchantEntity> merchantEntities = new ArrayList<>();
        Integer length = NumberUtils.INTEGER_TWO;
        //校验表中的数据是否店名或手机号重复
        HashMap<String, MerchantBatchVO> nameMap = new HashMap<>();
        HashMap<String, MerchantBatchVO> phoneMap = new HashMap<>();
        StringJoiner joiner = new StringJoiner(",");
        //循环处理数据
        for (MerchantBatchVO vo : voList) {
            String phone = StringUtils.trimToNull(vo.getPhone());
            String mname = StringUtils.trimToNull(vo.getMname());
            String adminId = StringUtils.trimToNull(vo.getAdminId());
            String type = StringUtils.trimToNull(vo.getType());
            String enterpriseScale = StringUtils.trimToNull(vo.getEnterpriseScale());
            if (StringUtils.isEmpty(type) || !Objects.equals(type.length(), 4)) {
                joiner.add(length.toString());
                length += 1;
                continue;
            }
            String stringDirect = type.substring(NumberUtils.INTEGER_ZERO, 2);
            String stringShow = type.substring(NumberUtils.INTEGER_TWO, 4);
            Integer direct = Objects.equals(stringDirect, "账期") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
            Integer show = Objects.equals(stringShow, "定量") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
            boolean check = checkoutType(stringDirect, stringShow, vo);
            boolean skuShow = checkDirect(adminId, direct, show);
            MerchantBatchVO nameVO = nameMap.get(mname);
            MerchantBatchVO phoneVO = phoneMap.get(mname);
            //校验格式，大客户信息， 手机号，店名称重复
            if (!check || !skuShow || Objects.nonNull(nameVO) || Objects.nonNull(phoneVO)) {
                joiner.add(length.toString());
                length += 1;
                continue;
            }
            MerchantEntity merchant = new MerchantEntity();
            merchant.setDirect(direct);
            merchant.setSkuShow(show);
            merchant.setMerchantType("KA");
            merchant.setEnterpriseScale(enterpriseScale);
            merchant.setInvitecode("seeegj");
            merchant.setSize("大客户");
            merchant.setServer(1);
            merchant.setCompanyBrand("茶百道");
            merchant.setType("茶饮");
            merchant.setAdminId(Long.valueOf(adminId));
            merchant.setMname(mname);
            merchant.setPhone(phone);
            merchant.setIslock(0);
            merchant.setPreRegisterFlag(1);
            merchantEntities.add(merchant);
            length += 1;
            nameMap.put(mname, vo);
            phoneMap.put(phone, vo);
        }
        //获取所有格式错误的行
        if (joiner.length() > 0) {
            throw new BizException("格式错误：" + joiner);
        }
        return merchantEntities;
    }


    /**
     * 校验类型信息
     */
    private boolean checkoutType(String direct, String show, MerchantBatchVO vo) {
        //校验客户属性

        String phone = StringUtils.trimToNull(vo.getPhone());
        String mname = StringUtils.trimToNull(vo.getMname());
        String adminId = StringUtils.trimToNull(vo.getAdminId());
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(mname) || StringUtils.isEmpty(adminId)) {
            return false;
        }
        if (!Objects.equals(direct, "账期") && !Objects.equals(direct, "现结")) {
            return false;
        }
        if (!Objects.equals(show, "定量") && !Objects.equals(show, "全量")) {
            return false;
        }
        return !StringUtils.isEmpty(adminId);

    }

    /**
     * 校验大客户信息:账期、现结；全量、定量
     */
    private boolean checkDirect(String adminId, Integer direct, Integer show) {
        AdminEntity admin = adminQueryRepository.selectByPrimaryKey(Long.valueOf(adminId));
        if (Objects.isNull(admin)) {
            return Boolean.FALSE;
        }
        String contractMethod = admin.getContractMethod();
        String replace = contractMethod.replace("\"", "");
        String method = direct + ":" + show;
        return replace.contains(method);
    }


    private String batchAdd(List<MerchantEntity> entities) {
        StringJoiner stringJoiner = new StringJoiner(",");
        //处理插入店铺信息
        Integer length = NumberUtils.INTEGER_ONE;
        for (MerchantEntity merchant : entities) {
            length += 1;
            String phone = merchant.getPhone();
            //校验手机号
            if (Objects.nonNull(phone) && !ValidateUtil.isMobile(phone)) {
                log.warn("【批量导入】手机号异常,phone:{}", phone);
                stringJoiner.add(String.valueOf(length));
                continue;
            }
            try {
                addMerchant(merchant);
            } catch (BizException e) {
                log.warn("【批量导入】业务校验异常,原因：{}", e.getMessage(), e);
                stringJoiner.add(String.valueOf(length));
            } catch (Exception e) {
                log.warn("茶百道批量预注册出错：行数：{}.", length, e);
                stringJoiner.add(String.valueOf(length));
            }
        }
        return stringJoiner.toString();
    }
}