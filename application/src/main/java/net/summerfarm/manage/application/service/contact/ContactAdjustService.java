package net.summerfarm.manage.application.service.contact;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactAdjustVO;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import net.summerfarm.manage.common.input.account.ContactAdjustQueryInput;

import java.util.List;

/**
 *
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
public interface ContactAdjustService {

    /**
     * @description: 新增
     * @return ContactAdjustEntity
     **/
    PageInfo<ContactAdjustVO> getPage(MerchantQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    ContactAdjustVO getDetail(MerchantQueryInput input);

     List<ContactAdjustVO> list(MerchantQueryInput input);
}