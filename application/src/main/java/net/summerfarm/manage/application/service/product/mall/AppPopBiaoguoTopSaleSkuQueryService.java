package net.summerfarm.manage.application.service.product.mall;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoTopSaleSkuQueryInput;

import java.util.List;

/**
 *
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
public interface AppPopBiaoguoTopSaleSkuQueryService {

    /**
     * @description: 分页
     * @return AppPopBiaoguoTopSaleSkuEntity
     **/
    PageInfo<AppPopBiaoguoTopSaleSkuEntity> getPage(AppPopBiaoguoTopSaleSkuQueryInput input);

    /**
     * @description: 列表
     * @return AppPopBiaoguoTopSaleSkuEntity
     **/
    List<AppPopBiaoguoTopSaleSkuEntity> getList(AppPopBiaoguoTopSaleSkuQueryInput input);

    /**
     * @description: 类目列表
     * @return AppPopBiaoguoTopSaleSkuEntity
     **/
    List<AppPopBiaoguoCategoryEntity> getCategoryList(AppPopBiaoguoTopSaleSkuQueryInput input);

    /**
     * @description: 详情
     * @return: java.lang.Boolean
     **/
    AppPopBiaoguoTopSaleSkuEntity getDetail(Long id);

    /**
     * 查询外部品
     */
    AppPopBiaoguoTopSaleSkuEntity getOne(AppPopBiaoguoTopSaleSkuQueryInput input);
}