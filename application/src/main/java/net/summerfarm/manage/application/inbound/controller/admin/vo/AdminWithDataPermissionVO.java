package net.summerfarm.manage.application.inbound.controller.admin.vo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.ToString;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-18 13:21:08
 */
@Data
@ToString(callSuper = true)
public class AdminWithDataPermissionVO extends AdminVO {

    @ApiModelProperty(value = "数据权限列表-该用户拥有的运营服务区权限")
    private List<AdminDataPermissionVO> dataPermissions;

    @ApiModelProperty(value = "数据报表-销售维度-仓数据权限")
    private Set<String> partSaleArea;

    @ApiModelProperty(value = "邀请码/地推码？应该是销售员才有")
    private String invitecode;

    /**
     * 不知道是什么字段...
     */
    @ApiModelProperty(value = "不知道是什么字段...")
    private Boolean openSea = false;

    /**
     * 母鸡这是什么字段。。
     */
    //普通客户为1 大客户为2
    @ApiModelProperty(value = "客户类型：普通用户(鲜沐员工)为:1, 批发大客户为:2, 其他客户比如SaaS客户为:0")
    private Integer type;


    /**
     * 是否销售主管
     */
    @ApiModelProperty(value = "是否销售主管(M1/M2/M3等)")
    private boolean onlySaleManager;
}