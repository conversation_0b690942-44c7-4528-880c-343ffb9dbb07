package net.summerfarm.manage.application.inbound.provider.admin.converter;

import net.summerfarm.client.resp.admin.AdminDataPermissionResp;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;

/**
 * Description: 转换类<br/>
 * date: 2024/7/9 15:37<br/>
 *
 * <AUTHOR> />
 */
public class AdminDataPermissionConverter {

    public static AdminDataPermissionResp toAdminDataPermissionResp(AdminDataPermissionEntity entity) {
        if(entity == null){
            return null;
        }
        AdminDataPermissionResp resp = new AdminDataPermissionResp();

        resp.setId(entity.getId());
        resp.setAdminId(entity.getAdminId());
        resp.setPermissionValue(entity.getPermissionValue());
        resp.setPermissionName(entity.getPermissionName());
        resp.setAddtime(entity.getAddtime());
        resp.setType(entity.getType());

        return resp;
    }
}
