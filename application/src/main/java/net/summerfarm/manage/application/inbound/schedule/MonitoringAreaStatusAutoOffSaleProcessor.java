package net.summerfarm.manage.application.inbound.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.domain.area.service.AreaService;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 关闭的area 对应的areasku自动下架
 **/
@Component
@Slf4j
public class MonitoringAreaStatusAutoOffSaleProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private FenceQueryFacade fenceQueryFacade;
    @Resource
    private AreaService areaService;
    @Resource
    private AreaSkuCommandService areaSkuCommandService;
    @Resource
    private AreaSkuQueryService areaSkuQueryService;


    @Override
    public ProcessResult processResult(XmJobInput context) {
        //关闭的areaNo
        Set<Integer> areaNos = areaService.queryCloseAreaNos ();
        if(CollectionUtil.isEmpty (areaNos)){
            return new ProcessResult(true);
        }

        boolean hasNext = true;
        int pageIndex = 1;
        PageInfo<Integer> page;
        while (hasNext) {
            page = areaSkuQueryService.pageOnsaleIdsByAreaNo(areaNos, pageIndex,100);
            List<Integer> ids = page.getList ();
            if(CollectionUtil.isNotEmpty (ids)){
                areaSkuCommandService.offSaleByIds(ids);
            }
            hasNext = page.isHasNextPage ();
            pageIndex = pageIndex + 1;
            try {
                Thread.sleep (1000 * 2 * 60);//2分钟 执行100个 ,防止dts监听 爆炸
            } catch (InterruptedException e) {
                throw new RuntimeException (e);
            }
            log.info ("MonitoringAreaStatusAutoOffSaleProcessor - list.size={}", page.getTotal ());
        }



        return new ProcessResult(true);
    }
}
