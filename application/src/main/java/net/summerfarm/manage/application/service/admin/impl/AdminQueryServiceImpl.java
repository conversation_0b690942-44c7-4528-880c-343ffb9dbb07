package net.summerfarm.manage.application.service.admin.impl;


import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.contexts.Global;
import net.summerfarm.manage.application.inbound.controller.admin.assembler.AdminAssembler;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminQueryInput;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminDataPermissionVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminWithDataPermissionVO;
import net.summerfarm.manage.application.service.admin.AdminQueryService;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.application.util.UserRoleUtils;
import net.summerfarm.manage.common.enums.AdminTypeEnum;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.domain.admin.param.query.AdminQueryParam;
import net.summerfarm.manage.domain.admin.param.query.InvitecodeQueryParam;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.admin.repository.InvitecodeQueryRepository;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.repository.ConfigQueryRepository;
import net.summerfarm.manage.facade.wms.WarehouseStorageFacade;
import net.summerfarm.manage.facade.wms.dto.WarehouseStorageDTO;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-18 13:21:08
 */
@Slf4j
@Service
public class AdminQueryServiceImpl implements AdminQueryService {

    /**
     * config表中配置的仓id，用于报表展示仓销售维数据
     */
    private static final String CONFIG_KEY_REPORT_SALE_AREA = "sale_report_area_id";

    @Autowired
    private AdminQueryRepository adminQueryRepository;

    @Autowired
    private ConfigQueryRepository configQueryRepository;

    @Autowired
    private AreaQueryRepository areaQueryRepository;

    @Autowired
    private WarehouseStorageFacade warehouseStorageFacade;

    @Autowired
    private InvitecodeQueryRepository invitecodeQueryRepository;

    @Autowired
    private AdminDataPermissionQueryRepository adminDataPermissionQueryRepository;

    @Override
    public PageInfo<AdminEntity> getPage(AdminQueryInput input) {
        ExceptionUtil.checkAndThrow(Objects.nonNull(input)
                && Objects.nonNull(input.getPageIndex()) && Objects.nonNull(input.getPageSize()), "必传参数缺失");
        AdminQueryParam queryParam = AdminAssembler.toAdminQueryParam(input);
        return adminQueryRepository.getPage(queryParam);
    }

    @Override
    public AdminVO getDetail(Long adminId) {
        if (Objects.isNull(adminId)) {
            log.warn("请求参数为空！,将使用当前用户的信息");
            adminId = UserInfoHolder.getAdminId();
        }
        AdminEntity adminEntity = adminQueryRepository.selectByPrimaryKey(adminId);
        if (null == adminEntity) {
            throw new BizException("用户不存在:" + adminId);
        }
        return AdminAssembler.toAdminVO(adminEntity);
    }

    /**
     * 获取当前登陆用户的明细属性、仓库权限等
     *
     * @return
     */
    @Override
    public AdminWithDataPermissionVO getCurrentUserDetail() {
        AdminWithDataPermissionVO adminWithDataPermissionVO = AdminAssembler.toAdminVO(UserInfoHolder.getCurrentUser());
        if (null == adminWithDataPermissionVO) {
            throw new BizException("请先登陆");
        }
        buildAdminWithDataPermissionVO(adminWithDataPermissionVO);
        return adminWithDataPermissionVO;
    }

    @Override
    public AdminWithDataPermissionVO getCurrentUserDetail(Long adminId) {
        AdminWithDataPermissionVO adminWithDataPermissionVO = AdminAssembler.toAdminVO(getDetail(adminId));
        if (null == adminWithDataPermissionVO) {
            throw new BizException("adminId非法:" + adminId);
        }
        buildAdminWithDataPermissionVO(adminWithDataPermissionVO);
        return adminWithDataPermissionVO;
    }

    private void buildAdminWithDataPermissionVO(@NotNull AdminWithDataPermissionVO admin) {
        InvitecodeQueryParam params = new InvitecodeQueryParam();
        params.setAdminId(admin.getAdminId().intValue());

        // 如果是超级管理员，或者是研发，则赋予全部权限...
        if (UserRoleUtils.isCurrentUserSA() || UserRoleUtils.isCurrentUserRD()) {
            // 默认获取所有的运营服务区；
            admin.setOpenSea(true);
            List<AreaSimpleEntity> areaSimpleEntityList = areaQueryRepository.batchQueryByAreaNos(null);
            List<AdminDataPermissionVO> adminPermissions = AdminAssembler.fromAreaEntity(areaSimpleEntityList);

            // 默认获取所有的库存仓
            List<WarehouseStorageDTO> warehouseStorageDTOList = warehouseStorageFacade.queryAllWarehouses();
            if (CollectionUtils.isNotEmpty(warehouseStorageDTOList)) {
                adminPermissions.addAll(AdminAssembler.fromWarehouseDTO(warehouseStorageDTOList));
            }
            admin.setDataPermissions(adminPermissions);
        } else {
            // 只要不是SA或者RD，则需要从这里获取权限
            AdminDataPermissionQueryParam permissionParams = new AdminDataPermissionQueryParam();
            permissionParams.setAdminId(admin.getAdminId().intValue());
            List<AdminDataPermissionEntity> adminDataPermissionEntityList = adminDataPermissionQueryRepository.selectByCondition(permissionParams);
            admin.setDataPermissions(AdminAssembler.fromEntity(adminDataPermissionEntityList));
            for (AdminDataPermissionEntity adminDataPermission : adminDataPermissionEntityList) {
                if ("0".equals(adminDataPermission.getPermissionValue()) || "1001".equals(adminDataPermission.getPermissionValue())) {
                    // 如果拥有1001的权限，那么就是公海？？？擦
                    admin.setOpenSea(true);
                }
            }
        }

        // 当用户是BD，或者普通大客户，则构建对应的权限数据
        if (UserRoleUtils.isCurrentUserBD()) {
            // BD才需要获取地推码
            List<InvitecodeEntity> invitecodeEntityList = invitecodeQueryRepository.selectByCondition(params);
            if (CollectionUtils.isNotEmpty(invitecodeEntityList)) {
                admin.setInvitecode(invitecodeEntityList.get(0).getInvitecode());
            }
        }
        // 如果是大客户经理，则设置type=2
        if (UserRoleUtils.isCurrentUserMajorAdmin()) {
            admin.setType(AdminTypeEnum.MAJOR_CUSTOMER.getType());
        }
        buildSaleManagerInfo(admin);
    }

    /**
     * 如果是销售主管，数据报表只给所拥有仓的数据
     *
     * @param adminVO
     */
    private void buildSaleManagerInfo(AdminWithDataPermissionVO adminVO) {
        // 目前所配置的仓数据
        ConfigEntity areaConfig = configQueryRepository.selectByKey(CONFIG_KEY_REPORT_SALE_AREA);
        if (Objects.isNull(areaConfig)) {
            log.warn("未配置报表销售区域:{}", CONFIG_KEY_REPORT_SALE_AREA);
            return;
        }
        Set<String> partArea = Sets.newLinkedHashSet(Global.DEFAULT_SPLITTER.splitToList(areaConfig.getValue()));

        adminVO.setOnlySaleManager(false);

        // 超管给ta全部的地区
        if (UserRoleUtils.isCurrentUserSA() || UserRoleUtils.isCurrentUserRD()) {
            adminVO.setOnlySaleManager(true);
            adminVO.setPartSaleArea(partArea);
            return;
        }

        if (UserRoleUtils.isCurrentUserSalesSA()) {
            // 如果有销售主管权限
            adminVO.setOnlySaleManager(true);
            List<AdminDataPermissionVO> dataPermissions = adminVO.getDataPermissions();
            if (CollectionUtils.isEmpty(dataPermissions)) {
                adminVO.setPartSaleArea(null);
                return;
            }
            // 查询该销售主管的数据权限，数据权限取交集
            List<String> roleDatePermissions = dataPermissions.stream().map(data -> data.getPermissionValue()).collect(Collectors.toList());
            partArea.retainAll(roleDatePermissions);
            adminVO.setPartSaleArea(partArea);
        }
    }
}