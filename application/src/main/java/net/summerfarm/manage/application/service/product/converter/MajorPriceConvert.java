package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceDownloadInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLargeAreaVO;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceVO;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceQueryInput;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam;
import net.summerfarm.manage.facade.inventory.dto.ProductCostQueryDto;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MajorPriceConvert {
    MajorPriceConvert INSTANCE = Mappers.getMapper(MajorPriceConvert.class);

    MajorPriceCommandParam input2Param(Integer areaNo, String areaName ,MajorPriceInput item);

    MajorPricePageQueryParam input2Param(MajorPriceQueryInput input);

    MajorPricePageQueryParam input2Param(MajorPriceDownloadInput input);

    MajorPriceCommandParam entity2Param(MajorPriceEntity e);

    MajorPriceLargeAreaVO object2Vo(MajorPriceFlatObject object);

    @Mapping(source = "lastCyclePrice", target = "priceLog.price")
    @Mapping(source = "lastCycleInterestRate", target = "priceLog.interestRate")
    @Mapping(source = "lastCyclePriceType", target = "priceLog.priceType")
    MajorPriceVO object2Vo(MajorPriceItemFlatObject object);

    ProductCostQueryDto vo2ProductCostQueryDto(MajorPriceVO vo);

    ProductCostQueryDto resp2ProductCostQueryDto(WarehouseBySkuAreaNoResp resp);

    List<ProductCostQueryDto> input2ProductCostQueryDtoList(List<MajorPriceInput> inputList);
}
