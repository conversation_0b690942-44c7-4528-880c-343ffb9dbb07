package net.summerfarm.manage.application.inbound.controller.contact;

import net.summerfarm.manage.application.inbound.controller.contact.input.ContactFrequencyQueryInput;
import net.summerfarm.manage.application.inbound.controller.contact.input.ContactInsertInput;
import net.summerfarm.manage.application.inbound.controller.contact.vo.ContactVO;
import net.summerfarm.manage.application.service.contact.ContactService;
import net.summerfarm.manage.domain.merchant.service.ContactDomainService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/13 17:08:44
 */
@RestController
@RequestMapping("/contact")
public class ContactController {

    @Resource
    private ContactService contactService;

    /**
     * 查询地址配送周期
     * @param input
     * @return ContactVO
     */
    @RequestMapping(value = "/query/frequency", method = RequestMethod.POST)
    public CommonResult<ContactVO> queryFrequencyContact(@RequestBody @Validated ContactFrequencyQueryInput input) {
        return CommonResult.ok(contactService.queryFrequencyContact(input));
    }


    /**
     * 内部使用
     * @param input
     * @return ContactVO
     */
    @RequestMapping(value = "/upsert/inner-insert", method = RequestMethod.POST)
    public CommonResult<Void> addContactWithoutAudit(@RequestBody @Validated ContactInsertInput input) {
        contactService.addContactWithoutAudit(input);
        return CommonResult.ok();
    }
}
