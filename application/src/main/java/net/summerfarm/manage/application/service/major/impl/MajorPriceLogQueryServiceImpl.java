package net.summerfarm.manage.application.service.major.impl;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.major.assembler.MajorPriceLogAssembler;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceLogQueryInput;
import net.summerfarm.manage.application.service.major.MajorPriceLogQueryService;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;
import net.summerfarm.manage.domain.major.repository.MajorPriceLogQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
*
* <AUTHOR>
* @date 2025-02-19 11:19:11
* @version 1.0
*
*/
@Service
public class MajorPriceLogQueryServiceImpl implements MajorPriceLogQueryService {

    @Resource
    private MajorPriceLogQueryRepository majorPriceLogQueryRepository;

    @Override
    public PageInfo<MajorPriceLogEntity> getPage(MajorPriceLogQueryInput input) {
        MajorPriceLogQueryParam queryParam = MajorPriceLogAssembler.toMajorPriceLogQueryParam(input);
        return majorPriceLogQueryRepository.getPage(queryParam);
    }

}