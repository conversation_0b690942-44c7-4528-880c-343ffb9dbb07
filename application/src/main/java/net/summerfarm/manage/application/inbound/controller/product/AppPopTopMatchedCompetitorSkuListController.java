package net.summerfarm.manage.application.inbound.controller.product;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopTopMatchedCompetitorSkuListAssembler;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopTopMatchedCompetitorSkuListVO;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListQueryInput;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.application.service.product.mall.AppPopTopMatchedCompetitorSkuListQueryService;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


/**
 * @Title app_pop_top_matched_competitor_sku_list table
 * @Description app_pop_top_matched_competitor_sku_list table功能模块
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 */
@RestController
@RequestMapping(value="/appPopTopMatchedCompetitorSkuList")
public class AppPopTopMatchedCompetitorSkuListController{

	@Resource
	private AppPopTopMatchedCompetitorSkuListQueryService appPopTopMatchedCompetitorSkuListQueryService;

	/**
	 * 推荐外部商品列表
	 * @return AppPopTopMatchedCompetitorSkuListVO
	 */
	@PostMapping(value = "/query/detail")
	public CommonResult<AppPopTopMatchedCompetitorSkuListVO> detail(@RequestBody AppPopTopMatchedCompetitorSkuListQueryInput input){
		ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(input.getSku()), "sku编码不能为空");
		input.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		return CommonResult.ok(AppPopTopMatchedCompetitorSkuListAssembler.toAppPopTopMatchedCompetitorSkuListVO(appPopTopMatchedCompetitorSkuListQueryService.getOne(input)));
	}


	/**
	 * app_pop_top_matched_competitor_sku_list table列表
	 * @return AppPopTopMatchedCompetitorSkuListVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<AppPopTopMatchedCompetitorSkuListVO>> getPage(@RequestBody AppPopTopMatchedCompetitorSkuListQueryInput input){
		PageInfo<AppPopTopMatchedCompetitorSkuListEntity> page = appPopTopMatchedCompetitorSkuListQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, AppPopTopMatchedCompetitorSkuListAssembler::toAppPopTopMatchedCompetitorSkuListVO));
	}




}

