package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ItemLabelInput
 * @Description
 * <AUTHOR>
 * @Date 10:37 2024/5/8
 * @Version 1.0
 **/
@Data
public class ItemLabelInput implements Serializable {

    /**
     * 标签名称
     */
    @NotNull(message = "标签名称不能为空！")
    private String labelName;
}
