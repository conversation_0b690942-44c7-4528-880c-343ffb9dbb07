package net.summerfarm.manage.application.inbound.controller.major.input.command;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MajorPriceExcelDataInput extends ImportExcelBaseDTO implements Serializable{
    /**
     * sku编号
     */
    @ExcelProperty("大客户id(必填)")
    private Integer adminId;

    @ExcelProperty("报价单类型(必填)")
    private String directString;
    /**
     * sku编号
     */
    @ExcelProperty("SKU(必填)")
    private String sku;
    /**
     * 状态0=保存,1=提交
     */
    @ExcelProperty("状态(必填)")
    private String status;
    /**
     * 城市
     */
    @ExcelProperty("城市(必填例如杭州)")
    private String areaName;
    /**
     * 报价单的生效时间
     */
    @ExcelProperty("生效时间(必填格式: xxxx-xx-xx xx:xx:xx)")
    private String validTime;

    /**
     * 报价单的失效时间
     */
    @ExcelProperty("截止时间(必填格式: xxxx-xx-xx xx:xx:xx)")
    private String invalidTime;
    /**
     * 商城是否展示 0 展示 1 不展示 类似上下架
     */
    @ExcelProperty("门店可见该商品(必填格式: 是或否)")
    private String mallShowString;

    /**
     * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮 4商城价下浮 5商城价加价 6商城价减价
     */
    @ExcelProperty("报价方式(必填)")
    private String priceTypeString;

    @ExcelProperty("数值(鲜沐商城价、毛利率时可空)")
    private BigDecimal amount;

    @ExcelProperty("毛利率固定值")
    private BigDecimal fixedPrice;

    @ExcelProperty("毛利率百分比%(毛利率时不为可空)")
    private BigDecimal interestRate;

    @ExcelProperty("支付方式(选填:商品为代仓需要支付时填入 1 )")
    private Integer payMethod;


    public String getKey(){
        return this.adminId+this.sku+this.areaName+this.directString;
    }
}
