package net.summerfarm.manage.application.inbound.controller.contact.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description 地址周期配送规则
 * @date 2023/11/13 17:06:30
 */
@Data
public class ContactVO implements Serializable {

    /**
     * 配送地址ID
     */
    private Long id;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;
}
