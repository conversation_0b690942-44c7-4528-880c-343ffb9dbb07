package net.summerfarm.manage.application.service.searchSynonym.impl;


import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryQueryService;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryQueryRepository;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.assembler.ProductSearchSynonymDictionaryAssembler;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
@Service
public class ProductSearchSynonymDictionaryQueryServiceImpl implements ProductSearchSynonymDictionaryQueryService {

    @Autowired
    private ProductSearchSynonymDictionaryQueryRepository productSearchSynonymDictionaryQueryRepository;

    @Override
    public PageInfo<ProductSearchSynonymDictionaryEntity> getPage(ProductSearchSynonymDictionaryQueryInput input) {
        ProductSearchSynonymDictionaryQueryParam queryParam = ProductSearchSynonymDictionaryAssembler.toProductSearchSynonymDictionaryQueryParam(input);
        return productSearchSynonymDictionaryQueryRepository.getPage(queryParam);
    }

    @Override
    public ProductSearchSynonymDictionaryEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return productSearchSynonymDictionaryQueryRepository.selectById(id);
    }
}