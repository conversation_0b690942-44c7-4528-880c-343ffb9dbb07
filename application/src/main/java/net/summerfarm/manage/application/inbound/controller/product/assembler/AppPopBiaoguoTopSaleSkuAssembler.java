package net.summerfarm.manage.application.inbound.controller.product.assembler;


import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoCategoryVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoTopSaleSkuVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.TopMatchedXianmuSkuVO;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoTopSaleSkuCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoTopSaleSkuQueryInput;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;
import net.summerfarm.manage.domain.product.param.command.AppPopBiaoguoTopSaleSkuCommandParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.*;
import java.util.List;

/**`
 *
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
public class AppPopBiaoguoTopSaleSkuAssembler {

    private AppPopBiaoguoTopSaleSkuAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static AppPopBiaoguoTopSaleSkuQueryParam toAppPopBiaoguoTopSaleSkuQueryParam(AppPopBiaoguoTopSaleSkuQueryInput appPopBiaoguoTopSaleSkuQueryInput) {
        if (appPopBiaoguoTopSaleSkuQueryInput == null) {
            return null;
        }
        AppPopBiaoguoTopSaleSkuQueryParam appPopBiaoguoTopSaleSkuQueryParam = new AppPopBiaoguoTopSaleSkuQueryParam();
        appPopBiaoguoTopSaleSkuQueryParam.setId(appPopBiaoguoTopSaleSkuQueryInput.getId());
        appPopBiaoguoTopSaleSkuQueryParam.setCategoryName(appPopBiaoguoTopSaleSkuQueryInput.getCategoryName());
        appPopBiaoguoTopSaleSkuQueryParam.setCompetitor(appPopBiaoguoTopSaleSkuQueryInput.getCompetitor());
        appPopBiaoguoTopSaleSkuQueryParam.setSkuCode(appPopBiaoguoTopSaleSkuQueryInput.getSkuCode());
        appPopBiaoguoTopSaleSkuQueryParam.setSpiderFetchTime(appPopBiaoguoTopSaleSkuQueryInput.getSpiderFetchTime());
        appPopBiaoguoTopSaleSkuQueryParam.setFinalStandardPrice(appPopBiaoguoTopSaleSkuQueryInput.getFinalStandardPrice());
        appPopBiaoguoTopSaleSkuQueryParam.setMonthSale(appPopBiaoguoTopSaleSkuQueryInput.getMonthSale());
        appPopBiaoguoTopSaleSkuQueryParam.setUrl(appPopBiaoguoTopSaleSkuQueryInput.getUrl());
        appPopBiaoguoTopSaleSkuQueryParam.setGrossWeight(appPopBiaoguoTopSaleSkuQueryInput.getGrossWeight());
        appPopBiaoguoTopSaleSkuQueryParam.setNetWeight(appPopBiaoguoTopSaleSkuQueryInput.getNetWeight());
        appPopBiaoguoTopSaleSkuQueryParam.setSpecification(appPopBiaoguoTopSaleSkuQueryInput.getSpecification());
        appPopBiaoguoTopSaleSkuQueryParam.setGoodsName(appPopBiaoguoTopSaleSkuQueryInput.getGoodsName());
        appPopBiaoguoTopSaleSkuQueryParam.setSalesVolume3d(appPopBiaoguoTopSaleSkuQueryInput.getSalesVolume3d());
        appPopBiaoguoTopSaleSkuQueryParam.setMonthsaleGmv(appPopBiaoguoTopSaleSkuQueryInput.getMonthsaleGmv());
        appPopBiaoguoTopSaleSkuQueryParam.setCategoryLevel2(appPopBiaoguoTopSaleSkuQueryInput.getCategoryLevel2());
        appPopBiaoguoTopSaleSkuQueryParam.setMonthsaleGmv3dAgo(appPopBiaoguoTopSaleSkuQueryInput.getMonthsaleGmv3dAgo());
        appPopBiaoguoTopSaleSkuQueryParam.setTopMatchedXianmuSkuList(appPopBiaoguoTopSaleSkuQueryInput.getTopMatchedXianmuSkuList());
        appPopBiaoguoTopSaleSkuQueryParam.setCreateTime(appPopBiaoguoTopSaleSkuQueryInput.getCreateTime());
        appPopBiaoguoTopSaleSkuQueryParam.setDs(appPopBiaoguoTopSaleSkuQueryInput.getDs());
        appPopBiaoguoTopSaleSkuQueryParam.setPageIndex(appPopBiaoguoTopSaleSkuQueryInput.getPageIndex());
        appPopBiaoguoTopSaleSkuQueryParam.setPageSize(appPopBiaoguoTopSaleSkuQueryInput.getPageSize());
        appPopBiaoguoTopSaleSkuQueryParam.setCategory1(appPopBiaoguoTopSaleSkuQueryInput.getCategory1());
        appPopBiaoguoTopSaleSkuQueryParam.setCategory2(appPopBiaoguoTopSaleSkuQueryInput.getCategory2());
        appPopBiaoguoTopSaleSkuQueryParam.setCategory3(appPopBiaoguoTopSaleSkuQueryInput.getCategory3());
        appPopBiaoguoTopSaleSkuQueryParam.setBuyerId(appPopBiaoguoTopSaleSkuQueryInput.getBuyerId());
        return appPopBiaoguoTopSaleSkuQueryParam;
    }





    public static AppPopBiaoguoTopSaleSkuCommandParam buildCreateParam(AppPopBiaoguoTopSaleSkuCommandInput appPopBiaoguoTopSaleSkuCommandInput) {
        if (appPopBiaoguoTopSaleSkuCommandInput == null) {
            return null;
        }
        AppPopBiaoguoTopSaleSkuCommandParam appPopBiaoguoTopSaleSkuCommandParam = new AppPopBiaoguoTopSaleSkuCommandParam();
        appPopBiaoguoTopSaleSkuCommandParam.setId(appPopBiaoguoTopSaleSkuCommandInput.getId());
        appPopBiaoguoTopSaleSkuCommandParam.setCategoryName(appPopBiaoguoTopSaleSkuCommandInput.getCategoryName());
        appPopBiaoguoTopSaleSkuCommandParam.setCompetitor(appPopBiaoguoTopSaleSkuCommandInput.getCompetitor());
        appPopBiaoguoTopSaleSkuCommandParam.setSkuCode(appPopBiaoguoTopSaleSkuCommandInput.getSkuCode());
        appPopBiaoguoTopSaleSkuCommandParam.setSpiderFetchTime(appPopBiaoguoTopSaleSkuCommandInput.getSpiderFetchTime());
        appPopBiaoguoTopSaleSkuCommandParam.setFinalStandardPrice(appPopBiaoguoTopSaleSkuCommandInput.getFinalStandardPrice());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthSale(appPopBiaoguoTopSaleSkuCommandInput.getMonthSale());
        appPopBiaoguoTopSaleSkuCommandParam.setUrl(appPopBiaoguoTopSaleSkuCommandInput.getUrl());
        appPopBiaoguoTopSaleSkuCommandParam.setGrossWeight(appPopBiaoguoTopSaleSkuCommandInput.getGrossWeight());
        appPopBiaoguoTopSaleSkuCommandParam.setNetWeight(appPopBiaoguoTopSaleSkuCommandInput.getNetWeight());
        appPopBiaoguoTopSaleSkuCommandParam.setSpecification(appPopBiaoguoTopSaleSkuCommandInput.getSpecification());
        appPopBiaoguoTopSaleSkuCommandParam.setGoodsName(appPopBiaoguoTopSaleSkuCommandInput.getGoodsName());
        appPopBiaoguoTopSaleSkuCommandParam.setSalesVolume3d(appPopBiaoguoTopSaleSkuCommandInput.getSalesVolume3d());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthsaleGmv(appPopBiaoguoTopSaleSkuCommandInput.getMonthsaleGmv());
        appPopBiaoguoTopSaleSkuCommandParam.setCategoryLevel2(appPopBiaoguoTopSaleSkuCommandInput.getCategoryLevel2());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthsaleGmv3dAgo(appPopBiaoguoTopSaleSkuCommandInput.getMonthsaleGmv3dAgo());
        appPopBiaoguoTopSaleSkuCommandParam.setTopMatchedXianmuSkuList(appPopBiaoguoTopSaleSkuCommandInput.getTopMatchedXianmuSkuList());
        appPopBiaoguoTopSaleSkuCommandParam.setCreateTime(appPopBiaoguoTopSaleSkuCommandInput.getCreateTime());
        appPopBiaoguoTopSaleSkuCommandParam.setDs(appPopBiaoguoTopSaleSkuCommandInput.getDs());
        return appPopBiaoguoTopSaleSkuCommandParam;
    }


    public static AppPopBiaoguoTopSaleSkuCommandParam buildUpdateParam(AppPopBiaoguoTopSaleSkuCommandInput appPopBiaoguoTopSaleSkuCommandInput) {
        if (appPopBiaoguoTopSaleSkuCommandInput == null) {
            return null;
        }
        AppPopBiaoguoTopSaleSkuCommandParam appPopBiaoguoTopSaleSkuCommandParam = new AppPopBiaoguoTopSaleSkuCommandParam();
        appPopBiaoguoTopSaleSkuCommandParam.setId(appPopBiaoguoTopSaleSkuCommandInput.getId());
        appPopBiaoguoTopSaleSkuCommandParam.setCategoryName(appPopBiaoguoTopSaleSkuCommandInput.getCategoryName());
        appPopBiaoguoTopSaleSkuCommandParam.setCompetitor(appPopBiaoguoTopSaleSkuCommandInput.getCompetitor());
        appPopBiaoguoTopSaleSkuCommandParam.setSkuCode(appPopBiaoguoTopSaleSkuCommandInput.getSkuCode());
        appPopBiaoguoTopSaleSkuCommandParam.setSpiderFetchTime(appPopBiaoguoTopSaleSkuCommandInput.getSpiderFetchTime());
        appPopBiaoguoTopSaleSkuCommandParam.setFinalStandardPrice(appPopBiaoguoTopSaleSkuCommandInput.getFinalStandardPrice());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthSale(appPopBiaoguoTopSaleSkuCommandInput.getMonthSale());
        appPopBiaoguoTopSaleSkuCommandParam.setUrl(appPopBiaoguoTopSaleSkuCommandInput.getUrl());
        appPopBiaoguoTopSaleSkuCommandParam.setGrossWeight(appPopBiaoguoTopSaleSkuCommandInput.getGrossWeight());
        appPopBiaoguoTopSaleSkuCommandParam.setNetWeight(appPopBiaoguoTopSaleSkuCommandInput.getNetWeight());
        appPopBiaoguoTopSaleSkuCommandParam.setSpecification(appPopBiaoguoTopSaleSkuCommandInput.getSpecification());
        appPopBiaoguoTopSaleSkuCommandParam.setGoodsName(appPopBiaoguoTopSaleSkuCommandInput.getGoodsName());
        appPopBiaoguoTopSaleSkuCommandParam.setSalesVolume3d(appPopBiaoguoTopSaleSkuCommandInput.getSalesVolume3d());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthsaleGmv(appPopBiaoguoTopSaleSkuCommandInput.getMonthsaleGmv());
        appPopBiaoguoTopSaleSkuCommandParam.setCategoryLevel2(appPopBiaoguoTopSaleSkuCommandInput.getCategoryLevel2());
        appPopBiaoguoTopSaleSkuCommandParam.setMonthsaleGmv3dAgo(appPopBiaoguoTopSaleSkuCommandInput.getMonthsaleGmv3dAgo());
        appPopBiaoguoTopSaleSkuCommandParam.setTopMatchedXianmuSkuList(appPopBiaoguoTopSaleSkuCommandInput.getTopMatchedXianmuSkuList());
        appPopBiaoguoTopSaleSkuCommandParam.setCreateTime(appPopBiaoguoTopSaleSkuCommandInput.getCreateTime());
        appPopBiaoguoTopSaleSkuCommandParam.setDs(appPopBiaoguoTopSaleSkuCommandInput.getDs());
        return appPopBiaoguoTopSaleSkuCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<AppPopBiaoguoTopSaleSkuVO> toAppPopBiaoguoTopSaleSkuVOList(List<AppPopBiaoguoTopSaleSkuEntity> appPopBiaoguoTopSaleSkuEntityList) {
        if (appPopBiaoguoTopSaleSkuEntityList == null) {
            return Collections.emptyList();
        }
        List<AppPopBiaoguoTopSaleSkuVO> appPopBiaoguoTopSaleSkuVOList = new ArrayList<>();
        for (AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity : appPopBiaoguoTopSaleSkuEntityList) {
            appPopBiaoguoTopSaleSkuVOList.add(toAppPopBiaoguoTopSaleSkuVO(appPopBiaoguoTopSaleSkuEntity));
        }
        return appPopBiaoguoTopSaleSkuVOList;
}


   public static AppPopBiaoguoTopSaleSkuVO toAppPopBiaoguoTopSaleSkuVO(AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity) {
       if (appPopBiaoguoTopSaleSkuEntity == null) {
            return null;
       }
       AppPopBiaoguoTopSaleSkuVO appPopBiaoguoTopSaleSkuVO = new AppPopBiaoguoTopSaleSkuVO();
       appPopBiaoguoTopSaleSkuVO.setId(appPopBiaoguoTopSaleSkuEntity.getId());
       appPopBiaoguoTopSaleSkuVO.setCategoryName(appPopBiaoguoTopSaleSkuEntity.getCategoryName());
       appPopBiaoguoTopSaleSkuVO.setCompetitor(appPopBiaoguoTopSaleSkuEntity.getCompetitor());
       appPopBiaoguoTopSaleSkuVO.setSkuCode(appPopBiaoguoTopSaleSkuEntity.getSkuCode());
       appPopBiaoguoTopSaleSkuVO.setSpiderFetchTime(appPopBiaoguoTopSaleSkuEntity.getSpiderFetchTime());
       appPopBiaoguoTopSaleSkuVO.setFinalStandardPrice(appPopBiaoguoTopSaleSkuEntity.getFinalStandardPrice());
       appPopBiaoguoTopSaleSkuVO.setMonthSale(appPopBiaoguoTopSaleSkuEntity.getMonthSale());
       appPopBiaoguoTopSaleSkuVO.setUrl(appPopBiaoguoTopSaleSkuEntity.getUrl());
       appPopBiaoguoTopSaleSkuVO.setGrossWeight(appPopBiaoguoTopSaleSkuEntity.getGrossWeight());
       appPopBiaoguoTopSaleSkuVO.setNetWeight(appPopBiaoguoTopSaleSkuEntity.getNetWeight());
       appPopBiaoguoTopSaleSkuVO.setSpecification(appPopBiaoguoTopSaleSkuEntity.getSpecification());
       appPopBiaoguoTopSaleSkuVO.setGoodsName(appPopBiaoguoTopSaleSkuEntity.getGoodsName());
       appPopBiaoguoTopSaleSkuVO.setSalesVolume3d(appPopBiaoguoTopSaleSkuEntity.getSalesVolume3d());
       appPopBiaoguoTopSaleSkuVO.setMonthsaleGmv(appPopBiaoguoTopSaleSkuEntity.getMonthsaleGmv());
       appPopBiaoguoTopSaleSkuVO.setCategoryLevel2(appPopBiaoguoTopSaleSkuEntity.getCategoryLevel2());
       appPopBiaoguoTopSaleSkuVO.setMonthsaleGmv3dAgo(appPopBiaoguoTopSaleSkuEntity.getMonthsaleGmv3dAgo());
       appPopBiaoguoTopSaleSkuVO.setTopMatchedXianmuSkuList(appPopBiaoguoTopSaleSkuEntity.getTopMatchedXianmuSkuList());
       appPopBiaoguoTopSaleSkuVO.setCreateTime(appPopBiaoguoTopSaleSkuEntity.getCreateTime());
       appPopBiaoguoTopSaleSkuVO.setDs(appPopBiaoguoTopSaleSkuEntity.getDs());
       appPopBiaoguoTopSaleSkuVO.setTopMatchedXianmuSkuVOList(convert(appPopBiaoguoTopSaleSkuEntity));
       appPopBiaoguoTopSaleSkuVO.setCategory1(appPopBiaoguoTopSaleSkuEntity.getCategory1());
       appPopBiaoguoTopSaleSkuVO.setCategory2(appPopBiaoguoTopSaleSkuEntity.getCategory2());
       appPopBiaoguoTopSaleSkuVO.setCategory3(appPopBiaoguoTopSaleSkuEntity.getCategory3());
       appPopBiaoguoTopSaleSkuVO.setLevel(appPopBiaoguoTopSaleSkuEntity.getLevel());
       appPopBiaoguoTopSaleSkuVO.setOrigin(appPopBiaoguoTopSaleSkuEntity.getOrigin());
       appPopBiaoguoTopSaleSkuVO.setFruitSize(appPopBiaoguoTopSaleSkuEntity.getFruitSize());
       return appPopBiaoguoTopSaleSkuVO;
   }

   public static List<TopMatchedXianmuSkuVO> convert(AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity) {
        if (Objects.isNull(appPopBiaoguoTopSaleSkuEntity)
                || CollectionUtils.isEmpty(appPopBiaoguoTopSaleSkuEntity.getTopMatchedXianmuSkuEntityList())) {
            return Lists.newArrayList();
        }
       List<TopMatchedXianmuSkuVO> result = Lists.newArrayList();
       appPopBiaoguoTopSaleSkuEntity.getTopMatchedXianmuSkuEntityList().forEach(matchedXianmuSkuEntity -> {
           TopMatchedXianmuSkuVO topMatchedXianmuSkuVO = TopMatchedXianmuSkuVO.builder()
                   .rank(matchedXianmuSkuEntity.getRank())
                   .xianmuSkuCode(matchedXianmuSkuEntity.getXianmuSkuCode())
                   .xianmuProductName(matchedXianmuSkuEntity.getXianmuProductName())
                   .specification(matchedXianmuSkuEntity.getSpecification())
                   .matchingReason(matchedXianmuSkuEntity.getMatchingReason())
                   .netWeightNum(matchedXianmuSkuEntity.getNetWeightNum())
                   .weightNum(matchedXianmuSkuEntity.getWeightNum())
                   .build();
            result.add(topMatchedXianmuSkuVO);
       });
        return result;
   }

   public static AppPopBiaoguoCategoryVO convert(AppPopBiaoguoCategoryEntity appPopBiaoguoCategoryEntity) {
        if (Objects.isNull(appPopBiaoguoCategoryEntity)) {
            return null;
        }
        return AppPopBiaoguoCategoryVO.builder()
                .category2(appPopBiaoguoCategoryEntity.getCategory2())
                .build();

   }



}
