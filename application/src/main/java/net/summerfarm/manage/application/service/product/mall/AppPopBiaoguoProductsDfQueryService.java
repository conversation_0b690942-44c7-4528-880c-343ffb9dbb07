package net.summerfarm.manage.application.service.product.mall;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoProductsDfQueryInput;

/**
 *
 * @date 2024-12-12 11:19:19
 * @version 1.0
 *
 */
public interface AppPopBiaoguoProductsDfQueryService {

    /**
     * @description: 新增
     * @return AppPopBiaoguoProductsDfEntity
     **/
    PageInfo<AppPopBiaoguoProductsDfEntity> getPage(AppPopBiaoguoProductsDfQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    AppPopBiaoguoProductsDfEntity getDetail(Long id);

}