package net.summerfarm.manage.application.inbound.provider.order.converter;

import net.summerfarm.client.resp.order.TimingOrderNoFreezeResp;
import net.summerfarm.client.resp.order.TimingOrderNoSetResp;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;

/**
 * Description: 转换类<br/>
 * date: 2024/6/5 15:50<br/>
 *
 * <AUTHOR> />
 */
public class TimingOrderConverter {

    public static TimingOrderNoFreezeResp flat2NoFreezeResp(NoFreezeProxySaleNoWareNoSkuFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }
        TimingOrderNoFreezeResp resp = new TimingOrderNoFreezeResp();

        resp.setStoreNo(flatObject.getStoreNo());
        resp.setSku(flatObject.getSku());
        resp.setQuantity(flatObject.getQuantity());

        return resp;
    }


    public static TimingOrderNoSetResp flat2TimingOrderNoSetResp(TimingOrderProxySaleNoWarehouseSkuFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }
        TimingOrderNoSetResp resp = new TimingOrderNoSetResp();

        resp.setStoreNo(flatObject.getStoreNo());
        resp.setSku(flatObject.getSku());
        resp.setQuantity(flatObject.getNotSetQuantity());

        return resp;
    }
}
