package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.vo.PendingAssociationProductVO;
import net.summerfarm.manage.domain.product.entity.PendingAssociationProductEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * @Description
 * @Date 2025/4/27 14:38
 * @<AUTHOR>
 */
public class PendingAssociationProductConverter {

    public static PendingAssociationProductVO entityToVO(PendingAssociationProductEntity entity, Map<String, String> skuCabinetCodeMap) {
        if (entity == null) {
            return null;
        }

        PendingAssociationProductVO vo = new PendingAssociationProductVO();
        vo.setSku(entity.getSku());
        vo.setPdId(entity.getPdId());
        vo.setPdName(entity.getPdName());
        vo.setWeight(entity.getWeight());
        vo.setWeightNum(entity.getWeightNum());
        vo.setNetWeightNum(entity.getNetWeightNum());
        vo.setPicturePath(entity.getPicturePath());
        vo.setCabinetCode(MapUtils.isNotEmpty(skuCabinetCodeMap)
                ? skuCabinetCodeMap.get(entity.getSku()) : "");
        vo.setAddTime(entity.getAddTime());

        return vo;
    }

}
