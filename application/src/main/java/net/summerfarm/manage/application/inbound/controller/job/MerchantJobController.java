package net.summerfarm.manage.application.inbound.controller.job;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.job.vo.JobProductVO;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobDetailVo;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobVo;
import net.summerfarm.manage.application.service.job.CrmJobMerchantDetailCommandService;
import net.summerfarm.manage.application.service.job.MerchantJobQueryService;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.result.CommonResult;
import net.xianmu.jobsdk.model.dto.JobImportResultDto;
import net.xianmu.jobsdk.model.input.JobIdInput;
import net.xianmu.jobsdk.model.input.MerchantJobCreateInput;
import net.xianmu.jobsdk.model.input.MerchantJobUpdateInput;
import net.xianmu.jobsdk.model.query.MerchantJobProductQuery;
import net.xianmu.jobsdk.model.query.MerchantJobQuery;
import net.xianmu.jobsdk.service.MerchantJobCommandService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 营销任务
 */
@RestController
@RequestMapping(value="/merchant-job")
public class MerchantJobController {

    @Resource
    private MerchantJobCommandService merchantJobCommandService;
    @Resource
    private MerchantJobQueryService merchantJobQueryService;
    @Resource
    private CrmJobMerchantDetailCommandService crmJobMerchantDetailCommandService;


    /**
     * 创建任务
     *
     * @return 任务创建结果, 包括成功数量,失败数量及错误文件resourceId
     */
    @PostMapping("/upsert/create-job")
    @RequiresPermissions(value = {"merchant-job:insert", Global.SA}, logical = Logical.OR)
    public CommonResult<JobImportResultDto> createJob(@RequestBody @Valid MerchantJobCreateInput input) {
        return CommonResult.ok(merchantJobCommandService.createJob(input, UserInfoHolder.getAdminId()));
    }

    /**
     * 更新任务.
     * 只能加品,不能修改人群范围,不能加品类
     */
    @PostMapping("/upsert/update-job")
    @RequiresPermissions(value = {"merchant-job:insert", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> updateJob(@RequestBody @Valid MerchantJobUpdateInput input) {
        merchantJobCommandService.updateJob(input, UserInfoHolder.getAdminId());
        return CommonResult.ok();
    }

    /**
     * 开启/停用任务
     * 无法开启/停用 "已结束"的任务
     */
    @PostMapping("/upsert/toggle-job")
    @RequiresPermissions(value = {"merchant-job:insert", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> toggleJob(@RequestBody @Valid JobIdInput input) {
        merchantJobCommandService.toggleJob(input.getJobId(), UserInfoHolder.getAdminId());
        return CommonResult.ok();
    }

    /**
     * 查询任务列表
     */
    @PostMapping("/query/page")
    @RequiresPermissions(value = {"merchant-job:query", Global.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<MerchantJobVo>> queryPage(@RequestBody MerchantJobQuery query) {
        return CommonResult.ok(merchantJobQueryService.pageJob(query));
    }

    /**
     * 查询任务详情
     */
    @PostMapping("/query/detail")
    @RequiresPermissions(value = {"merchant-job:query", Global.SA}, logical = Logical.OR)
    public CommonResult<MerchantJobDetailVo> queryDetail(@RequestBody @Valid JobIdInput query) {
        return CommonResult.ok(merchantJobQueryService.getJobDetail(query.getJobId()));
    }

    /**
     * 分页查询商品列表
     */
    @PostMapping("/query/product/page")
    @RequiresPermissions(value = {"merchant-job:query", Global.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<JobProductVO>> queryProductPage(@RequestBody @Valid MerchantJobProductQuery query) {
        return CommonResult.ok(merchantJobQueryService.pageProduct(query.getJobId(), query.getPageIndex(), query.getPageSize()));
    }

    /**
     * 导出任务进度
     */
    @PostMapping("/export-async/progress")
    @RequiresPermissions(value = {"merchant-job:query", Global.SA}, logical = Logical.OR)
    public CommonResult<Long> exportProgress(@RequestBody @Valid JobIdInput query) {
        return CommonResult.ok(merchantJobQueryService.exportJobProgress(query.getJobId(), UserInfoHolder.getAdminId()));
    }


    /**
     * 根据订单号手动发奖（后门）
     */
    @PostMapping("/inner/reward")
    public CommonResult<Void> awardReward(@RequestBody List<String> orderList) {
        if(CollectionUtil.isNotEmpty(orderList)) {
            orderList.forEach(orderNo -> crmJobMerchantDetailCommandService.awardRewardAfterDelivery(orderNo));
        }
        return CommonResult.ok();
    }
}
