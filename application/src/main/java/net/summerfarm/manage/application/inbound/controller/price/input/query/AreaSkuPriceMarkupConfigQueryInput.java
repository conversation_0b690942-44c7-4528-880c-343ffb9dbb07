package net.summerfarm.manage.application.inbound.controller.price.input.query;

import lombok.Data;
import java.io.Serializable;

import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class AreaSkuPriceMarkupConfigQueryInput extends BasePageInput implements Serializable{
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 商品编号
	 */
	private String sku;

	/**
	 * 城市编号
	 */
	private Integer areaNo;


	/**
	 * 商品编号
	 */
	private String pdName;


	/**
	 * 类目id
	 */
	private List<Long> frontCategoryIds;

}