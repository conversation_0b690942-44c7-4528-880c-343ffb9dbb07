package net.summerfarm.manage.application.service.major;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceLogQueryInput;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;

/**
 *
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
public interface MajorPriceLogQueryService {


    /**
     * @description: 新增
     * @return MajorPriceLogEntity
     **/
    PageInfo<MajorPriceLogEntity> getPage(MajorPriceLogQueryInput input);
}