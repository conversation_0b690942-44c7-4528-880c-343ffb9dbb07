package net.summerfarm.manage.application.inbound.controller.product.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description
 * @Date 2025/4/16 17:54
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarnTimeCommandInput {

    @NotBlank(message = "pdNo不能为空")
    private String pdNo;

    @NotNull(message = "warnTime不能为空")
    private Integer warnTime;

}
