package net.summerfarm.manage.application.service.product.validator;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.enums.BuyerSourceEnum;
import net.summerfarm.manage.application.inbound.controller.product.input.command.SkuBaseInfoCommandInput;
import net.summerfarm.manage.common.enums.SubTypeEnum;
import net.summerfarm.manage.common.util.VolumeUtil;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.goods.PopBuyerFacade;
import net.summerfarm.manage.facade.goods.dto.PopBuyerInfoDTO;
import net.summerfarm.repository.other.dto.Inventory;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminId;

/**
 * @Description 商品信息校验器
 * @Date 2024/7/2 15:42
 * @<AUTHOR>
 */
@Component
@Slf4j
public class InventoryValidator {
    @Resource
    private PopBuyerFacade popBuyerFacade;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    public void checkPopSpuPermission(List<Long> pdIdList) {
        if (CollectionUtils.isEmpty(pdIdList)) {
            return;
        }
        pdIdList.forEach(this::checkPopSpuPermission);
    }


    public void checkPopSpuPermission(Long pdId) {
        if (Objects.isNull(pdId)) {
            return;
        }
        try {
            Long adminId = getAdminId();
            // 根据adminId查询pop买手信息
            List<PopBuyerInfoDTO> popBuyerList = popBuyerFacade.queryPopBuyerByAdminIdList(Lists.newArrayList(adminId));
            if (CollectionUtils.isEmpty(popBuyerList) || Objects.isNull(popBuyerList.get(0))) {
                return;
            }
            PopBuyerInfoDTO popBuyerInfoDTO = popBuyerList.get(0);
            // 非临时账号直接通过
            if (!Objects.equals(BuyerSourceEnum.TEMP.getValue(), popBuyerInfoDTO.getSource())) {
                return;
            }
            // 查询商品信息
            List<InventoryEntity> inventoryList = inventoryQueryRepository.selectByPdId(pdId);
            if (CollectionUtils.isEmpty(inventoryList)) {
                return;
            }
            // 如果不存在有自己所属的sku，则提示报错
            boolean containSelfSku = inventoryList.stream()
                    .filter(item -> Objects.equals(SubTypeEnum.POP.getExtType(), item.getSubType()))
                    .map(InventoryEntity::getBuyerId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList()).contains(popBuyerInfoDTO.getBuyerId());
            ExceptionUtil.checkAndThrow(containSelfSku, "您没有权限操作该商品");
        } catch (BizException bizE) {
            log.warn("该账号无权限操作此商品信息，请联系管理员, pdId:{}", pdId);
            throw bizE;
        } catch (Exception e) {
            log.warn("checkPopSpuPermission 异常，不影响正常业务流程", e);
        }

    }

    public void checkPopSkuPermission(String sku) {
        try {
            if (StringUtils.isBlank(sku)) {
                return;
            }
            Long adminId = getAdminId();
            // 根据adminId查询pop买手信息
            List<PopBuyerInfoDTO> popBuyerList = popBuyerFacade.queryPopBuyerByAdminIdList(Lists.newArrayList(adminId));
            if (CollectionUtils.isEmpty(popBuyerList) || Objects.isNull(popBuyerList.get(0))) {
                return;
            }
            PopBuyerInfoDTO popBuyerInfoDTO = popBuyerList.get(0);
            // 非临时账号直接通过
            if (!Objects.equals(BuyerSourceEnum.TEMP.getValue(), popBuyerInfoDTO.getSource())) {
                return;
            }
            // 查询商品信息
            InventoryQueryParam queryParam = new InventoryQueryParam();
            queryParam.setSku(sku);
            InventoryEntity inventory = inventoryQueryRepository.querySelectOne(queryParam);
            if (Objects.isNull(inventory)) {
                return;
            }
            // 如果不存在有自己所属的sku，则提示报错
            ExceptionUtil.checkAndThrow(Objects.equals(popBuyerInfoDTO.getBuyerId(), inventory.getBuyerId()), "您没有权限操作该商品");
        } catch (BizException bizE) {
            log.warn("该账号无权限操作此商品信息，请联系管理员, sku:{}", sku);
            throw bizE;
        } catch (Exception e) {
            log.warn("checkPopSkuPermission 异常，不影响正常业务流程", e);
        }

    }

    public void checkPopSkuExist(String sku) {
        if (StringUtils.isBlank(sku)) {
            return;
        }
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(sku);
        InventoryEntity inventory = inventoryQueryRepository.querySelectOne(queryParam);
        ExceptionUtil.checkAndThrow(Objects.nonNull(inventory), "未查询到该pop商品");
        ExceptionUtil.checkAndThrow(Objects.equals(SubTypeEnum.POP.getExtType(), inventory.getSubType()), "非pop类型商品，不支持该操作");
    }

    public void checkSkuBaseInfoUpdate(SkuBaseInfoCommandInput commandInput) {
        ExceptionUtil.checkAndThrow(Objects.nonNull(commandInput)
                && StringUtils.isNotBlank(commandInput.getSku()), "必传参数缺失-sku");
        if (StringUtils.isNotBlank(commandInput.getVolume())) {
            ExceptionUtil.checkAndThrow(VolumeUtil.isValidVolumeStr(commandInput.getVolume()), "体积格式有误，请确认后重新填写");
        }

    }


}
