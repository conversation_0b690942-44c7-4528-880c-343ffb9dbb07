package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.manage.application.inbound.controller.product.input.InventoryUpdateInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductUpdateInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyValueInput;
import net.summerfarm.manage.application.inbound.controller.product.input.command.WarnTimeCommandInput;
import net.summerfarm.manage.application.service.product.converter.ProductInputConverter;
import net.summerfarm.manage.application.service.product.converter.ProductsPropertyMappingConverter;
import net.summerfarm.manage.application.service.product.converter.ProductsPropertyValueConverter;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.summerfarm.manage.application.service.product.mall.ProductCommandService;
import net.summerfarm.manage.application.service.product.validator.InventoryValidator;
import net.summerfarm.manage.common.constants.RocketMqConstant;
import net.summerfarm.manage.common.enums.products.ProductsEnum;
import net.summerfarm.manage.common.input.order.ProductInsertResponsibleMsgDTO;
import net.summerfarm.manage.common.middleware.MQOperator;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.domain.product.service.InventoryCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductsCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductsPropertyMappingCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductsPropertyValueCommandDomainService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminId;
import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminName;
import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;

/**
 * @ClassName ProductCommandServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 10:56 2024/5/7
 * @Version 1.0
 **/
@Slf4j
@Service
public class ProductCommandServiceImpl implements ProductCommandService {

    @Resource
    private ProductsQueryRepository productsQueryRepository;

    @Resource
    private ProductsCommandDomainService productsCommandDomainService;

    @Resource
    private ProductsPropertyValueCommandDomainService propertyValueCommandDomainService;

    @Resource
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;

    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Resource
    private GoodsLocationDetailQueryRepository goodsLocationDetailQueryRepository;

    @Resource
    private GoodsLocationQueryRepository goodsLocationQueryRepository;

    @Resource
    private MQOperator mqOperator;

    @Resource
    private AdminQueryRepository adminQueryRepository;

    @Resource
    private InventoryCommandDomainService inventoryDomainService;

    @Resource
    private ProductsPropertyMappingCommandDomainService propertyMappingCommandDomainService;

    @Resource
    private InventoryCommandService inventoryCommandService;

    @Resource
    private InventoryValidator inventoryValidator;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductInfo(ProductUpdateInput input) {
        Long pdId = input.getPdId();
        inventoryValidator.checkPopSpuPermission(pdId);
        ProductsEntity productsEntity = productsQueryRepository.selectById(pdId);
        if (productsEntity == null) {
            throw new BizException("商品信息不存在");
        }

        //三级类目改变需要删除spu属性
        boolean categoryChanged = !Objects.equals(productsEntity.getCategoryId(), input.getCategoryId());
        if (categoryChanged) {
            propertyValueCommandDomainService.deleteSpuProperty(pdId);
        }

        //校验所有关键属性值格式是否正确
        List<ProductsPropertyValueQueryParam> params = ProductsPropertyValueConverter.inputToParam(input.getKeyValueList());
        List<String> name = productsPropertyValueQueryRepository.updateCheckValueFormat(params);
        if (!CollectionUtils.isEmpty(name)) {
            log.warn("销售属性填写格式错误:{}", name);
            throw new BizException("销售属性填写格式错误：" + name.get(0));
        }

        //校验关键属性值是否都填写
        List<ProductsPropertyEntity> effectKeyPropertyList = productsPropertyValueQueryRepository.selectEffectKeyPropertyByCategoryId(input.getCategoryId());
        List<ProductsPropertyValueEntity> keyValueList = productsPropertyValueQueryRepository.listBySkuAndPdid(null, pdId);
        for (ProductsPropertyEntity property : effectKeyPropertyList) {
            if (keyValueList.stream().anyMatch(fill -> Objects.equals(fill.getProductsPropertyId(), property.getId()))) {
                continue;
            }
            if (input.getKeyValueList().stream().noneMatch(val -> Objects.equals(property.getId(), val.getProductsPropertyId()))) {
                throw new BizException("请填写关键属性：" + property.getName());
            }
        }

        //校验新增销售属性值是否都填写 且 格式是否正确（修改时这里的参数只包含新增的销售属性）
        List<InventoryEntity> inventories = inventoryQueryRepository.selectByPdId(pdId);
        List<ProductsPropertyValueQueryParam> propertyValueQueryParams;
        if (!CollectionUtils.isEmpty(input.getSalePropertyList())) {
            if (!CollectionUtils.isEmpty(inventories)) {
                Map<String, List<ProductsPropertyValueInput>> saleValueMap = input.getSaleValueList().stream()
                        .collect(Collectors.groupingBy(ProductsPropertyValueInput::getSku));
                for (List<ProductsPropertyValueInput> valueList : saleValueMap.values()) {
                    for (ProductsPropertyInput property : input.getSalePropertyList()) {
                        boolean flag = valueList.stream().noneMatch(el -> Objects.equals(property.getId(), el.getProductsPropertyId()));
                        if (flag) {
                            throw new BizException("请填写销售属性：" + property.getName());
                        }
                    }
                    propertyValueQueryParams = ProductsPropertyValueConverter.inputToParam(valueList);
                    List<String> wrongSalePropertyList = productsPropertyValueQueryRepository.updateCheckValueFormat(propertyValueQueryParams);
                    if (!CollectionUtils.isEmpty(wrongSalePropertyList)) {
                        log.warn("销售属性填写格式错误:{}", wrongSalePropertyList);
                        throw new BizException("销售属性填写格式错误：" + wrongSalePropertyList.get(0));
                    }
                }
            }
        }

        //修改温区时校验
        if (!Objects.equals(productsEntity.getStorageLocation(), input.getStorageLocation())
                && input.getStorageLocation() != null && !CollectionUtils.isEmpty(inventories)) {
            List<String> skus = inventories.stream().map(InventoryEntity::getSku).collect(Collectors.toList());
            List<GoodsLocationDetailEntity> query = goodsLocationDetailQueryRepository.selectBySkus(skus);
            Map<String, List<GoodsLocationDetailEntity>> listMap = query.stream().collect(Collectors.groupingBy(GoodsLocationDetailEntity::getSku));
            inventories.forEach(x -> {
                List<GoodsLocationDetailEntity> detailEntities = listMap.get(x.getSku());
                if (!CollectionUtils.isEmpty(detailEntities)) {
                    GoodsLocationDetailEntity detailVO = detailEntities.get(NumberUtils.INTEGER_ZERO);
                    String glNo = detailVO.getGlNo();
                    GoodsLocationEntity goodsLocation = goodsLocationQueryRepository.selectByGlNo(glNo);
                    if (goodsLocation != null && !Objects.equals(input.getStorageLocation(), goodsLocation.getTemperature())) {
                        throw new BizException("存在有货位的sku,无法更改温区");
                    }
                }
            });
        }

        //判断是否是上新审核
        if (input.getAuditFlag() != null) {
            //校验
            operateCheck(input);
            commonCheck(input);

            //校验sku信息
            List<InventoryUpdateInput> skuList = input.getSkuList();
            for (InventoryUpdateInput sku : skuList) {
                if (StringUtils.isBlank(sku.getUnit())) {
                    throw new BizException("请填写SKU包装");
                }
                if (sku.getType() == null) {
                    throw new BizException("请填写SKU性质");
                }
                if (StringUtils.isBlank(sku.getVolume())) {
                    throw new BizException("请填写SKU体积");
                }
                if (StringUtils.isBlank(sku.getWeightNum())) {
                    throw new BizException("请填写SKU重量");
                }
            }

            //是否能够审核
            boolean outFlag = ProductsEnum.Outdated.CREATING.getCode().equals(productsEntity.getOutdated());
            boolean auditFlag = ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal() == productsEntity.getAuditStatus();

            if (!(outFlag && auditFlag)) {
                throw new BizException("当前商品不可审核上线");
            }
            input.setAuditTime(LocalDateTime.now());
            input.setAuditor(getAdminId() == null ? null : getAdminId().intValue());
            if (input.getAuditFlag()) {
                input.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                input.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                //上新审核成功插入采购负责人
                insertResponsible(productsEntity);
            } else {
                input.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
            }

            //处理sku上新
            InventoryQueryParam query = new InventoryQueryParam();
            query.setPdId(input.getPdId());
            query.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
            query.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
            List<InventoryEntity> auditSkuList = inventoryQueryRepository.selectList(query);
            InventoryCommandParam inventoryCommandParam;
            for (InventoryEntity auditSku : auditSkuList) {
                inventoryCommandParam = new InventoryCommandParam();
                inventoryCommandParam.setSku(auditSku.getSku());
                inventoryCommandParam.setAuditTime(LocalDateTime.now());
                if (input.getAuditFlag()) {
                    inventoryCommandParam.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                    inventoryCommandParam.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                } else {
                    inventoryCommandParam.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
                }
                log.info("updateSpuAndSku Operator:{},:{}",getAdminName(), JSON.toJSONString(auditSku));
                inventoryDomainService.update(inventoryCommandParam);
            }

        } else {
            input.setAuditStatus(null);
        }

        ProductsCommandParam productsCommandParam = ProductInputConverter.inputToCommonParam(input);
        int rs = productsCommandDomainService.update(productsCommandParam);
        if (rs <= 0) {
            throw new BizException(ResultConstant.DEFAULT_FAILED);
        }

        //更新关键属性值
        if (!CollectionUtils.isEmpty(input.getKeyValueList())) {
            List<ProductsPropertyValueCommandParam> commandParams =  ProductsPropertyValueConverter.inputToCommonParam(input.getKeyValueList());
            propertyValueCommandDomainService.addKeyPropertyValue(input.getPdId(), commandParams);
        }

        //更新销售属性映射
        if (!CollectionUtils.isEmpty(input.getSalePropertyList())){
            propertyMappingCommandDomainService.addSalePropertyMapping(input.getPdId(), ProductsPropertyMappingConverter.inputToCommonParam(input.getSalePropertyList()));
        }

        //添加销售属性值
        if (!CollectionUtils.isEmpty(input.getSaleValueList())) {
            List<InventoryEntity> inventoryEntities = inventoryQueryRepository.listBySkus(input.getSaleValueList().stream().map(ProductsPropertyValueInput::getSku).collect(Collectors.toList()));
            Map<String, InventoryEntity> inventoryEntityMap = inventoryEntities.stream().collect(Collectors.toMap(InventoryEntity::getSku, inventoryEntity -> inventoryEntity));
            Map<String, List<ProductsPropertyValueInput>> saleValueMap = input.getSaleValueList().stream().collect(Collectors.groupingBy(ProductsPropertyValueInput::getSku));
            for (Map.Entry<String, List<ProductsPropertyValueInput>> entry : saleValueMap.entrySet()) {
                propertyValueCommandDomainService.addSalePropertyValue(entry.getKey(), input.getPdId(), ProductsPropertyValueConverter.inputToCommonParam(entry.getValue()));

                //更新sku规格
                inventoryCommandService.updateWeight(inventoryEntityMap.get(entry.getKey()), null);
            }
        }
    }

    @Override
    public void batchUpdateWarnTime(List<WarnTimeCommandInput> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return;
        }
        List<String> pdNoList = inputList.stream().map(WarnTimeCommandInput::getPdNo).distinct().collect(Collectors.toList());
        Map<String, Integer> warnTimeMap = inputList.stream().collect(Collectors.toMap(WarnTimeCommandInput::getPdNo, WarnTimeCommandInput::getWarnTime, (a, b) -> a));

        List<List<String>> partPdNoList = ListUtil.partition(pdNoList, 100);
        partPdNoList.forEach(partNoList -> {
            List<ProductsEntity> productsEntityList = productsQueryRepository.selectListByPdNo(partNoList);
            if (CollectionUtils.isEmpty(productsEntityList)) {
                return;
            }
            productsEntityList.forEach(productsEntity -> {
                String pdNo = productsEntity.getPdNo();
                Integer inputWarnTime = warnTimeMap.get(pdNo);
                if (!Objects.equals(inputWarnTime, productsEntity.getWarnTime())) {
                    ProductsCommandParam commandParam = new ProductsCommandParam();
                    commandParam.setPdId(productsEntity.getPdId());
                    commandParam.setWarnTime(inputWarnTime);
                    productsCommandDomainService.update(commandParam);
                    log.warn("到期预警时长更新成功，pdId:{},pdNo:{},从{}更新为{}", productsEntity.getPdId(),
                            productsEntity.getPdNo(), productsEntity.getWarnTime(), inputWarnTime);
                }

            });

        });

    }

    private void insertResponsible(ProductsEntity productsVO) {
        if (productsVO.getCreator() == null){
            return;
        }
        AdminEntity admin = adminQueryRepository.selectByPrimaryKey(productsVO.getCreator().longValue());
        if (admin != null) {
            ProductInsertResponsibleMsgDTO msgDTO = new ProductInsertResponsibleMsgDTO();
            msgDTO.setPdId(productsVO.getPdId());
            msgDTO.setCreateType(productsVO.getCreateType());
            msgDTO.setCreatorId(productsVO.getCreator());
            msgDTO.setCreatorName(admin.getRealname());
            msgDTO.setOperatorId(getAdminId() == null ? null : getAdminId().intValue());
            msgDTO.setOperatorName(getAdminName());
            msgDTO.setPdNo(productsVO.getPdNo());
            mqOperator.sendDataToQueue(RocketMqConstant.Topic.TOPIC_PRODUCT_INSERT_RESPONSIBLE,
                    RocketMqConstant.SendCouponTag.TAG_PRODUCT_UPDATE_RESPONSIBLE,JSON.toJSONString(msgDTO));
        }
    }

    private void commonCheck(ProductUpdateInput req) {
        if (req.getCategoryId() == null) {
            throw new BizException(ResultConstant.PARAM_FAULT);
        }
        if (StringUtils.isBlank(req.getRealName())) {
            throw new BizException("请填写实物名称");
        }
        if (StringUtils.isBlank(req.getStorageLocation())) {
            throw new BizException("请填写贮存区域");
        }
        if (req.getQualityTime() == null || StringUtils.isBlank(req.getQualityTimeUnit())) {
            throw new BizException("请填写保质期时长");
        }
        Long tenantId = req.getTenantId();
        List<ProductsPropertyEntity> propertyList = productsPropertyValueQueryRepository.selectEffectKeyPropertyByCategoryId(req.getCategoryId());
        for (ProductsPropertyEntity property : propertyList) {
            if (req.getKeyValueList().stream().noneMatch(el -> Objects.equals(property.getId(), el.getProductsPropertyId()))) {
                throw new BizException("请填写关键属性：" + property.getName());
            }
        }
        if (CollectionUtils.isEmpty(req.getSalePropertyList())) {
            throw new BizException("请勾选销售属性");
        }
        boolean popProducts = !CollectionUtils.isEmpty(req.getSkuList()) &&
                req.getSkuList().stream().map(InventoryUpdateInput::getSubType)
                        .collect(Collectors.toList()).contains(SubAgentTypeEnum.POP.getType());
        if (Objects.nonNull(tenantId) && tenantId > XM_TENANTID) {
            //校验是否存在相同关键属性spu
            List<ProductsPropertyValueQueryParam> params = ProductsPropertyValueConverter.inputToParam(req.getKeyValueList());
            List<String> pdNoList = productsPropertyValueQueryRepository.equalsSpuByKeyProperty(req.getPdNo(), req.getCategoryId(), req.getCreateType(), params);
            if (!CollectionUtils.isEmpty(pdNoList)) {
                throw new BizException("有相同SPU，SPU编码为：" + pdNoList.get(0));
            }
        } else if(popProducts) {
            String pdNo = req.getPdNo();
            // pop商品校验是否存在相同关键属性spu
            List<ProductsPropertyValueQueryParam> params = ProductsPropertyValueConverter.inputToParam(req.getKeyValueList());
            List<String> pdNoList = productsPropertyValueQueryRepository.equalsSpuByKeyProperty(req.getPdNo(), req.getCategoryId(), req.getCreateType(), params);
            // 将自身的pdNo排除
            pdNoList = pdNoList.stream().filter(existPdNo -> !Objects.equals(existPdNo, pdNo)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pdNoList)) {
                List<ProductsEntity> productsList = productsQueryRepository.selectListByPdNo(pdNoList);
                if (!CollectionUtils.isEmpty(productsList)
                        && productsList.stream().map(ProductsEntity::getPdName).collect(Collectors.toList()).contains(req.getRealName())) {
                    throw new BizException("有相同SPU，SPU编码为：" + pdNoList.get(0));
                }
            }
        } else {
            //校验是否存在相同关键属性spu
            List<ProductsPropertyValueQueryParam> params = ProductsPropertyValueConverter.inputToParam(req.getKeyValueList());
            List<String> pdNoList = productsPropertyValueQueryRepository.equalsSpuByKeyProperty(req.getPdNo(), req.getCategoryId(), req.getCreateType(), params);
            int i = productsQueryRepository.selectByPdNameCount(req.getRealName());
            if (!CollectionUtils.isEmpty(pdNoList) && i > 0) {
                throw new BizException("有相同SPU，SPU编码为：" + pdNoList.get(0));
            }
        }
    }

    private void operateCheck(ProductUpdateInput req) {
        if (StringUtils.isBlank(req.getPdName())) {
            throw new BizException("请填写商品名称");
        }
        if (req.getWarnTime() == null) {
            throw new BizException("请填写到期预警");
        }
//        if (StringUtils.isBlank(req.getPicturePath())) {
//            return AjaxResult.getErrorWithMsg("请上传橱窗图");
//        }
//        if (StringUtils.isBlank(req.getDetailPicture())) {
//            return AjaxResult.getErrorWithMsg("请上传详情图");
//        }
        if (StringUtils.isBlank(req.getAfterSaleTime(), req.getAfterSaleType(), req.getRefundType())) {
            throw new BizException("请填写售后信息");
        }
        if (CollectionUtils.isEmpty(req.getSkuList())) {
            throw new BizException("请添加sku信息");
        }

        for (InventoryUpdateInput sku : req.getSkuList()) {
            if (sku.getBaseSaleQuantity() == null) {
                throw new BizException("请填写最小起售量");
            }
            if (sku.getBaseSaleUnit() == null) {
                throw new BizException("请填写最小起售规格");
            }
            if (sku.getExtType() == null) {
                throw new BizException("请填写SKU商品性质");
            }
        }
    }
}
