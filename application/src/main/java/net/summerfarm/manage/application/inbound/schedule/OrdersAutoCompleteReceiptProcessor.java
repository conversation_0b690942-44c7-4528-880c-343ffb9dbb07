package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/12/3 17:10
 * @PackageName:net.summerfarm.manage.application.inbound.schedule
 * @ClassName: OrdersAutoCompleteReceiptProcessor
 * @Description: 售后订单自动完成确认收货
 * @Version 1.0
 */
@Component
@Slf4j
public class OrdersAutoCompleteReceiptProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private OrderCommandService orderCommandService;

    private static final String ORDER_NO_KEY = "orderNo";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("售后订单自动完成确认收货 start :{}", LocalDateTime.now());
        String orderNo = null;
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());
            if (jsonObject.containsKey(ORDER_NO_KEY)) {
                orderNo = jsonObject.getString(ORDER_NO_KEY);
            }
        }
        orderCommandService.autoCompleteReceipt(orderNo);
        log.info("售后订单自动完成确认收货 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
