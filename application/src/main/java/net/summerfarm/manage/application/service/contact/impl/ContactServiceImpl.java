package net.summerfarm.manage.application.service.contact.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.contact.assembler.ContactAssembler;
import net.summerfarm.manage.application.inbound.controller.contact.input.ContactFrequencyQueryInput;
import net.summerfarm.manage.application.inbound.controller.contact.input.ContactInsertInput;
import net.summerfarm.manage.application.inbound.controller.contact.vo.ContactVO;
import net.summerfarm.manage.application.service.contact.ContactService;
import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchant.service.ContactDomainService;
import net.summerfarm.manage.facade.deliivery.ContactDeliveryRuleQueryFacade;
import net.summerfarm.manage.facade.deliivery.dto.ContactDTO;
import net.summerfarm.manage.facade.deliivery.input.ContactDeliveryRuleQueryInput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/13 17:14:22
 */
@Service
@Slf4j
public class ContactServiceImpl implements ContactService {

    @Resource
    private ContactDeliveryRuleQueryFacade ruleQueryFacade;
    @Resource
    private ContactDomainService contactDomainService;
    @Resource
    private MerchantQueryRepository merchantQueryRepository;

    @Override
    public ContactVO queryFrequencyContact(ContactFrequencyQueryInput input) {
        ContactDeliveryRuleQueryInput queryInput = new ContactDeliveryRuleQueryInput();
        queryInput.setOutBusinessNo(String.valueOf(input.getContactId()));
        ContactDTO contactDTO = ruleQueryFacade.queryXmContactDeliveryRule(queryInput);
        return ContactAssembler.toContactVO(contactDTO);
    }

    @Override
    public void addContactWithoutAudit(ContactInsertInput input) {
        log.info("内部新增免审地址：{}", JSON.toJSONString(input));
        List<Long> mId = input.getMIds();
        Integer storeNo = input.getStoreNo();
        List<MerchantEntity> list = merchantQueryRepository.selectByIds(mId);
        if(CollUtil.isEmpty(list)) {
            log.error("门店不存在!");
        }
        list.forEach(merchantEntity -> contactDomainService.addContactWithoutAudit(merchantEntity, storeNo));
    }
}
