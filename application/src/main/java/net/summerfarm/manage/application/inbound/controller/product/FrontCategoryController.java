package net.summerfarm.manage.application.inbound.controller.product;

import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.application.inbound.controller.product.vo.FrontCategoryVO;
import net.summerfarm.manage.application.service.product.mall.FrontCategoryQueryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Title 前台类目
 * @Description 前台类目功能模块
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 */
@RestController
@RequestMapping(value="/frontCategory")
public class FrontCategoryController{

	@Autowired
	private FrontCategoryQueryService frontCategoryQueryService;


	/**
	 * pop前台类目列表(父子结构)
	 * @return FrontCategoryVO
	 */
	@GetMapping(value="/query/pop-tree")
	public CommonResult<List<FrontCategoryVO>> getPopTree(){
		List<FrontCategoryVO> list = frontCategoryQueryService.getPopTree();
		return CommonResult.ok(list);
	}


}

