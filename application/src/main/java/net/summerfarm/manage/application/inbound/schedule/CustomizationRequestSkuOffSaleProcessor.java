package net.summerfarm.manage.application.inbound.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestSkuMappingRepository;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 定制需求 复制出的sku自动下架
 **/
@Component
@Slf4j
public class CustomizationRequestSkuOffSaleProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CustomizationRequestSkuMappingRepository customizationRequestSkuMappingRepository;
    @Autowired
    private AreaSkuCommandService areaSkuCommandService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("定制需求 复制出的sku自动下架 start :{}", LocalDateTime.now());
        //查询创建时间超过半小时的记录
        LocalDateTime halfHourAgo = LocalDateTime.now().minusMinutes(30);
        List<CustomizationRequestSkuMappingEntity> expiredMappings = customizationRequestSkuMappingRepository.findByCreateTimeBefore(halfHourAgo);
        List<String> skus = expiredMappings.stream ().map (mapping -> mapping.getSku ()).collect (Collectors.toList ());
        if(CollectionUtil.isNotEmpty (skus)){
            areaSkuCommandService.offSaleBySkus (skus);
        }
        log.info("定制需求 复制出的sku自动下架 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
