package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.customization.CustomizationRequestService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Description 定制需求到货的提醒 - 定时任务
 * <AUTHOR>
 * @Version 1.0
 **/
@Component
@Slf4j
public class CustomizationRequestInventoryProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private CustomizationRequestService customizationRequestService;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("定制需求到货的提醒 start :{}", LocalDateTime.now());
        customizationRequestService.sendArrivalNotice();
        log.info("定制需求到货的提醒 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
