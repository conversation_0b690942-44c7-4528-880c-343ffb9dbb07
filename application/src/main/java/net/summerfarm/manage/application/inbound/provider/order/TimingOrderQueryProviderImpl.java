package net.summerfarm.manage.application.inbound.provider.order;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.order.TimingOrderQueryProvider;
import net.summerfarm.client.req.order.TimingOrderNoFreezeQueryReq;
import net.summerfarm.client.req.order.TimingOrderNoSetQueryReq;
import net.summerfarm.client.resp.order.TimingOrderNoFreezeResp;
import net.summerfarm.client.resp.order.TimingOrderNoSetResp;
import net.summerfarm.manage.application.inbound.provider.order.converter.TimingOrderConverter;
import net.summerfarm.manage.application.service.order.OrderQueryService;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2024/6/5 13:52<br/>
 *
 * <AUTHOR> />
 */
@DubboService
@Slf4j
public class TimingOrderQueryProviderImpl implements TimingOrderQueryProvider {

    @Resource
    private OrderQueryService orderQueryService;

    @Override
    public DubboResponse<List<TimingOrderNoFreezeResp>> queryNoFreezeInventoryProxySaleNoWarehouseOrderInfo(@Valid TimingOrderNoFreezeQueryReq req) {
        List<NoFreezeProxySaleNoWareNoSkuFlatObject> noFreezeProxySaleNoWareNoSkus = orderQueryService.queryTimingOrderNoFreezeProxySaleNoWarehouse(req.getStartDate(), req.getEndDate(), req.getStoreNo());
        return DubboResponse.getOK(noFreezeProxySaleNoWareNoSkus.stream().map(TimingOrderConverter::flat2NoFreezeResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<TimingOrderNoSetResp>> queryNoSetOrderProxySaleNoWarehouseOrderInfo(@Valid TimingOrderNoSetQueryReq req) {
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuFlatObjects = orderQueryService.queryTimingOrderNoSetOrderProxySaleNoWarehouseOrderInfo(req.getStoreNo());
        return DubboResponse.getOK(skuFlatObjects.stream().map(TimingOrderConverter::flat2TimingOrderNoSetResp).collect(Collectors.toList()));
    }
}
