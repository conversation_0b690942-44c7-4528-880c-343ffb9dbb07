package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2024/11/15 14:55
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalPopProductMappingVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 鲜沐sku编码
     */
    private String xmSkuCode;

    /**
     * 名称
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 三级类目id
     */
    private Long categoryId;

    /**
     * 三级类目名称
     */
    private String categoryName;

    /**
     * 绑定的外部品信息
     */
    private AppPopBiaoguoTopSaleSkuVO popBiaoguoTopSaleSkuVO;

}
