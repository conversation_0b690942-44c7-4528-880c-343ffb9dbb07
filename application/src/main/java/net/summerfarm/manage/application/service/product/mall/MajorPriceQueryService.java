package net.summerfarm.manage.application.service.product.mall;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceDownloadInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLargeAreaVO;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceRepeatQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceVO;
import net.summerfarm.manage.domain.major.dto.QuotationExcelDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @date 2024-04-08 15:19:21
 * @version 1.0
 */
public interface MajorPriceQueryService {


    PageInfo<MajorPriceLargeAreaVO> majorPricePage(MajorPriceQueryInput majorPriceInput);



    PageInfo<MajorPriceVO> majorPriceCityPage(MajorPriceQueryInput majorPriceInput);



    Long majorPriceDownload(MajorPriceDownloadInput majorPriceInput);


    /**
     * 查询是否存在时间重复的报价单
     1、 未来（不考虑是否提交）生效的报价单，不论时间是否重叠 都算重复
     2、 生效中的报价单 时间重叠 算重复
     * @param input
     * @return 返回错误提示
     */
    String repeatQuery(MajorPriceRepeatQueryInput input);

}