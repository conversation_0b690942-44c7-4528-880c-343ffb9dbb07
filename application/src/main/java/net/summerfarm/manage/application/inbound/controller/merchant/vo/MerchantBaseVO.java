package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 门店的基础vo
 * @Date: 2023/10/24 11:37
 * @Author: sg
 */

@Data
public class MerchantBaseVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;
    /**
     * 店铺id
     */
    private Long mId;

    /**
     * 商户名称
     */
    private String mname;

    /**
     * 主联系人
     */
    private String mcontact;

    /**
     * 手机
     */
    private String phone;

    /**
     * 审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑 4、注销
     */
    private Integer islock;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     *
     */
    private Integer areaNo;

    /**
     *     ADMIN(1, "大客户"),
     *         MERCHANT(2, "单店"),
     *         SAAS(3, "saas品牌客户");
     */
    private String size;

    /**
     * 客户类型
     */
    private String type;

    /**
     * 地推人员
     */
    private String adminRealName;

    /**
     *运营服务大区域名称
     */
    private String areaName;

    /**
     * 工商名称？？？ 发票本身表里面拿
     */
    private String invoiceTitle;

    /**
     *  merchant 里取  1 账期 2  现结
     */
    private Integer direct;
}
