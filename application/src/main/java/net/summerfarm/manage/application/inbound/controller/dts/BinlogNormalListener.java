package net.summerfarm.manage.application.inbound.controller.dts;

import lombok.extern.slf4j.Slf4j;

import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.manage.application.service.dts.DbTableDml;
import net.summerfarm.manage.application.service.dts.DbTableDmlFactory;
import net.summerfarm.manage.common.constants.RocketMqConstant;
import net.summerfarm.manage.common.dto.DtsModel;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton binlog顺序消费
 * @date 2024/4/26 16:20
 */
@Component
@Slf4j
@MqListener(topic = RocketMqMessageConstant.MYSQL_BINLOG,
        tag = RocketMqConstant.NormalTag.TAG,
        consumerGroup = "GID_sf_mall_manage_binlog_normal",consumeThreadMax = 16,consumeThreadMin = 16)
public class BinlogNormalListener extends AbstractMqListener<DtsModel> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModel dtsModel) {
        log.info("rocketmq 收到消息，事件类型：{}，recordId/msg-key：{}， 表：{}.{}",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());

        DbTableDml creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            creator.handle(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:{},请先注册后再做处理!", dtsModel.getTable());
        }
    }
}
