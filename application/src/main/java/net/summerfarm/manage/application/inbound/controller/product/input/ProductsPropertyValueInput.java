package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProductsPropertyValueInput
 * @Description
 * <AUTHOR>
 * @Date 17:54 2024/4/30
 * @Version 1.0
 **/
@Data
public class ProductsPropertyValueInput implements Serializable {
    /**
     * pd_id
     */
    private Long pdId;

    /**
     * sku
     */
    private String sku;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * 创建人
     */
    private String creator;
}
