package net.summerfarm.manage.application.inbound.provider.product.saas;

import com.google.common.base.Throwables;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.product.saas.SaasInterfaceServiceProvider;
import net.summerfarm.client.req.product.saas.SummerFarmSkuPriceInfoReq;
import net.summerfarm.client.resp.product.saas.MallPrice4SaasResp;
import net.summerfarm.client.resp.product.saas.SummerFarmSkuPriceInfoResp;
import net.summerfarm.manage.application.inbound.provider.product.saas.converter.SkuPriceInfoConverter;
import net.summerfarm.manage.application.service.product.saas.PriceQueryService;
import net.summerfarm.manage.application.service.product.saas.ProductQueryService;
import net.summerfarm.manage.application.service.product.saas.dto.MallPrice4SaasDTO;
import net.summerfarm.manage.application.service.product.saas.dto.SummerFarmSkuPriceInfoDTO;
import net.summerfarm.manage.common.query.product.SummerFarmSkuPriceInfoInput;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/11/8
 */
@Slf4j
@DubboService
@Component
public class SaasSkuQueryProviderImpl implements SaasInterfaceServiceProvider {

    @Resource
    private ProductQueryService productQueryService;

    @Resource
    private PriceQueryService priceQueryService;

    /**
     * 查询报价单区间
     *
     * @param reqList 报价单查询
     * @return
     */
    public DubboResponse<List<SummerFarmSkuPriceInfoResp>> queryAdminSkuPricingInfo(
            List<SummerFarmSkuPriceInfoReq> reqList) {
        List<SummerFarmSkuPriceInfoResp> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(reqList)) {
            return DubboResponse.getOK(resultList);
        }
        List<SummerFarmSkuPriceInfoInput> inputList = SkuPriceInfoConverter.toSummerFarmSkuPriceInfoInputList(
                reqList);
        try {
            List<SummerFarmSkuPriceInfoDTO> infoDTOList = productQueryService.queryAdminSkuPricingInfo(
                    inputList);
            resultList = SkuPriceInfoConverter.toSummerFarmSkuPriceInfoRespList(
                    infoDTOList);
        } catch (Exception e) {
            log.warn("Invoke SaasInterfaceServiceProvider.queryAdminSkuPricingInfo error,cause:{}",
                    Throwables.getStackTraceAsString(e));
            return DubboResponse.getDefaultError(e.getMessage());
        }
        return DubboResponse.getOK(resultList);
    }

    @Override
    public DubboResponse<MallPrice4SaasResp> queryMallPriceInfo4Saas(Integer areaNo, String sku, Long adminId) {
        MallPrice4SaasDTO dto = priceQueryService.queryMallPriceInfo4Saas (areaNo, sku, adminId);
        // 如果没有创建报价或者都是商城价，使用原价
        return DubboResponse.getOK(SkuPriceInfoConverter.toMallPrice4SaasResp(dto));
    }
}
