package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;
import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryQueryService;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.xianmu.oss.config.OssProperties;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.helper.XianMuOssHelper;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EsSynonymAutoImportProcessor extends XianMuJavaProcessorV2 {
    @Value("${synonym.oss.bucketName:xm-es-data}")
    private String bucketName;

    @Value("${synonym.oss.objectName:test_synonym.txt}")
    private String objectName;

    @Resource
    private ProductSearchSynonymDictionaryQueryService productSearchSynonymDictionaryQueryService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String jobParameters = context.getJobParameters();
        if (StringUtils.isBlank(jobParameters)) {
            jobParameters = context.getInstanceParameters();
        }
        JobParam jobParam = JSONObject.parseObject(jobParameters, JobParam.class);
        uploadSynonymFileToOSS(jobParam);
        return new ProcessResult(true, "success");
    }

    /**
     * 上传同义词文件到oss
     * 1.从同义词库中查询所有的同义词
     * 2.过滤空行
     * 3.将同义词连成一个string
     * 4.上传到oss
     */
    private void uploadSynonymFileToOSS(JobParam jobParam) {
        if (null == jobParam || StringUtils.isBlank(jobParam.getBucketName()) || StringUtils.isBlank(jobParam.getObjectName())) {
            // 如果没有配置，则使用默认的配置
            jobParam = new JobParam();
            jobParam.setBucketName(bucketName);
            jobParam.setObjectName(objectName);
        }
        log.info("开始上传同义词文件到oss, jobParam:{}", jobParam);
        long startTime = System.currentTimeMillis();
        ProductSearchSynonymDictionaryQueryInput input = new ProductSearchSynonymDictionaryQueryInput();
        input.setPageIndex(1);
        input.setPageSize(20000); // 这里限制一下大小，如果太大了，则只返回20000条记录
        PageInfo<ProductSearchSynonymDictionaryEntity> synonymContent = productSearchSynonymDictionaryQueryService.getPage(input);
        if (synonymContent == null || synonymContent.getList() == null || synonymContent.getList().isEmpty()) {
            log.info("没有同义词数据，不进行上传");
            return;
        }
        String content = synonymContent.getList().stream().map(ProductSearchSynonymDictionaryEntity::getSynonymTerms).filter(s -> s != null && !s.trim().isEmpty()) // 过滤空行
                .collect(Collectors.joining("\n"));
        if (content.isEmpty()) {
            log.info("没有同义词数据，不进行上传");
            return;
        }
        OssProperties ossProperties = XianMuOssHelper.getOssProperties(OSSExpiredLabelEnum.NO_EXPIRATION);
        ossProperties.setBucketName(jobParam.getBucketName());
        OSS ossClient = XianMuOssHelper.buildOssClient(ossProperties);
        Map<String, String> tags = new HashMap<>(16);
        // 依次填写对象标签的键（例如owner）和值（例如John）。
        tags.put(OSSExpiredLabelEnum.NO_EXPIRATION.getKey(), OSSExpiredLabelEnum.NO_EXPIRATION.getValue());
        // 在HTTP header中设置标签信息。
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("text/plain;charset=UTF-8");
        metadata.setObjectTagging(tags);
        // 上传文件的同时设置标签信息。
        PutObjectResult result = ossClient.putObject(ossProperties.getBucketName(), jobParam.getObjectName(), new ByteArrayInputStream(content.getBytes(Charset.forName("UTF-8"))), metadata);
        long endTime = System.currentTimeMillis();
        log.info("完成Oss文件上传，fileName={}, size={}, 用时={}ms, result:{}", jobParam.getObjectName(), endTime - startTime, synonymContent.getList().size(), result);
    }

    @Data
    public static class JobParam {
        private String bucketName;
        private String objectName;
    }
}
