package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.input.AreaSkuQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.AreaSkuVO;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;
import net.summerfarm.manage.domain.product.param.query.AreaSkuQueryParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName AreaSkuConverter
 * @Description
 * <AUTHOR>
 * @Date 17:56 2024/5/8
 * @Version 1.0
 **/
public class AreaSkuConverter {
    public static List<AreaSkuVO> entityToVO(List<AreaSkuEntity> areaSkuEntities) {
        if (CollectionUtils.isEmpty(areaSkuEntities)) {
            return Collections.emptyList();
        }
        List<AreaSkuVO> areaSkuVOS = new ArrayList<>(areaSkuEntities.size());
        for (AreaSkuEntity areaSkuEntity : areaSkuEntities) {
            areaSkuVOS.add(entityToVOV2(areaSkuEntity));
        }
        return areaSkuVOS;
    }

    public static List<AreaSkuPriceCommandParam> areaSkuPriceDTO2CommandParam(AreaSkuPriceDTO e) {
       return e.getAreaNoList ().stream().map (i->{
            AreaSkuPriceCommandParam param = new AreaSkuPriceCommandParam ();
            param.setSku (e.getSku ());
            param.setAreaNo (i);
            param.setPrice (e.getPrice ());
            return param;
        }).collect(Collectors.toList());
    }

    public static List<AreaSkuQueryParam> toAreaSkuQueryParams(List<AreaSkuQueryInput> input) {
        if (CollectionUtils.isEmpty(input)) {
            return Collections.emptyList();
        }
        List<AreaSkuQueryParam> params = new ArrayList<>(input.size());
        for (AreaSkuQueryInput areaSkuQueryInput : input) {
            AreaSkuQueryParam param = new AreaSkuQueryParam();
            param.setSku(areaSkuQueryInput.getSku());
            param.setAreaNos(areaSkuQueryInput.getAreaNos());
            params.add(param);
        }
        return params;
    }

    public static AreaSkuVO entityToVOV2(AreaSkuEntity areaSkuEntity) {
        AreaSkuVO areaSkuVO = new AreaSkuVO();
        areaSkuVO.setId(areaSkuEntity.getId());
        areaSkuVO.setSku(areaSkuEntity.getSku());
        areaSkuVO.setAreaNo(areaSkuEntity.getAreaNo());
        areaSkuVO.setQuantity(areaSkuEntity.getQuantity());
        areaSkuVO.setLockQuantity(areaSkuEntity.getLockQuantity());
        areaSkuVO.setSafeQuantity(areaSkuEntity.getSafeQuantity());
        areaSkuVO.setOriginalPrice(areaSkuEntity.getOriginalPrice());
        areaSkuVO.setPrice(areaSkuEntity.getPrice());
        areaSkuVO.setLadderPrice(areaSkuEntity.getLadderPrice());
        areaSkuVO.setShare(areaSkuEntity.getShare());
        areaSkuVO.setOnSale(areaSkuEntity.getOnSale());

        areaSkuVO.setPriority(areaSkuEntity.getPriority());
        areaSkuVO.setPdPriority(areaSkuEntity.getPdPriority());
        areaSkuVO.setUpdater(areaSkuEntity.getUpdater());
        areaSkuVO.setAddTime(areaSkuEntity.getAddTime());
        areaSkuVO.setUpdateTime(areaSkuEntity.getUpdateTime());
        areaSkuVO.setLevel(areaSkuEntity.getLevel());
        areaSkuVO.setLimitedQuantity(areaSkuEntity.getLimitedQuantity());
        areaSkuVO.setSalesMode(areaSkuEntity.getSalesMode());
        areaSkuVO.setAreaName(areaSkuEntity.getAreaName());

        areaSkuVO.setStoreQuantity(areaSkuEntity.getStoreQuantity());
        areaSkuVO.setShareQuantity(areaSkuEntity.getShareQuantity());
        areaSkuVO.setShow(areaSkuEntity.getShow());
        areaSkuVO.setInfo(areaSkuEntity.getInfo());
        areaSkuVO.setMType(areaSkuEntity.getMType());
        areaSkuVO.setShowAdvance(areaSkuEntity.getShowAdvance());
        areaSkuVO.setAdvance(areaSkuEntity.getAdvance());
        areaSkuVO.setOldPrice(areaSkuEntity.getOldPrice());
        areaSkuVO.setCornerStatus(areaSkuEntity.getCornerStatus());
        areaSkuVO.setOpenSale(areaSkuEntity.getOpenSale());
        areaSkuVO.setOpenSaleTime(areaSkuEntity.getOpenSaleTime());
        areaSkuVO.setCloseSale(areaSkuEntity.getCloseSale());

        areaSkuVO.setCloseSaleTime(areaSkuEntity.getCloseSaleTime());
        areaSkuVO.setFixFlag(areaSkuEntity.getFixFlag());
        areaSkuVO.setFixNum(areaSkuEntity.getFixNum());
        areaSkuVO.setInterestRateNew(areaSkuEntity.getInterestRateNew());
        areaSkuVO.setInterestRateOld(areaSkuEntity.getInterestRateOld());
        areaSkuVO.setAutoFlagNew(areaSkuEntity.getAutoFlagNew());
        areaSkuVO.setAutoFlagOld(areaSkuEntity.getAutoFlagOld());
        areaSkuVO.setParentNo(areaSkuEntity.getParentNo());
        areaSkuVO.setWarehouseNo(areaSkuEntity.getWarehouseNo());
        return areaSkuVO;
    }
}
