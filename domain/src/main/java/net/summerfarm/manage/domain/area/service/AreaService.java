package net.summerfarm.manage.domain.area.service;

import com.google.common.collect.Lists;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AreaService {
    @Resource
    AreaQueryRepository areaQueryRepository;

    public Map<Integer, Area> getAreaMap(List<Integer> areaNos) {
        if (CollectionUtils.isEmpty(areaNos)) {
            return new HashMap<>();
        }
        List<Integer> query = areaNos.stream().distinct().collect(Collectors.toList());
        List<Area> areas = areaQueryRepository.getNameByAreaNos(query);
        return areas.stream().collect(Collectors.toMap(Area::getAreaNo, Function.identity(), (x1, x2) -> x1));
    }

    public List<AreaSimpleEntity> batchQueryByAreaNos(List<Integer> areaNos) {
        if (CollectionUtils.isEmpty(areaNos)) {
            return Lists.newArrayList();
        }
        List<AreaSimpleEntity> entityList = areaQueryRepository.batchQueryByAreaNos(
                areaNos);
        return entityList;
    }

    public List<AreaSimpleEntity> batchQueryByLargeAreaNos(List<Integer> largeAreaNos) {
        if (CollectionUtils.isEmpty(largeAreaNos)) {
            return Lists.newArrayList();
        }
        List<AreaSimpleEntity> entityList = areaQueryRepository.batchQueryByLargeAreaNos(largeAreaNos);
        return entityList;
    }

    public Set<Integer> queryCloseAreaNos() {
        return areaQueryRepository.queryCloseAreaNos();
    }
}
