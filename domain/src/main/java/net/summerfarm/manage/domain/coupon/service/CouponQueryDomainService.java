package net.summerfarm.manage.domain.coupon.service;


import net.summerfarm.manage.domain.coupon.repository.CouponQueryRepository;
import net.summerfarm.manage.domain.coupon.repository.CouponCommandRepository;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 优惠券表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-12-19 13:49:12
 * @version 1.0
 *
 */
@Service
public class CouponQueryDomainService {


}
