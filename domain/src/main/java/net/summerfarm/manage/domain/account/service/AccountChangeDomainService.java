package net.summerfarm.manage.domain.account.service;



import net.summerfarm.manage.domain.account.repository.AccountChangeQueryRepository;
import net.summerfarm.manage.domain.account.repository.AccountChangeCommandRepository;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2023-10-26 16:20:19
 * @version 1.0
 *
 */
@Service
public class AccountChangeDomainService {


    @Autowired
    private AccountChangeCommandRepository accountChangeCommandRepository;
    @Autowired
    private AccountChangeQueryRepository accountChangeQueryRepository;



    public AccountChangeEntity insert(AccountChangeEntity entity) {
        return accountChangeCommandRepository.insertSelective(entity);
    }


    public Boolean update(AccountChangeEntity entity) {
        accountChangeCommandRepository.updateByIdSelective(entity);
        return true;
    }
}
