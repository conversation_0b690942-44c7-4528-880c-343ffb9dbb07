package net.summerfarm.manage.domain.coupon.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-05-31 15:37:46
 * @version 1.0
 *
 */
@Data
public class MerchantCouponEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 商户id
	 */
	private Long mId;

	/**
	 * 优惠券id
	 */
	private Integer couponId;

	/**
	 * 有效日期
	 */
	private LocalDateTime vaildDate;

	/**
	 * 红包发送者
	 */
	private String sender;

	/**
	 * 是否使用，0未使用，1已使用
	 */
	private Integer used;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 
	 */
	private Integer view;

	/**
	 * 
	 */
	private String orderNo;

	/**
	 * 领取类型 0-发放（人工）1-手动领取  2-抽奖活动 （被动）3-自动领取（新人注册/推荐好友下单）4-满返活动（被动） 5-其他
	 */
	private Integer receiveType;

	/**
	 * 开始生效时间
	 */
	private LocalDateTime startTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 优惠券发放记录id
	 */
	private Long sendId;

	

	
}