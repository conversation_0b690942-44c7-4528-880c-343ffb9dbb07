package net.summerfarm.manage.domain.merchantpool.entity;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-16 15:42:54
 * @version 1.0
 *
 */
@Data
public class MerchantPoolDetailEntity {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 人群包id
	 */
	private Long poolInfoId;

	/**
	 * 商户id
	 */
	private Long mId;

	/**
	 * 店铺类型
	 */
	private String size;

	/**
	 * 运营服务区域
	 */
	private Integer areaNo;

	/**
	 * 版本(只会存在两个版本)
	 */
	private Integer version;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}