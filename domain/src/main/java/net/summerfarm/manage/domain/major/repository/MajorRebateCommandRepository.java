package net.summerfarm.manage.domain.major.repository;



import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.param.command.MajorRebateCommandParam;




/**
*
* <AUTHOR>
* @date 2025-02-27 15:22:25
* @version 1.0
*
*/
public interface MajorRebateCommandRepository {

    MajorRebateEntity insertSelective(MajorRebateCommandParam param);

    int updateSelectiveById(MajorRebateCommandParam param);

    int remove(Long id);

}