package net.summerfarm.manage.domain.marketItem.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
@Data
public class MarketItemAiExtQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * SPUID, products.pd_id
	 */
	private Long pdId;

	/**
	 * AI扩展类型，1-商品相关问题
	 */
	private Integer extType;

	/**
	 * AI扩展值
	 */
	private String extValue;

	/**
	 * SKU编码列表，用于批量查询
	 */
	private List<String> skus;

	/**
	 * SKU编码
	 */
	private String sku;

}