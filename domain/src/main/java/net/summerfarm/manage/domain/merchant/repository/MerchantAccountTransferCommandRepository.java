package net.summerfarm.manage.domain.merchant.repository;



import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;




/**
*
* <AUTHOR>
* @date 2024-01-10 14:07:22
* @version 1.0
*
*/
public interface MerchantAccountTransferCommandRepository {

    MerchantAccountTransferEntity insertSelective(MerchantAccountTransferCommandParam param);

}