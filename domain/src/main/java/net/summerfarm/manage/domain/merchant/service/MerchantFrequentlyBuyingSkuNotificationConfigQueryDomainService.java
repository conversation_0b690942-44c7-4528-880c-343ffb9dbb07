package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 门店常购清单配置领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Service
public class MerchantFrequentlyBuyingSkuNotificationConfigQueryDomainService {


}
