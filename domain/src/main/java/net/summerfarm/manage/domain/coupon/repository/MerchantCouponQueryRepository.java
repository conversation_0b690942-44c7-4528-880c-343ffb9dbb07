package net.summerfarm.manage.domain.coupon.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import net.summerfarm.manage.domain.coupon.param.query.MerchantCouponQueryParam;



/**
*
* <AUTHOR>
* @date 2024-05-31 15:37:46
* @version 1.0
*
*/
public interface MerchantCouponQueryRepository {

    PageInfo<MerchantCouponEntity> getPage(MerchantCouponQueryParam param);

    MerchantCouponEntity selectById(Long id);

    List<MerchantCouponEntity> selectByCondition(MerchantCouponQueryParam param);

}