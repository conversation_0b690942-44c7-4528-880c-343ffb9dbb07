package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.InventoryRecordQueryRepository;
import net.summerfarm.manage.domain.product.repository.InventoryRecordCommandRepository;
import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: sku信息变化记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 15:10:53
 * @version 1.0
 *
 */
@Service
public class InventoryRecordQueryDomainService {


}
