package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.GoodsLocationDetailQueryRepository;
import net.summerfarm.manage.domain.product.repository.GoodsLocationDetailCommandRepository;
import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationDetailCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 货位详情
领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-07 14:12:49
 * @version 1.0
 *
 */
@Service
public class GoodsLocationDetailCommandDomainService {


    @Autowired
    private GoodsLocationDetailCommandRepository goodsLocationDetailCommandRepository;
    @Autowired
    private GoodsLocationDetailQueryRepository goodsLocationDetailQueryRepository;



    public GoodsLocationDetailEntity insert(GoodsLocationDetailCommandParam param) {
        return goodsLocationDetailCommandRepository.insertSelective(param);
    }


    public int update(GoodsLocationDetailCommandParam param) {
        return goodsLocationDetailCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return goodsLocationDetailCommandRepository.remove(id);
    }
}
