package net.summerfarm.manage.domain.order.flatObject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 订单配送计划<br/>
 * date: 2024/12/30 15:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OrderDeliveryPlanFlatObject {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * delivery_plan主键
     */
    private Integer dpId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 1大客户\2大连锁3\小连锁\4单点
     */
    private String msize;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人地址
     */
    private String contactAddress;

    /**
     * 配送时间区间
     */
    private String timeFrame;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 超时加单费用
     */
    private BigDecimal outTimesFee;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 订单生成时间
     */
    private LocalDateTime orderTime;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 鲜沐大客户ID
     */
    private Long bigCustomerId;

    /**
     * 订单项
     */
    private List<OrderDeliveryPlanItemFlatObject> orderDeliveryPlanItemFlatObjectList;
}
