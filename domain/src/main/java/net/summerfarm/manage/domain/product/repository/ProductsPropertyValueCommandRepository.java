package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
*
* <AUTHOR>
* @date 2024-05-06 16:02:28
* @version 1.0
*
*/
public interface ProductsPropertyValueCommandRepository {

    ProductsPropertyValueEntity insertSelective(ProductsPropertyValueCommandParam param);

    int updateSelectiveById(ProductsPropertyValueCommandParam param);

    void addSalePropertyValue(List<ProductsPropertyValueCommandParam> commandParams);

    void deleteByPdId(Long pdId);

    void deleteByPdIdAndPropertyIds(Long pdId, Set<Integer> productsPropertyIds);
}