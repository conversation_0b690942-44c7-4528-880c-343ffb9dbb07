package net.summerfarm.manage.domain.admin.service;


import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionCommandRepository;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 数据权限表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-06-18 23:13:51
 * @version 1.0
 *
 */
@Service
public class AdminDataPermissionQueryDomainService {


}
