package net.summerfarm.manage.domain.merchant.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Data
public class MerchantSkuOrderDataEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * 最近一次下单时间
	 */
	private LocalDateTime lastOrderTime;

	/**
	 * 最近一次下单数量
	 */
	private Integer lastOrderQuantity;

	/**
	 * 最近30天下单次数
	 */
	private Integer lastThirtyDaysOrderCount;

	/**
	 * 最近60天下单次数
	 */
	private Integer lastSixtyDaysOrderCount;

	/**
	 * 最近2年下单次数
	 */
	private Integer lastTwoYearsOrderCount;

	/**
	 * 数据同步日期，格式为YYYYMMDD
	 */
	private String dayTag;

	

	
}