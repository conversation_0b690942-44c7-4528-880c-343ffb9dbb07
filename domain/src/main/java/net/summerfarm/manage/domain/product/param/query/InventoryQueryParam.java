package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @ClassName InventoryQueryParam
 * @Description
 * <AUTHOR>
 * @Date 15:10 2024/4/30
 * @Version 1.0
 **/
@Data
public class InventoryQueryParam extends BasePageInput {

    /**
     * id
     */
    private Long invId;

    /**
     * 性质
     * 类型 0 自营 1 代仓
     */
    private Integer type;
    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * sku性质
     */
    private Integer outdated;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * sku
     */
    private String sku;

    /**
     * sku列表
     */
    private List<String> skuList;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * pdIds
     */
    private List<Long> pdIds;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 是否放入样品池
     */
    private Integer samplePool;

    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 叶子类目
     */
    private Long categoryId;

    /**
     * 二级类目id
     */
    private Long secondCategoryId;

    /**
     * 一级类目id
     */
    private Long firstCategoryId;

    /**
     * 属性id
     */
    private Long productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;
}
