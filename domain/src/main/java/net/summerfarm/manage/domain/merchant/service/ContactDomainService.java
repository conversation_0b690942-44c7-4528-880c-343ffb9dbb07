package net.summerfarm.manage.domain.merchant.service;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.ContactRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



/**
 *
 * @Title: 联系人业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-09-19 16:21:24
 * @version 1.0
 *
 */
@Service
@Slf4j
public class ContactDomainService {


    @Autowired
    private ContactRepository contactRepository;
    
    public Long addContact(MerchantEntity merchant){
        ContactEntity contact = new ContactEntity();
        contact.setContact(merchant.getMcontact());
        contact.setMId(merchant.getMId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(1);
        contact.setPoiNote(merchant.getPoiNote());
        contact = contactRepository.insertSelective(contact);
        return contact.getContactId();
    }


    /**
     * 添加免审地址
     * @param merchant
     * @param storeNo
     * @return
     */
    public ContactEntity addContactWithoutAudit(MerchantEntity merchant, Integer storeNo){
        ContactEntity contact = new ContactEntity();
        // 默认杭州总部
        String defaultPoi = "120.058591,30.279943";
        contact.setContact(merchant.getMcontact());
        contact.setMId(merchant.getMId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(0);
        contact.setPoiNote(StringUtils.isBlank(merchant.getPoiNote()) ? defaultPoi : merchant.getPoiNote());
        contact.setStoreNo(storeNo);
        contact.setRemark("人工工单操作");
        contact = contactRepository.insertSelective(contact);
        log.info("免审地址添加完成:contact：{}", JSON.toJSONString(contact));
        return contact;
    }


}
