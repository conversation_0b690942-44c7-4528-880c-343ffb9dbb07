package net.summerfarm.manage.domain.merchant.repository;

import net.summerfarm.manage.domain.merchant.entity.ContactEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-19 16:21:24
* @version 1.0
*
*/

public interface ContactRepository {

    ContactEntity selectByPrimaryKey(Long id);

    ContactEntity insertSelective(ContactEntity entity);

    ContactEntity selectDefaultContactByMid(Long mId);
}