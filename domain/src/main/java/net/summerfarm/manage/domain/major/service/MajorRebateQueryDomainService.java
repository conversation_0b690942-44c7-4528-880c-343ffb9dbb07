package net.summerfarm.manage.domain.major.service;


import net.summerfarm.manage.domain.major.repository.MajorRebateQueryRepository;
import net.summerfarm.manage.domain.major.repository.MajorRebateCommandRepository;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-02-27 15:22:25
 * @version 1.0
 *
 */
@Service
public class MajorRebateQueryDomainService {


}
