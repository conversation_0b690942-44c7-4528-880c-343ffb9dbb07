package net.summerfarm.manage.domain.merchant.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam;



/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository {

    PageInfo<MerchantFrequentlyBuyingSkuNotificationConfigEntity> getPage(MerchantFrequentlyBuyingSkuNotificationConfigQueryParam param);

    MerchantFrequentlyBuyingSkuNotificationConfigEntity selectById(Long id);

    List<MerchantFrequentlyBuyingSkuNotificationConfigEntity> selectByCondition(MerchantFrequentlyBuyingSkuNotificationConfigQueryParam param);

}