package net.summerfarm.manage.domain.afterSale.flatObject;

import lombok.Data;

import java.time.LocalDate;

/**
 * Description: 售后配送详情<br/>
 * date: 2024/12/31 15:57<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AfterSaleDeliveryPathFlatObject {

    /**
     * ID
     */
    private Long id;

    /**
     * 售后单号
     */
    private String afterSaleNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 1大客户\2大连锁3\小连锁\4单点
     */
    private String msize;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 类型 0 配送 1 回收 2 配送回收
     */
    private Integer deliveryType;

    /**
     * 类型  0 配送 1 回收
     */
    private Integer orderItemDeliveryType;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人地址
     */
    private String contactAddress;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 规格
     */
    private String weight;

    /**
     * sku
     */
    private String sku;

    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 鲜沐大客户ID
     */
    private Long bigCustomerId;
}
