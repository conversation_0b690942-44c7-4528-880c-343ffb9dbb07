package net.summerfarm.manage.domain.coupon.param.command;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-12-19 13:49:12
 * @version 1.0
 *
 */
@Data
public class CouponCommandParam {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 红包名称
	 */
	private String name;

	/**
	 * 优惠券代码
	 */
	private String code;

	/**
	 * 额度
	 */
	private BigDecimal money;

	/**
	 * 使用阈值
	 */
	private BigDecimal threshold;

	/**
	 * 优惠券类型，0指固定时间间隔到期，1固定时间点到期
	 */
	private Integer type;

	/**
	 * 有效日期
	 */
	private LocalDateTime vaildDate;

	/**
	 * 有效时间，单位：天
	 */
	private Integer vaildTime;

	/**
	 * 卡券分组 0-平台活动券 1-售后补偿券 2-区域拉新券 3-会员权益券 4-销售客情券 5-销售月活券 6-行业活动券 8-员工福利券 9-销售囤货券 10-区域活动券 11-销售品类券 20-市场活动券 13-销售现货券 19-区域召回券 21-其他 16-功能测试券 17-平台补偿券 18-配送补偿券
	 */
	private Integer grouping;

	/**
	 * 是否仅新手，0不，1是
	 */
	private Integer newHand;

	/**
	 * 可用的品类
	 */
	private String categoryId;

	/**
	 * 可用sku
	 */
	private String sku;

	/**
	 * 备注
	 */
	private String reamrk;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 1 存在 2默认删除
	 */
	private Integer status;

	/**
	 * 1、普通商品优惠券 2、普通运费优惠券 3、精准送优惠券 
 4、红包 5、商品兑换券
	 */
	private Integer agioType;

	/**
	 * 开始生效时间
	 */
	private LocalDateTime startDate;

	/**
	 * 开始生效间隔日期
	 */
	private Integer startTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否限制发放一个 0(否）1（是）
	 */
	private Integer limitFlag;

	/**
	 * 1:预售尾款,2:除省心送,秒杀,预售外的活动可用,3:全部,4:仅省心送
	 */
	private Integer activityScope;

	/**
	 * 任务标识:0(否),1(是)
	 */
	private Integer taskTag;

	/**
	 * 任务作废标识:0(否),1(是)
	 */
	private Integer deleteTag;

	/**
	 * 是否系统创建
	 */
	private Integer autoCreated;

	/**
	 * 领取次数  等于0不限  大于0就是实际限制领取次数
	 */
	private Integer quantityClaimed;

	/**
	 * 剩余总量
	 */
	private Integer grantAmount;

	/**
	 * 领取限制 0-不限  大于0实际限制多少张
	 */
	private Integer grantLimit;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 卡劵范围描述
	 */
	private String skuScopeDesc;

	/**
	 * 卡劵范围
	 */
	private Integer skuScope;

	

	
}