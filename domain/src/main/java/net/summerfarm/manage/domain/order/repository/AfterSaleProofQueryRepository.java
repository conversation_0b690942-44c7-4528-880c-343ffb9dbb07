package net.summerfarm.manage.domain.order.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.query.AfterSaleProofQueryParam;



/**
*
* <AUTHOR>
* @date 2024-01-18 16:27:13
* @version 1.0
*
*/
public interface AfterSaleProofQueryRepository {

    PageInfo<AfterSaleProofEntity> getPage(AfterSaleProofQueryParam param);

    AfterSaleProofEntity selectById(Long id);

    List<AfterSaleProofEntity> selectByCondition(AfterSaleProofQueryParam param);

}