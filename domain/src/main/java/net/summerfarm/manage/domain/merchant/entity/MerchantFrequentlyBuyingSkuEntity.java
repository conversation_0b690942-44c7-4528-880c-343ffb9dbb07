package net.summerfarm.manage.domain.merchant.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Data
public class MerchantFrequentlyBuyingSkuEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 状态 -1：逻辑删除 0：待加入清单  1：已加入清单
	 */
	private Integer status;

	/**
	 * 置顶序号 0：未置顶 1：已置顶
	 */
	private Integer top;

	/**
	 * 数据来源  0：系统初始化 1：推荐弹窗 2：商品列表  3：商品详情  4：购物车 5：支付成功页
	 */
	private Integer source;

	/**
	 * 最近一次删除时间
	 */
	private LocalDateTime recentDeleteTime;

	

	
}