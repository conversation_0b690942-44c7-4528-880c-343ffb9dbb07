package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.AfterSaleOrderQueryRepository;
import net.summerfarm.manage.domain.order.repository.AfterSaleOrderCommandRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleOrderCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 售后订单表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-18 16:06:21
 * @version 1.0
 *
 */
@Service
public class AfterSaleOrderCommandDomainService {


    @Autowired
    private AfterSaleOrderCommandRepository afterSaleOrderCommandRepository;
    @Autowired
    private AfterSaleOrderQueryRepository afterSaleOrderQueryRepository;



    public AfterSaleOrderEntity insert(AfterSaleOrderCommandParam param) {
        return afterSaleOrderCommandRepository.insertSelective(param);
    }


    public int update(AfterSaleOrderCommandParam param) {
        return afterSaleOrderCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return afterSaleOrderCommandRepository.remove(id);
    }
}
