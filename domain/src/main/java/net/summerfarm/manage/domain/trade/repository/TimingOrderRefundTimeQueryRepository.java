package net.summerfarm.manage.domain.trade.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.query.TimingOrderRefundTimeQueryParam;



/**
*
* <AUTHOR>
* @date 2024-01-22 14:45:54
* @version 1.0
*
*/
public interface TimingOrderRefundTimeQueryRepository {

    PageInfo<TimingOrderRefundTimeEntity> getPage(TimingOrderRefundTimeQueryParam param);

    TimingOrderRefundTimeEntity selectById(Long id);

    List<TimingOrderRefundTimeEntity> selectByCondition(TimingOrderRefundTimeQueryParam param);

}