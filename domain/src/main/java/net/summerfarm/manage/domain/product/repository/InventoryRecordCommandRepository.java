package net.summerfarm.manage.domain.product.repository;


import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryRecordCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-05-06 15:10:53
* @version 1.0
*
*/
public interface InventoryRecordCommandRepository {

    InventoryRecordEntity insertSelective(InventoryRecordCommandParam param);

    int updateSelectiveById(InventoryRecordCommandParam param);

    int remove(Long id);

    void saveBatch(List<InventoryRecordCommandParam> params);
}