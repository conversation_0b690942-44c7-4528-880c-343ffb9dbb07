package net.summerfarm.manage.domain.product.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AreaSkuQueryParam;

import java.util.List;
import java.util.Set;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public interface AreaSkuQueryRepository {

    List<AreaSkuEntity> queryListSkuPrice(List<String> skus, List<Integer> areaNos,Boolean onsale);

    List<AreaSkuEntity> selectVOList(String sku, Integer areaNo);
    AreaSkuEntity selectValidAndOnSale(Integer areaNo, String sku);

    List<AreaSkuEntity> queryAreaSkuBySkuAndAreaNoList(List<AreaSkuQueryParam> param);

    /**
     * 分页查询指定区域的上架商品ID
     *
     * @param areaNos 区域编号集合
     * @param pageIndex 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageInfo<Integer> pageOnsaleIdsByAreaNo(Set<Integer> areaNos, Integer pageIndex, Integer pageSize);
}
