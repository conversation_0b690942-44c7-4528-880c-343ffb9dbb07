package net.summerfarm.manage.domain.activity.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Data
public class ActivityScopeConfigCommandParam {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 基础信息id
	 */
	private Long basicInfoId;

	/**
	 * 范围id
	 */
	private Long scopeId;

	/**
	 * 活动范围类型，1 人群包，2 运营城市，3 运营大区
	 */
	private Integer scopeType;

	/**
	 * 最后一次修改人id
	 */
	private Integer updaterId;

	/**
	 * 是否已被删除，0 否，1 是
	 */
	private Integer delFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}