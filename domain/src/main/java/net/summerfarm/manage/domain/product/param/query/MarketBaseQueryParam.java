package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * 商品相关查询类
 */
@Data
public class MarketBaseQueryParam extends BasePageInput implements Serializable {
    /**
     * spu name
     */
    private String spuTitleLike;
    /**
     * sku
     */
    private String itemCodeLike;
    /**
     *  类型 0 自营 1 代仓
     */
    private Integer characters;
    /**
     * 大客户id
     */
    private Long adminId;

    /**
     * sku
     */
    private String itemCode;

    /**
     * pdId
     */
    private Long outId;
}
