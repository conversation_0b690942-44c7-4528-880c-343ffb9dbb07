package net.summerfarm.manage.domain.merchant.temp;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.facade.merchant.MerchantQueryFacade;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @descripton 门店临时仓库，整合买家中心以及门店域暂未迁移的数据
 * @date 2024/1/10 15:47
 */
@Component
public class MerchantTempRepository {


    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Resource
    private MerchantQueryRepository merchantQueryRepository;


    /**
     * 根据mid查询门店（不带权限）
     * @param mId
     * @return
     */
    public MerchantEntity getMerchantByMid(Long mId) {
        MerchantStoreAndExtendResp merchantExtends = merchantQueryFacade.getMerchantExtendsByMid(mId);
        if (null == merchantExtends) {
            return null;
        }

        MerchantEntity dto = toMerchant(merchantExtends);
        // 补充其他域的信息
        MerchantEntity merchant = merchantQueryRepository.selectById(mId);
        this.warpMerchantEntity(merchant, dto);

        return dto;
    }


    private MerchantEntity toMerchant(MerchantStoreAndExtendResp merchantStoreAndExtendResp) {
        MerchantEntity dto = new MerchantEntity();
        dto.setTenantId(merchantStoreAndExtendResp.getTenantId());
        dto.setAdminId(merchantStoreAndExtendResp.getAdminId());
        dto.setMname(merchantStoreAndExtendResp.getStoreName());
        dto.setMcontact(merchantStoreAndExtendResp.getAccountName());
        dto.setPhone(merchantStoreAndExtendResp.getPhone());
        dto.setSize(MerchantEntity.transSizeFromUserCenter(merchantStoreAndExtendResp.getSize()));
        dto.setIslock(MerchantEntity.transStatusFromUserCenter(merchantStoreAndExtendResp.getStatus()));
        dto.setEnterpriseScale(MerchantEntity.transTypeFromUserCenter(merchantStoreAndExtendResp.getType()));
        dto.setRegisterTime(merchantStoreAndExtendResp.getRegisterTime());
        dto.setRemark(merchantStoreAndExtendResp.getAuditRemark());
        dto.setAuditTime(merchantStoreAndExtendResp.getAuditTime());
        dto.setMId(merchantStoreAndExtendResp.getMId());
        dto.setType(merchantStoreAndExtendResp.getBusinessType());
        dto.setChannelCode(merchantStoreAndExtendResp.getChannelCode());
        dto.setPopView(merchantStoreAndExtendResp.getPopView());
        dto.setChangePop(merchantStoreAndExtendResp.getChangePop());
        dto.setFirstLoginPop(merchantStoreAndExtendResp.getFirstLoginPop());
        dto.setDisplayButton(merchantStoreAndExtendResp.getDisplayButton());
        dto.setPreRegisterFlag(merchantStoreAndExtendResp.getPreRegisterFlag());
        dto.setAreaNo(merchantStoreAndExtendResp.getAreaNo());
        dto.setProvince(merchantStoreAndExtendResp.getProvince());
        dto.setCity(merchantStoreAndExtendResp.getCity());
        dto.setArea(merchantStoreAndExtendResp.getArea());
        dto.setPoiNote(merchantStoreAndExtendResp.getPoiNote());
        return dto;
    }

    private void warpMerchantEntity(MerchantEntity merchant, MerchantEntity dto) {
        if(merchant == null || dto == null) {
            return;
        }
        dto.setSkuShow(merchant.getSkuShow());
        dto.setRechargeAmount(merchant.getRechargeAmount());
        dto.setInviterChannelCode(merchant.getInviterChannelCode());
        dto.setCashAmount(merchant.getCashAmount());
        dto.setDoorPic(merchant.getDoorPic());
        dto.setMemberIntegral(merchant.getMemberIntegral());
        dto.setDirect(merchant.getDirect());
        dto.setGrade(merchant.getGrade());
        dto.setHouseNumber(merchant.getHouseNumber());
        dto.setLastOrderTime(merchant.getLastOrderTime());
        dto.setOpenid(merchant.getOpenid());
        dto.setUnionid(merchant.getUnionid());
        dto.setMpOpenid(merchant.getMpOpenid());
        dto.setRankId(merchant.getRankId());
        dto.setLoginTime(merchant.getLoginTime());
        dto.setAuditUser(merchant.getAuditUser());
        dto.setAddress(merchant.getAddress());
        dto.setServer(merchant.getServer());
        dto.setCluePool(merchant.getCluePool());
        dto.setCompanyBrand(merchant.getCompanyBrand());
        dto.setEnterpriseScale(merchant.getEnterpriseScale());
        dto.setMerchantType(merchant.getMerchantType());
    }
}
