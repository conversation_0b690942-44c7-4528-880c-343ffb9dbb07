package net.summerfarm.manage.domain.merchant.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Data
public class MerchantFrequentlyBuyingSkuNotificationConfigEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 特价提醒 0：不提醒  1：提醒（默认）
	 */
	private Integer specialOffer;

	/**
	 * 到货提醒 0：不提醒  1：提醒（默认）
	 */
	private Integer goodsArrived;

	

	
}