package net.summerfarm.manage.domain.crm.service;

import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 *
 * @Title: 商户对应负责人业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-09-20 15:02:32
 * @version 1.0
 *
 */
@Service
public class FollowUpRelationDomainService {

    @Autowired
    private FollowUpRelationRepository followUpRelationRepository;

    public void insertForAddMerchant(Long mid) {
        FollowUpRelationEntity followUpRelation = new FollowUpRelationEntity();
        followUpRelation.setMId(mid);
        followUpRelation.setAdminId(1);
        followUpRelation.setAdminName("杨晗");
        followUpRelation.setAddTime(LocalDateTime.now());
        followUpRelation.setReassign(true);
        followUpRelation.setReason("新注册用户");
        updateAndInsertFollow(followUpRelation);
    }

    public void updateAndInsertFollow(FollowUpRelationEntity followInput){
        if(Objects.isNull(followInput) || Objects.isNull(followInput.getMId())){
            throw new BizException("商户id不能为空");
        }
        // 每次变更均改变其最新跟进时间
        followInput.setReassignTime(LocalDateTime.now());
        followInput.setAddTime(LocalDateTime.now());
        FollowUpRelationEntity oldFollow = followUpRelationRepository.selectOne(FollowUpRelationEntity.builder().mId(followInput.getMId()).build());
        // 无商户跟进记录,新增一条
        if(Objects.isNull(oldFollow)){
            followUpRelationRepository.insertSelective(followInput);
        }else {
            // 有跟进记录,更新商户跟进记录
            followInput.setId(oldFollow.getId());
            followUpRelationRepository.updateById(followInput);
        }
    }


    public Map<Long,List<FollowUpRelationEntity>> selectByMids(List<Long> mIds){
        if (CollectionUtils.isEmpty(mIds)){
            return new HashMap<>();
        }
        FollowUpRelationEntity followUpRelationEntity = new FollowUpRelationEntity();
        followUpRelationEntity.setMIds(mIds);
        List<FollowUpRelationEntity> followUpRelationEntities = followUpRelationRepository.selectByCondition(followUpRelationEntity);
        return followUpRelationEntities.stream().collect(Collectors.groupingBy(FollowUpRelationEntity::getMId));
    }

}
