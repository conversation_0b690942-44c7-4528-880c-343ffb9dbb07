package net.summerfarm.manage.domain.order.repository;



import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleProofCommandParam;




/**
*
* <AUTHOR>
* @date 2024-01-18 16:27:13
* @version 1.0
*
*/
public interface AfterSaleProofCommandRepository {

    AfterSaleProofEntity insertSelective(AfterSaleProofCommandParam param);

    int updateSelectiveById(AfterSaleProofCommandParam param);

    int remove(Long id);

}