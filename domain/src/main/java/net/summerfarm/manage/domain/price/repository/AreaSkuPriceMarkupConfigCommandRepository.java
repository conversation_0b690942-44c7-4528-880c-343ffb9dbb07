package net.summerfarm.manage.domain.price.repository;



import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;

import java.math.BigDecimal;
import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-03-26 13:59:07
* @version 1.0
*
*/
public interface AreaSkuPriceMarkupConfigCommandRepository {

    AreaSkuPriceMarkupConfigEntity insertSelective(AreaSkuPriceMarkupConfigCommandParam param);

    int updateSelectiveById(AreaSkuPriceMarkupConfigCommandParam param);

    int updateMarkupValueByIds(BigDecimal markupValue, List<Long> ids);

    int batchInsert(List<AreaSkuPriceMarkupConfigCommandParam> paramList);


    int remove(Long id);

}