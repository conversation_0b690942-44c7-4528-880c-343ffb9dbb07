package net.summerfarm.manage.domain.product.entity;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.enums.ProductCategoryTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
@Data
public class ProductEntity implements Serializable {

    /**
     * sku
     */
    private String sku;

    /**
     * pdNo
     */
    private String pdNo;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku图片
     */
    private String skuPic;

    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目类型
     */
    private Integer categoryType;

    /**
     * 规格
     */
    private String weight;

    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;
    /**
     * 买手名称
     */
    private String buyerName;
    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;
    /**
     * 关键属性，和商城一致
     */
    private List<ProductsPropertyValueEntity> keyValueList;

    /**
     * 同商城搜索页商品名称展示一致
     */
    private String mallSkuName;

    /**
     * 同商城搜索页商品图片展示一致
     */
    private String mallSkuPic;

    /**
     * 0 平台 1 大客户 2 帆台代仓
     */
    private Integer createType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 发源地
     */
    private String origin;


    /**
     * sku集合
     */
    private List<InventoryEntity> inventoryList;

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     *
     * @return
     */
    public ProductEntity resetSkuName() {
        if (StringUtils.isBlank(getSkuName())) {
            mallSkuName = productName + " " + getWeight();
        } else {
            mallSkuName = getSkuName();
        }
        return this;
    }

    /**
     * 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     *
     * @return
     */
    public ProductEntity resetSkuPic() {
        if (StringUtils.isBlank(getSkuPic())) {
            mallSkuPic = picturePath;
        } else {
            mallSkuPic = getSkuPic();
        }
        return this;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息 && 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     *
     * @return
     */
    public ProductEntity resetSkuNameAndSkuPic() {
        resetSkuName();
        resetSkuPic();
        return this;
    }

    public ProductEntity sortKeyValueList() {
        if (CollectionUtil.isNotEmpty(keyValueList)) {
            //属性排序处理
            if (Objects.equals(categoryType, ProductCategoryTypeEnum.FRUIT.getCode())) {
                keyValueList.sort(
                        Comparator.comparing(ProductsPropertyValueEntity::getName, Comparator.comparing(Global.FRUIT_PROPERTY_SORT::indexOf)));
            } else {
                keyValueList.sort(
                        Comparator.comparing(ProductsPropertyValueEntity::getName, Comparator.comparing(Global.NON_FRUIT_PROPERTY_SORT::indexOf)));
            }
        }
        return this;
    }

}
