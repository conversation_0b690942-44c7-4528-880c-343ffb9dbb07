package net.summerfarm.manage.domain.order.repository;



import net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity;
import net.summerfarm.manage.domain.order.param.command.OrderItemPreferentialCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-31 15:06:17
* @version 1.0
*
*/
public interface OrderItemPreferentialCommandRepository {

    OrderItemPreferentialEntity insertSelective(OrderItemPreferentialCommandParam param);

    int updateSelectiveById(OrderItemPreferentialCommandParam param);

    int remove(Long id);

}