package net.summerfarm.manage.domain.afterSale.repository;



import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.param.command.AfterSaleDeliveryPathCommandParam;




/**
*
* <AUTHOR>
* @date 2024-12-31 14:32:57
* @version 1.0
*
*/
public interface AfterSaleDeliveryPathCommandRepository {

    AfterSaleDeliveryPathEntity insertSelective(AfterSaleDeliveryPathCommandParam param);

    int updateSelectiveById(AfterSaleDeliveryPathCommandParam param);

    int remove(Long id);

}