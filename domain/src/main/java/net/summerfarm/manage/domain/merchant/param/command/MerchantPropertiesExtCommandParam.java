package net.summerfarm.manage.domain.merchant.param.command;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName MerchantCommandParam
 * @Description TODO
 * <AUTHOR>
 * @Date 10:25 2024/1/23
 * @Version 1.0
 **/
@Data
public class MerchantPropertiesExtCommandParam {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * key
     */
    private String key;

    /**
     * value
     */
    private String value;
}
