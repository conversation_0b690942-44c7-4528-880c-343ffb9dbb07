package net.summerfarm.manage.domain.order.service;


import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.manage.domain.order.entity.OrderRelationEntity;
import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrderRelationQueryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 *
 * @Title: 主子单关联关系表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-23 16:02:29
 * @version 1.0
 *
 */
@Service
public class OrderRelationQueryDomainService {

    @Resource
    private OrderRelationQueryRepository orderRelationQueryRepository;


    public String queryMasterOrderNoByOrderNo(String orderNo) {

        List<OrderRelationEntity> orderRelationEntities = orderRelationQueryRepository.selectByOrderNoBatch (Collections.singletonList (orderNo));
        if(CollectionUtil.isEmpty (orderRelationEntities)){
            return null;
        }
        return orderRelationEntities.get (0).getMasterOrderNo ();
    }
}
