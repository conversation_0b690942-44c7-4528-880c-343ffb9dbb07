package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.GoodsLocationEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:36:00
* @version 1.0
*
*/
public interface GoodsLocationCommandRepository {

    GoodsLocationEntity insertSelective(GoodsLocationCommandParam param);

    int updateSelectiveById(GoodsLocationCommandParam param);

    int remove(Long id);

}