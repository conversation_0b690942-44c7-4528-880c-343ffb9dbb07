package net.summerfarm.manage.domain.price.param.query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AreaSkuPriceMarkupConfigQueryParam extends BasePageInput {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 主键、自增
	 */
	private List<Long> ids;

	/**
	 * 商品编号
	 */
	private String sku;

	/**
	 * 城市编号
	 */
	private Integer areaNo;


	/**
	 * 商品编号
	 */
	private String pdName;

	/**
	 * 前台类目id
	 */
	private List<Long> frontCategoryIds;


	/**
	 * 类目id
	 */
	private List<Long> categoryIds;


	/**
	 * 城市编号
	 */
	private List<Integer> areaNos;

	
}