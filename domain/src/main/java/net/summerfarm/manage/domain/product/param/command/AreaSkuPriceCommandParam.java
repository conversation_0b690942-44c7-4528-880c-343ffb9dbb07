package net.summerfarm.manage.domain.product.param.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AreaSkuPriceCommandParam {
    /**
     * sku编号
     */
    @NotNull
    private String sku;

    /**
     * 城市编号
     */
    @NotNull
    private Integer areaNo;

    /**
     * 价格
     */
    @NotNull
    private BigDecimal price;
}
