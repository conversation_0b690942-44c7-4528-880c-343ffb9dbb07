package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.AppPopTopMatchedCompetitorSkuListQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: app_pop_top_matched_competitor_sku_list table领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
@Service
public class AppPopTopMatchedCompetitorSkuListQueryDomainService {


}
