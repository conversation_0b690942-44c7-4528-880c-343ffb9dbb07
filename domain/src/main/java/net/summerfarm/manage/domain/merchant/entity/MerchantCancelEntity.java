package net.summerfarm.manage.domain.merchant.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023-12-27 14:01:49
 * @version 1.0
 *
 */
@Data
public class MerchantCancelEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 门店ID
	 */
	private Long mId;

	/**
	 * 注销状态 默认-1（待注销） MerchantCancelEnum
	 */
	private Integer status;

	/**
	 * 申请原因
	 */
	private String remake;

	/**
	 * 申请凭证-后台申请必填
	 */
	private String certificate;

	/**
	 * 申请人
	 */
	private Long creator;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 更新人
	 */
	private Long updater;

	/**
	 * 申请来源  0-商城  1-后台
	 */
	private Integer resource;

	/**
	 * 注册手机号
	 */
	private String phone;

	/**
	 * 门店名称
	 */
	private String mname;

	/**
	 * 运营区域
	 */
	private Integer areaNo;

	

	
}