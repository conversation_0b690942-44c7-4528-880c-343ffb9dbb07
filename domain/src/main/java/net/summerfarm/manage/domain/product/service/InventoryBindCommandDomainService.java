package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.InventoryBindQueryRepository;
import net.summerfarm.manage.domain.product.repository.InventoryBindCommandRepository;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryBindCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: sku绑定关系表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 13:48:40
 * @version 1.0
 *
 */
@Service
public class InventoryBindCommandDomainService {


    @Autowired
    private InventoryBindCommandRepository inventoryBindCommandRepository;
    @Autowired
    private InventoryBindQueryRepository inventoryBindQueryRepository;



    public InventoryBindEntity insert(InventoryBindCommandParam param) {
        return inventoryBindCommandRepository.insertSelective(param);
    }


    public int update(InventoryBindCommandParam param) {
        return inventoryBindCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return inventoryBindCommandRepository.remove(id);
    }
}
