package net.summerfarm.manage.domain.product.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-11-15 14:13:27
* @version 1.0
*
*/
public interface ExternalProductMappingQueryRepository {

    PageInfo<ExternalProductMappingEntity> getPage(ExternalProductMappingQueryParam param);

    ExternalProductMappingEntity selectById(Long id);

    List<ExternalProductMappingEntity> selectByCondition(ExternalProductMappingQueryParam param);

    /**
     * 查询待绑定商品列表
     *
     * <AUTHOR>
     * @date 2024/11/19 15:05
     */
    PageInfo<ExternalProductMappingEntity> getPageUnmapped(ExternalProductMappingQueryParam queryParam);

    /**
     * 查询待绑定商品类目列表
     *
     * <AUTHOR>
     * @date 2024/11/20 15:42
     */
    List<CategoryEntity> getListUnmappedCategory(ExternalProductMappingQueryParam queryParam);

    /**
     * 分页查询已绑定列表
     *
     * <AUTHOR>
     * @date 2024/12/4 15:37
     */
    PageInfo<ExternalProductMappingEntity> getPageMapped(ExternalProductMappingQueryParam queryParam);

    /**
     * 分页查询已绑定类目列表
     *
     * <AUTHOR>
     * @date 2024/12/4 15:37
     */
    List<CategoryEntity> getListMappedCategory(ExternalProductMappingQueryParam queryParam);

    /**
     * 根据类型查询外部值列表
     *
     * <AUTHOR>
     * @date 2024/12/3 18:54
     */
    List<String> getListExternalValueList(Integer type);
}