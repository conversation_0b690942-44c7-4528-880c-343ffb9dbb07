package net.summerfarm.manage.domain.afterSale.entity;

import java.time.LocalDateTime;
import java.time.LocalDate;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-12-31 14:32:57
 * @version 1.0
 *
 */
@Data
public class AfterSaleDeliveryPathEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 创建时间
	 */
	private LocalDateTime gmtCreate;

	/**
	 * 修改时间
	 */
	private LocalDateTime gmtModified;

	/**
	 * 用户id
	 */
	private Integer mId;

	/**
	 * 回收，配送时间
	 */
	private LocalDate deliveryTime;

	/**
	 * 配送地址id
	 */
	private Long concatId;

	/**
	 * 售后订单号
	 */
	private String afterSaleNo;

	/**
	 * 类型 0 配送 1 回收 2 配送回收
	 */
	private Integer type;

	/**
	 * 出库仓
	 */
	private Integer outStoreNo;

	/**
	 * 状态 0 失效 1 待完成 2 完成中 3 已完成
	 */
	private Integer status;

	

	
}