package net.summerfarm.manage.domain.timing.repository;

import net.summerfarm.manage.domain.timing.entity.TimingRuleEntity;

import java.util.List;
import java.util.Set;

/**
 * 定期送规则仓储接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface TimingRuleRepository {

    /**
     * 保存定期送规则
     *
     * @return 保存后的实体
     */
    void saveBatch(List<TimingRuleEntity> collect);

    /**
     * 根据SKU查询定期送规则列表
     *
     * @param skus 团购商品SKU
     * @return 定期送规则列表
     */
    List<TimingRuleEntity> findByAreaAndSkus(Integer areaNo, Set<String> skus);


}
