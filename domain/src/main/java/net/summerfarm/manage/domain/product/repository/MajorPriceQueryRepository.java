package net.summerfarm.manage.domain.product.repository;

import java.util.List;
import java.util.Set;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.major.dto.QuotationExcelDto;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.entity.MajorPriceLowRemainder;
import net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam;
import net.summerfarm.manage.domain.product.param.query.MajorPriceQueryParam;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public interface MajorPriceQueryRepository {

    List<MajorPriceEntity> queryListMajorPrice(Integer adminId, String sku, List<Integer> areaNos);
    List<MajorPriceEntity> queryListMajorPriceWithoutTime(Integer direct,Long adminId, Set<String> skus, Set<Integer> areaNos);

    List<MajorPriceEntity> queryListMajorPriceByIds(List<Long> ids);

    List<MajorPriceLowRemainder> selectLowPriceRemainderSku(Integer adminId, Integer areaNo, String sku);

    MajorPriceEntity selectMajorPrice(Long adminId, Integer direct, Integer areaNo, String sku);

    void majorPriceMallShowBatchUpdate(Integer mallShow, Integer direct, Integer adminId);

    MajorPriceEntity selectLastCommitMajorPrice(Long adminId);

    PageInfo<MajorPriceFlatObject> selectMajorPricePage(MajorPricePageQueryParam majorPricePageQueryParam);

    PageInfo<MajorPriceItemFlatObject> selectMajorPriceCityPage(MajorPricePageQueryParam majorPricePageQueryParam);

    List<QuotationExcelDto> selectMajorPriceDownloadList(MajorPricePageQueryParam majorPricePageQueryParam);
}
