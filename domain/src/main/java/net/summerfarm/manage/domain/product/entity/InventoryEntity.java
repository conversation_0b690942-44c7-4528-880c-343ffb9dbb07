package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Data
public class InventoryEntity implements Serializable {

    /**
     * id
     */
    private Long invId;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 属性ID
     */
    private Integer aitId;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * 销售模式
     */
    private Integer salesMode;

    /**
     * 产地
     */
    private String origin;

    /**
     * 包装
     */
    private String unit;

    /**
     * 包数
     */
    private String pack;

    /**
     * 租户ID：1-鲜沐
     */
    private Long tenantId;

    /**
     * 上新类型：0、平台 1、大客户 2、帆台代仓
     */
    private Integer createType;

    /**
     * 规格
     */
    private String weight;

    /**
     * 规格备注
     */
    private String weightNotes;

    /**
     * 是否为国产，0：不是，1是
     */
    private Integer isDomestic;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 有效期
     */
    private String expiryDate;

    /**
     * 是否展示
     */
    private Boolean show;

    /**
     * 生熟度
     */
    private String maturity;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 贮存区域
     */
    private String storageMethod;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 商品介绍
     */
    private String introduction;

    /**
     * 标记位-过时的sku
     */
    private Integer outdated;

    /**
     * 售后最大数量
     */
    private Integer afterSaleQuantity;

    /**
     * 最小起售量
     */
    private Integer baseSaleQuantity;

    /**
     * 售卖规格
     */
    private Integer baseSaleUnit;

    /**
     * 类型 0 自营 1 代仓
     */
    private Integer type;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 所属/大客户ID
     */
    private Integer adminId;

    /**
     * 是否放入样品池
     */
    private Integer samplePool;

    /**
     * sku头图
     */
    private String skuPic;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 供应商是否可见：0不可见，1可见
     */
    private Integer supplierVisible;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;


    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 任务类型：0、SPU 1、SKU
     */
    private Integer taskType;

    /**
     * 工商名称
     */
    private String realName;

    /**
     * 审核人adminId
     */
    private Integer auditor;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * spu名称
     */
    private String pdName;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 大客户名称备注
     */
    private String nameRemakes;

    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 净重
     */
    private String netWeightUnit;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值
     */
    private Integer minAutoAfterSaleThreshold;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 主图
     */
    private String picturePath;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 二级类目名称
     */
    private String secondCategoryName;

}
