package net.summerfarm.manage.domain.product.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Data
public class ExternalProductMappingEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 映射类型 1-sku 2-类目
	 */
	private Integer type;

	/**
	 * 鲜沐内部值
	 */
	private String internalValue;

	/**
	 * 外部平台值
	 */
	private String externalValue;

	/**
	 * 鲜沐sku编码
	 */
	private String xmSkuCode;

	/**
	 * 外部sku
	 */
	private String externalSkuCode;

	/**
	 * 名称
	 */
	private String title;

	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 毛重
	 */
	private BigDecimal weightNum;

	/**
	 * 净重
	 */
	private BigDecimal netWeightNum;

	/**
	 * 主图
	 */
	private String mainPicture;

	/**
	 * 三级类目id
	 */
	private Long categoryId;

	/**
	 * 三级类目名称
	 */
	private String categoryName;

	/**
	 * 区域最高价
	 */
	private BigDecimal maxPrice;

	/**
	 * 区域最低价
	 */
	private BigDecimal minPrice;

	/**
	 * 映射的竞品top数据
	 */
	private AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity;

	/**
	 * 映射的竞品详细数据
	 */
	private AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity;

	/**
	 * 鲜沐成本
	 */
	private BigDecimal xmSkuCost;

	/**
	 * 鲜沐成本
	 */
	private BigDecimal xmSkuWeightCost;

	/**
	 * 外部品成本
	 */
	private BigDecimal externalSkuCost;

	/**
	 * 外部单斤成本
	 */
	private BigDecimal externalSkuGrossWeightCost;
	
}