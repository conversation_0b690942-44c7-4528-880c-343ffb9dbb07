package net.summerfarm.manage.domain.major.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/24 13:45
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuotationExcelHeadDto {

    /**
     * 主标题
     */
    String headerTitle;

    /**
     * 报价人
     */
    String quoter;

    /**
     * 导出报价时间
     */
    String quoteDate;
    /**
     * 有效期
     */
    private String validPeriod;

}
