package net.summerfarm.manage.domain.merchant.entity;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-09-19 10:55:24
 * @version 1.0
 *
 */
@Data
public class MerchantEntity {
	/**
	 * 
	 */
	private Long mId;

	/**
	 * 买家中心门店id
	 */
	private Long storeId;
	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 商户类型
	 */
	private Integer roleId;

	/**
	 * 商户名称
	 */
	private String mname;

	/**
	 * 主联系人
	 */
	private String mcontact;

	/**
	 * 微信用户id
	 */
	private String openid;

	/**
	 * 手机
	 */
	private String phone;

	/**
	 * 审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑 4、注销
	 */
	private Integer status;


	/**
	 *
	 */
	private Integer islock;

	/**
	 * 等级
	 */
	private Integer rankId;

	/**
	 * 注册时间
	 */
	private LocalDateTime registerTime;

	/**
	 * 登录时间
	 */
	private LocalDateTime loginTime;

	/**
	 * 6位邀请码
	 */
	private String invitecode;

	/**
	 * 用户分享码
	 */
	private String channelCode;

	/**
	 * 邀请人渠道码
	 */
	private String inviterChannelCode;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 审核人
	 */
	private Integer auditUser;

	/**
	 * 营业执照路径
	 */
	private String businessLicense;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 地区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 商家腾讯地图坐标
	 */
	private String poiNote;

	/**
	 * 审核备注
	 */
	private String remark;

	/**
	 * 店铺招牌
	 */
	private String shopSign;

	/**
	 * 其他证明照片
	 */
	private String otherProof;

	/**
	 * 上次下单时间
	 */
	private LocalDateTime lastOrderTime;

	/**
	 * 
	 */
	private Integer areaNo;

	/**
	 * 1大客户\2大连锁3\小连锁\4单点
	 */
	private String size;

	/**
	 * 客户类型
	 */
	private String type;

	/**
	 * 商圈
	 */
	private String tradeArea;

	/**
	 * 商圈组
	 */
	private String tradeGroup;

	/**
	 * 
	 */
	private String unionid;

	/**
	 * 
	 */
	private String mpOpenid;

	/**
	 * 用户ID
	 */
	private Long adminId;

	/**
	 * 1是直营 2是加盟
1 账期 2  现结


	 */
	private Integer direct;

	/**
	 * 1服务区内 2服务区外
	 */
	private Integer server;

	/**
	 * 
	 */
	private Integer popView;

	/**
	 * 会员当月积分
	 */
	private BigDecimal memberIntegral;

	/**
	 * 会员等级
	 */
	private Integer grade;

	/**
	 * 
	 */
	private Integer skuShow;

	/**
	 * 余额
	 */
	private BigDecimal rechargeAmount;

	/**
	 * 可提现金额
	 */
	private BigDecimal cashAmount;

	/**
	 * cash_amount更新时间
	 */
	private LocalDateTime cashUpdateTime;

	/**
	 * 配送单是否展示价格
	 */
	private Integer showPrice;

	/**
	 * 账号合并人
	 */
	private String mergeAdmin;

	/**
	 * 账号和并时间
	 */
	private LocalDateTime mergeTime;

	/**
	 * 首次登录弹窗：0、未弹 1、已弹
	 */
	private Integer firstLoginPop;

	/**
	 * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
	 */
	private Integer changePop;

	/**
	 * 拉黑备注
	 */
	private String pullBlackRemark;

	/**
	 * 操作人
	 */
	private String pullBlackOperator;

	/**
	 * 门牌号
	 */
	private String houseNumber;

	/**
	 * 企业品牌
	 */
	private String companyBrand;

	/**
	 * 是否选择线索池 0 不是 1 是
	 */
	private Integer cluePool;

	/**
	 * 大客户类型: ka,批发大客户,普通
	 */
	private String merchantType;

	/**
	 * 规模
	 */
	private String enterpriseScale;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 
	 */
	private Integer examineType;

	/**
	 * 开关状态 0 开（展示） 1 关（不展示）
	 */
	private Integer displayButton;

	/**
	 * 运营状态:正常(0),倒闭(1)
	 */
	private Integer operateStatus;

	/**
	 * 更新人adminId
	 */
	private Integer updater;

	/**
	 * 门头照片
	 */
	private String doorPic;

	/**
	 * 预注册标记,1- 预注册，0- 非预注册
	 */
	private Integer preRegisterFlag;
	/**
	 * tms 打印配送单
	 */
	private Boolean printOutTMSConfig;

	/**
	 * 业务线 0=鲜沐;1=pop
	 */
	private Integer businessLine;

	/**
	 * 转换鲜沐门店的类型
	 * 默认：未知
	 *
	 * @return
	 */
	public static Integer transTypeFromXm(String sourceType) {
		if (StrUtil.isBlank(sourceType)) {
			return MerchantStoreEnums.Type.UN_KNOW.getCode();
		}
		Integer type;
		switch (sourceType) {
			case "个人店":
				type = MerchantStoreEnums.Type.PERSONAL.getCode();
				break;
			case "托管店":
				type = MerchantStoreEnums.Type.MANAGED.getCode();
				break;
			case "加盟店":
			case "连锁-加盟店":
				type = MerchantStoreEnums.Type.JOINING.getCode();
				break;
			case "直营店":
				type = MerchantStoreEnums.Type.DIRECT.getCode();
				break;
			case "连锁店":
				type = MerchantStoreEnums.Type.CHAIN.getCode();
				break;
			default:
				type = MerchantStoreEnums.Type.UN_KNOW.getCode();
		}
		return type;
	}


	public static String transSizeFromUserCenter(Integer sourceSize) {
		return ObjectUtil.equal(sourceSize, RegionalOrganizationEnums.Size.ADMIN.getCode()) ? RegionalOrganizationEnums.Size.ADMIN.getDesc() : RegionalOrganizationEnums.Size.MERCHANT.getDesc();
	}

	public static String transTypeFromUserCenter(Integer sourceType) {
		if (null == sourceType) {
			return MerchantStoreEnums.Type.UN_KNOW.getDesc();
		}
		String type;
		switch (sourceType) {
			case 3:
				type = MerchantStoreEnums.Type.PERSONAL.getDesc();
				break;
			case 2:
				type = MerchantStoreEnums.Type.MANAGED.getDesc();
				break;
			case 1:
				type = MerchantStoreEnums.Type.JOINING.getDesc();
				break;
			case 0:
				type = MerchantStoreEnums.Type.DIRECT.getDesc();
				break;
			case 4:
				type = MerchantStoreEnums.Type.CHAIN.getDesc();
				break;
			default:
				type = MerchantStoreEnums.Type.UN_KNOW.getDesc();
		}
		return type;
	}



	/**
	 * 转换鲜沐门店的状态
	 *
	 * @return
	 */
	public static Integer transStatusFromUserCenter(Integer sourceStatus) {
		if (sourceStatus == null) {
			return null;
		}
		Integer status;
		switch (sourceStatus) {
			case 1:
				status = 0;
				break;
			case 0:
				status = 1;
				break;
			case 2:
				status = 2;
				break;
			case 4:
				status = 3;
				break;
			case 5:
				status = 4;
				break;
			default:
				status = 0;
		}
		return status;
	}

	
}