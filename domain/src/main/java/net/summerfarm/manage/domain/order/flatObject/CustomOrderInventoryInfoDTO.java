package net.summerfarm.manage.domain.order.flatObject;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/16 15:19
 * @Version 1.0
 */
@Data
public class CustomOrderInventoryInfoDTO implements Serializable {
    private static final long serialVersionUID = -1618059803338240930L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * sku
     */
    private String skuCode;

    /**
     * 商品名称（快照）
     */
    private String pdName;

    /**
     * 重量/规格
     */
    private String weight;

    /**
     * 订单数量
     */
    private Integer orderQuantity;

    /**
     * 订单实付金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 销售库存数量
     */
    private Integer saleInventoryQuantity;
}
