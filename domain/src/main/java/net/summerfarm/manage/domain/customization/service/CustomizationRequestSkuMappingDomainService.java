package net.summerfarm.manage.domain.customization.service;

import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestSkuMappingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定制需求sku关联领域服务
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class CustomizationRequestSkuMappingDomainService {

    @Autowired
    private CustomizationRequestSkuMappingRepository customizationRequestSkuMappingRepository;
}
