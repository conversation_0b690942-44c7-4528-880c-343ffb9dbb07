package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.ExternalProductMappingCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.summerfarm.manage.domain.product.repository.ExternalProductMappingQueryRepository;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;

import javax.annotation.Resource;

/**
 *
 * @Title: 外部平台商品映射表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Service
public class ExternalProductMappingCommandDomainService {


    @Resource
    private ExternalProductMappingCommandRepository externalProductMappingCommandRepository;
    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;



    public ExternalProductMappingEntity insert(ExternalProductMappingCommandParam param) {
        return externalProductMappingCommandRepository.insertSelective(param);
    }


    public int update(ExternalProductMappingCommandParam param) {
        return externalProductMappingCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return externalProductMappingCommandRepository.remove(id);
    }
}
