package net.summerfarm.manage.domain.major.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-02-27 15:22:25
 * @version 1.0
 *
 */
@Data
public class MajorRebateCommandParam {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private String sku;

	/**
	 * 
	 */
	private String name;

	/**
	 * 
	 */
	private String weight;

	/**
	 * 1定额钱 2 定额百分比
	 */
	private Integer type;

	/**
	 * 
	 */
	private Double number;

	/**
	 * 
	 */
	private Integer adminId;

	/**
	 * 
	 */
	private Integer areaNo;

	/**
	 * 
	 */
	private String areaName;

	/**
	 * 1 是类目 2代表是sku
	 */
	private Integer cate;

	/**
	 * 1存在 2删除
	 */
	private Integer status;

	/**
	 * 
	 */
	private LocalDateTime addTime;

	/**
	 * 
	 */
	private LocalDateTime updateTime;

	

	
}