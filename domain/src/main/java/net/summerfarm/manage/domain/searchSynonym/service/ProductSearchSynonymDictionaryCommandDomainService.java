package net.summerfarm.manage.domain.searchSynonym.service;


import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryQueryRepository;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryCommandRepository;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: ES用到的同义词表，每一行是一组同义词，比如："葡萄,巨峰,巨峰葡萄,夏黑,夏黑葡萄,青提,晴王青提,阳光玫瑰"领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
@Service
public class ProductSearchSynonymDictionaryCommandDomainService {


    @Autowired
    private ProductSearchSynonymDictionaryCommandRepository productSearchSynonymDictionaryCommandRepository;
    @Autowired
    private ProductSearchSynonymDictionaryQueryRepository productSearchSynonymDictionaryQueryRepository;



    public ProductSearchSynonymDictionaryEntity insert(ProductSearchSynonymDictionaryCommandParam param) {
        return productSearchSynonymDictionaryCommandRepository.insertSelective(param);
    }


    public int update(ProductSearchSynonymDictionaryCommandParam param) {
        return productSearchSynonymDictionaryCommandRepository.updateSelectiveById(param);
    }


    public int delete(Integer id) {
        return productSearchSynonymDictionaryCommandRepository.remove(id);
    }
}
