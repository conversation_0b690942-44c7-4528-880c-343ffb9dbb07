package net.summerfarm.manage.domain.coupon.repository;



import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.CouponCommandParam;




/**
*
* <AUTHOR>
* @date 2024-12-19 13:49:12
* @version 1.0
*
*/
public interface CouponCommandRepository {

    CouponEntity insertSelective(CouponCommandParam param);

    int updateSelectiveById(CouponCommandParam param);

    int remove(Long id);

}