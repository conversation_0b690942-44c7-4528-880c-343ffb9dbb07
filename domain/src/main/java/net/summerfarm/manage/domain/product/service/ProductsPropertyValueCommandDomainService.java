package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueCommandRepository;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;


/**
 *
 * @Title: 商品属性值领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 16:02:28
 * @version 1.0
 *
 */
@Service
public class ProductsPropertyValueCommandDomainService {


    @Autowired
    private ProductsPropertyValueCommandRepository productsPropertyValueCommandRepository;
    @Autowired
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;


    public ProductsPropertyValueEntity insert(ProductsPropertyValueCommandParam param) {
        return productsPropertyValueCommandRepository.insertSelective(param);
    }


    public int update(ProductsPropertyValueCommandParam param) {
        return productsPropertyValueCommandRepository.updateSelectiveById(param);
    }

    public void  addSalePropertyValue(String sku, Long pdId, List<ProductsPropertyValueCommandParam> commandParams) {
        List<ProductsPropertyValueEntity> filledValueList = productsPropertyValueQueryRepository.listBySkuAndPdid(sku, pdId);
        commandParams.stream()
                .filter(el -> {
                    for (ProductsPropertyValueEntity valueVO : filledValueList) {
                        if (Objects.equals(valueVO.getProductsPropertyId(), el.getProductsPropertyId())) {
                            //属性值相同，不更新数据库
                            if (Objects.equals(valueVO.getProductsPropertyValue(), el.getProductsPropertyValue())) {
                                return false;
                            }

                            el.setId(valueVO.getId());
                            el.setPdId(pdId);
                            el.setSku(sku);
                            el.setCreateTime(LocalDateTime.now());
                            productsPropertyValueCommandRepository.updateSelectiveById(el);
                            return false;
                        }
                    }
                    return true;
                })
                .forEach(el -> {
                    el.setPdId(pdId);
                    el.setSku(sku);
                    el.setCreateTime(LocalDateTime.now());
                    productsPropertyValueCommandRepository.insertSelective(el);
                });
    }

    public void deleteSpuProperty(Long pdId) {
        productsPropertyValueCommandRepository.deleteByPdId(pdId);
    }

    public void addKeyPropertyValue(Long pdId, List<ProductsPropertyValueCommandParam> commandParams) {
        List<ProductsPropertyValueEntity> filledValueList = productsPropertyValueQueryRepository.listBySkuAndPdid(null, pdId);

        commandParams.stream()
                .filter(el -> {
                    for (ProductsPropertyValueEntity valueVO : filledValueList) {
                        if (Objects.equals(valueVO.getProductsPropertyId(), el.getProductsPropertyId())) {
                            //属性值相同，不更新数据库
                            if (Objects.equals(valueVO.getProductsPropertyValue(), el.getProductsPropertyValue())) {
                                return false;
                            }
                            el.setId(valueVO.getId());
                            el.setPdId(pdId);
                            el.setCreateTime(LocalDateTime.now());
                            productsPropertyValueCommandRepository.updateSelectiveById(el);
                            return false;
                        }
                    }
                    return true;
                })
                .forEach(el -> {
                    el.setPdId(pdId);
                    el.setCreateTime(LocalDateTime.now());
                    productsPropertyValueCommandRepository.insertSelective(el);
                });
    }


    public void deleteByPdId(Long pdId, Set<Integer> productsPropertyIds) {
        productsPropertyValueCommandRepository.deleteByPdIdAndPropertyIds(pdId, productsPropertyIds);
    }
}
