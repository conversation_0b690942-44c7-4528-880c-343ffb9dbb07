package net.summerfarm.manage.domain.merchant.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;



/**
*
* <AUTHOR>
* @date 2023-12-27 14:01:49
* @version 1.0
*
*/
public interface MerchantCancelQueryRepository {

    PageInfo<MerchantCancelEntity> getPage(MerchantCancelQueryParam param);

    MerchantCancelEntity selectById(Long id);

    List<MerchantCancelEntity> selectByCondition(MerchantCancelQueryParam param);

}