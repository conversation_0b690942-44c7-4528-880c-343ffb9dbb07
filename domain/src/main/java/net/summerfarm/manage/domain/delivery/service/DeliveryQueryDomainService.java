package net.summerfarm.manage.domain.delivery.service;

import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * Description: 查询服务<br/>
 * date: 2024/6/5 15:20<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryQueryDomainService {

    @Resource
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;

    public List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(LocalDate startDate, LocalDate endDate, Integer storeNo) {
        if(startDate == null || endDate == null || storeNo == null){
            throw new BizException("开始时间、结束时间、城配仓编号均不能为空");
        }
        //最大间隔时间判断
        long days = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
        if(days > 100){
            throw new BizException("日期间隔天数最大为100天");
        }
        return deliveryPlanQueryRepository.queryTimingOrderNoFreezeProxySaleNoWarehouse(startDate,endDate,storeNo);
    }
}
