package net.summerfarm.manage.domain.admin.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-06-18 23:13:51
 * @version 1.0
 *
 */
@Data
public class AdminDataPermissionQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private Integer adminId;

	/**
	 * 
	 */
	private String permissionValue;

	/**
	 * 
	 */
	private String permissionName;

	/**
	 * 
	 */
	private LocalDateTime addtime;

	/**
	 * 
	 */
	private String type;

	

	
}