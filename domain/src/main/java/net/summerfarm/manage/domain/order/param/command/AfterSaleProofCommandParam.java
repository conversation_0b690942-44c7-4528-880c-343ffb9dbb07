package net.summerfarm.manage.domain.order.param.command;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-01-18 16:27:13
 * @version 1.0
 *
 */
@Data
public class AfterSaleProofCommandParam {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private String afterSaleOrderNo;

	/**
	 * 
	 */
	private Integer quantity;

	/**
	 * 
	 */
	private BigDecimal handleNum;

	/**
	 * 
	 */
	private String proofPic;

	/**
	 * 
	 */
	private String afterSaleType;

	/**
	 * 
	 */
	private String refundType;

	/**
	 * 
	 */
	private Integer handleType;

	/**
	 * 
	 */
	private String handler;

	/**
	 * 原因
	 */
	private String handleRemark;

	/**
	 * 
	 */
	private String auditer;

	/**
	 * 
	 */
	private Integer status;

	/**
	 * 
	 */
	private String applyRemark;

	/**
	 * 
	 */
	private LocalDateTime updatetime;

	/**
	 * 
	 */
	private String applyer;

	/**
	 * 
	 */
	private String auditeRemark;

	/**
	 * 审核备注
	 */
	private String extraRemark;

	/**
	 * 
	 */
	private LocalDateTime auditetime;

	/**
	 * 
	 */
	private LocalDateTime handletime;

	/**
	 * 回收废金额
	 */
	private BigDecimal recoveryNum;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 二级审核原因
	 */
	private String handleSecondaryRemark;

	/**
	 * 二级售后分类
	 */
	private String applySecondaryRemark;

	/**
	 * 售后视频
	 */
	private String proofVideo;

	

	
}