package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryRecordCommandParam;
import net.summerfarm.manage.domain.product.repository.InventoryRecordCommandRepository;
import net.summerfarm.manage.domain.product.repository.InventoryRecordQueryRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 *
 * @Title: sku信息变化记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 15:10:53
 * @version 1.0
 *
 */
@Service
public class InventoryRecordCommandDomainService {


    @Autowired
    private InventoryRecordCommandRepository inventoryRecordCommandRepository;
    @Autowired
    private InventoryRecordQueryRepository inventoryRecordQueryRepository;



    public InventoryRecordEntity insert(InventoryRecordCommandParam param) {
        return inventoryRecordCommandRepository.insertSelective(param);
    }


    public int update(InventoryRecordCommandParam param) {
        return inventoryRecordCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return inventoryRecordCommandRepository.remove(id);
    }

    public void saveBatch(List<InventoryRecordCommandParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        inventoryRecordCommandRepository.saveBatch(params);
    }
}
