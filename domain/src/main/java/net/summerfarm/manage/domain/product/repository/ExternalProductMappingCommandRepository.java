package net.summerfarm.manage.domain.product.repository;


import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;

/**
*
* <AUTHOR>
* @date 2024-11-15 14:13:27
* @version 1.0
*
*/
public interface ExternalProductMappingCommandRepository {

    ExternalProductMappingEntity insertSelective(ExternalProductMappingCommandParam param);

    int updateSelectiveById(ExternalProductMappingCommandParam param);

    int remove(Long id);

}