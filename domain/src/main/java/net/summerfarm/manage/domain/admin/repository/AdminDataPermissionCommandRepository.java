package net.summerfarm.manage.domain.admin.repository;



import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.command.AdminDataPermissionCommandParam;




/**
*
* <AUTHOR>
* @date 2024-06-18 23:13:51
* @version 1.0
*
*/
public interface AdminDataPermissionCommandRepository {

    AdminDataPermissionEntity insertSelective(AdminDataPermissionCommandParam param);

    int updateSelectiveById(AdminDataPermissionCommandParam param);

    int remove(Long id);

}