package net.summerfarm.manage.domain.product.param.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.manage.domain.major.valueobject.MajorPriceValueObject;
import net.xianmu.common.input.BasePageInput;
import org.hibernate.validator.constraints.Range;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-04-08 15:19:21
 * @version 1.0
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MajorPricePageQueryParam extends BasePageInput {
	/**
	 * adminId
	 */
	private Integer adminId;

	/**
	 * 1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * sku编号
	 */
	private String sku;

	/**
	 * 商品名称
	 */
	private String pdName;

	/**
	 * 库存仓-上架时指定
	 */
	private Integer warehouseNo;

	/**
	 * 报价单状态：0: 未生效， 1：已生效， 2：失效,3:待提交
	 */
	private Integer validStatus;

	/**
	 * 性质 1:代销不入仓;2:代销入仓;3:经销;4:代仓;
	 */
	private Integer subType;

	/**
	 * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮 4商城价下浮 5商城价加价 6商城价减价
	 */
	private Integer priceType;
	/**
	 * 城市
	 */
	private Integer areaNo;

	/**
	 * 运营大区
	 */
	private Integer largeAreaNo;

	List<MajorPriceValueObject> largeNoAndSkuList;

	
}