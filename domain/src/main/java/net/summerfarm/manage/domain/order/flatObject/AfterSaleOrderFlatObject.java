package net.summerfarm.manage.domain.order.flatObject;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName AfterSaleOrderFlatObject
 * @Description TODO
 * <AUTHOR>
 * @Date 17:15 2024/1/19
 * @Version 1.0
 **/
@Data
public class AfterSaleOrderFlatObject {

    /**
     *售后单
     */
    private String afterSaleOrderNo;

    /**
     *售后数量
     */
    private Integer quantity;

    /**
     *补偿数量
     */
    private BigDecimal handleNum;

    /**
     *
     */
    private String refundType;

    /**
     *
     */
    private Integer handleType;

    /**
     * 0 未到货售后 1 已到货售后
     */
    private Integer deliveryed;

    /**
     *
     */
    private Integer status;
}
