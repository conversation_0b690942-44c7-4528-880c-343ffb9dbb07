package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-05-07 16:18:00
 * @version 1.0
 *
 */
@Data
public class ProductsPropertyMappingQueryParam extends BasePageInput {
	/**
	 * 主键、自增
	 */
	private Integer id;

	/**
	 * 映射类型：0、类目 1、spu
	 */
	private Integer type;

	/**
	 * 类目id/pd id
	 */
	private Integer mappingId;

	/**
	 * 映射id列表
	 */
	private List<Integer> mappingIdList;

	/**
	 * 属性id
	 */
	private Integer productsPropertyId;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}