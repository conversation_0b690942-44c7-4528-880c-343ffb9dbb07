package net.summerfarm.manage.domain.admin.repository;



import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import net.summerfarm.manage.domain.admin.param.command.InvitecodeCommandParam;




/**
*
* <AUTHOR>
* @date 2024-06-18 13:07:30
* @version 1.0
*
*/
public interface InvitecodeCommandRepository {

    InvitecodeEntity insertSelective(InvitecodeCommandParam param);

    int updateSelectiveById(InvitecodeCommandParam param);

    int remove(Long inviteId);

}