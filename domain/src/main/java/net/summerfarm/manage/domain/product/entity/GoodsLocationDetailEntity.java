package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024-05-07 14:12:49
 * @version 1.0
 *
 */
@Data
public class GoodsLocationDetailEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 货位编号
	 */
	private String glNo;

	/**
	 * 批次
	 */
	private String batch;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 保质期
	 */
	private Date qualityDate;

	
	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 状态 0 生效 1 失效
	 */
	private Integer status;

	/**
	 * 销售出库任务锁定库存
	 */
	private Integer saleLockQuantity;

	/**
	 * 生产时期
	 */
	private Date productionDate;

	/**
	 * 总数量 - 销售冻结数量
	 */
	private Integer approveQuantity;
	

	
}