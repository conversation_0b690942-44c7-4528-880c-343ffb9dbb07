package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductLabelValueCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:46
* @version 1.0
*
*/
public interface ProductLabelValueCommandRepository {

    ProductLabelValueEntity insertSelective(ProductLabelValueCommandParam param);

    int updateSelectiveById(ProductLabelValueCommandParam param);

    int remove(Long id);

}