package net.summerfarm.manage.domain.customization.repository;

import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定制需求sku关联仓储接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface CustomizationRequestSkuMappingRepository {

    /**
     * 保存定制需求sku关联
     * 
     * @param entity 定制需求sku关联实体
     * @return 保存后的实体
     */
    CustomizationRequestSkuMappingEntity save(CustomizationRequestSkuMappingEntity entity);

    /**
     * 批量保存定制需求sku关联
     * 
     * @param entities 定制需求sku关联实体列表
     * @return 保存的数量
     */
    int batchSave(List<CustomizationRequestSkuMappingEntity> entities);

    /**
     * 根据ID删除定制需求sku关联
     * 
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据定制需求ID删除关联的sku
     * 
     * @param customizationRequestId 定制需求ID
     * @return 删除的数量
     */
    int deleteByCustomizationRequestId(Long customizationRequestId);

    /**
     * 批量删除定制需求sku关联
     * 
     * @param ids 主键ID列表
     * @return 删除的数量
     */
    int deleteByIds(List<Long> ids);

    /**
     * 更新定制需求sku关联
     * 
     * @param entity 定制需求sku关联实体
     * @return 更新后的实体
     */
    CustomizationRequestSkuMappingEntity update(CustomizationRequestSkuMappingEntity entity);

    /**
     * 根据ID查询定制需求sku关联
     * 
     * @param id 主键ID
     * @return 定制需求sku关联实体
     */
    CustomizationRequestSkuMappingEntity findById(Long id);

    /**
     * 根据定制需求ID查询关联的sku列表
     * 
     * @param customizationRequestId 定制需求ID
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMappingEntity> findByCustomizationRequestId(Long customizationRequestId);

    /**
     * 根据sku查询定制需求sku关联
     * 
     * @param sku sku
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMappingEntity> findBySku(String sku);

    /**
     * 根据模版sku查询定制需求sku关联
     * 
     * @param sourceSku 模版sku
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMappingEntity> findBySourceSku(String sourceSku);

    /**
     * 查询定制需求sku关联总数
     *
     * @param entity 查询条件
     * @return 总数
     */
    long count(CustomizationRequestSkuMappingEntity entity);

    /**
     * 查询创建时间超过指定时间的记录
     *
     * @param beforeTime 指定时间
     * @return 定制需求sku关联列表
     */
    List<CustomizationRequestSkuMappingEntity> findByCreateTimeBefore(LocalDateTime beforeTime);
}
