package net.summerfarm.manage.domain.afterSale.service;


import net.summerfarm.manage.domain.afterSale.repository.AfterSaleDeliveryPathQueryRepository;
import net.summerfarm.manage.domain.afterSale.repository.AfterSaleDeliveryPathCommandRepository;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.param.command.AfterSaleDeliveryPathCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 售后(换货,补发,退货退款)配送信息领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-12-31 14:32:57
 * @version 1.0
 *
 */
@Service
public class AfterSaleDeliveryPathCommandDomainService {


    @Autowired
    private AfterSaleDeliveryPathCommandRepository afterSaleDeliveryPathCommandRepository;
    @Autowired
    private AfterSaleDeliveryPathQueryRepository afterSaleDeliveryPathQueryRepository;



    public AfterSaleDeliveryPathEntity insert(AfterSaleDeliveryPathCommandParam param) {
        return afterSaleDeliveryPathCommandRepository.insertSelective(param);
    }


    public int update(AfterSaleDeliveryPathCommandParam param) {
        return afterSaleDeliveryPathCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return afterSaleDeliveryPathCommandRepository.remove(id);
    }
}
