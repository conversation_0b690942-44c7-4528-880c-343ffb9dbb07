package net.summerfarm.manage.domain.sampleApply.repository;



import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import net.summerfarm.manage.domain.sampleApply.param.command.SampleApplyCommandParam;




/**
*
* <AUTHOR>
* @date 2025-01-02 14:00:39
* @version 1.0
*
*/
public interface SampleApplyCommandRepository {

    SampleApplyEntity insertSelective(SampleApplyCommandParam param);

    int updateSelectiveById(SampleApplyCommandParam param);

    int remove(Long sampleId);

}