package net.summerfarm.manage.domain.activity.service;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.manage.common.enums.InventoryExtTypeEnum;
import net.summerfarm.manage.common.enums.activity.ActivityStatusEnum;
import net.summerfarm.manage.common.enums.activity.ActivityTypeEnum;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import net.summerfarm.manage.domain.activity.param.query.ActivityScopeQueryParam;
import net.summerfarm.manage.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivityBasicInfoCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.summerfarm.manage.domain.activity.valueObject.ActivityItemScopeValueObject;
import net.summerfarm.manage.domain.activity.valueObject.ActivityLadderPriceValueObject;
import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject;
import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuValueObject;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.domain.product.service.InventoryQueryDomainService;
import net.summerfarm.repository.other.dto.Inventory;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.client.cms.common.enums.ScopeTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 *
 * @Title: 活动基本信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Service
public class ActivityBasicInfoQueryDomainService {

    @Resource
    private AreaQueryRepository areaQueryRepository;
    @Resource
    private ActivityBasicInfoQueryRepository activityBasicInfoQueryRepository;
    @Resource
    private ActivitySkuDetailQueryRepository detailQueryRepository;
    @Resource
    private ActivitySkuPriceQueryRepository priceQueryRepository;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;
    @Resource
    private InventoryQueryDomainService inventoryQueryDomainService;






    public List<ActivitySkuValueObject> listActivitySku(List<String> skus, Integer areaNo) {
        List<ActivitySkuValueObject> list = Lists.newArrayList();
        for (String sku : skus) {
            ActivitySkuValueObject activitySku = selectActivitySku(sku, areaNo);
            if (activitySku != null) {
                list.add(activitySku);
            }
        }
        return list;
    }

    public ActivitySkuValueObject selectActivitySku(String sku, Integer areaNo) {
        List<ActivityItemScopeValueObject> configs = this.listActivityItemConfigs(
                Lists.newArrayList(sku),
                areaNo);
        if (CollectionUtil.isEmpty(configs)) {
            return null;
        }
        Map<Long, Long> itemConfigBasicMap = configs.stream()
                .collect(Collectors.toMap(ActivityItemScopeValueObject::getItemConfigId, ActivityItemScopeValueObject::getBasicInfoId, (p1, p2) -> p2));
        List<Long> itemConfigIds = Lists.newArrayList(itemConfigBasicMap.keySet());
        //查询是否有活动商品
        List<ActivitySkuDetailValueObject> skuDetailList = detailQueryRepository.listDetailByItemConfigs(
                itemConfigIds, sku);
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            ActivitySkuValueObject activitySku = new ActivitySkuValueObject();
            //获取到sku信息，包含价格
            //如果当前sku在运营城市有多个特价活动，取最晚创建活动的活动价, 先按时间、再按id（这里需要优化成按优先级排序）
            List<ActivitySkuDetailValueObject> sortDetailList = skuDetailList.stream().sorted(Comparator.comparing(ActivitySkuDetailValueObject::getActivityCreateTime,Comparator.reverseOrder()).thenComparing(ActivitySkuDetailValueObject::getBasicInfoId, Comparator.reverseOrder())).collect(Collectors.toList());
            ActivitySkuDetailValueObject skuDetail = sortDetailList.get(0);
            ActivitySkuPriceEntity skuPrice = priceQueryRepository.selectByDetailId(
                    skuDetail.getId(), sku, areaNo);
            //在创建活动后新增了运营区域，如果特价活动配置的是大区可能导致新增的活动区域没有活动价，需要当做不是活动商品处理
            if (skuPrice == null) {
                return null;
            }
            // 默认的活动价根据最小起售量计算
            int unit = inventoryQueryDomainService.selectMinSaleUnit(sku);
            BigDecimal activityPrice = skuPrice.getLadderPrice() == null ? skuPrice.getActivityPrice() : ActivityLadderPriceValueObject.getLadderPriceByUnit(JSON.parseArray(skuPrice.getLadderPrice(), ActivityLadderPriceValueObject.class), unit, skuPrice.getSalePrice());
            activitySku.setActivityId(itemConfigBasicMap.get(skuDetail.getItemConfigId()));
            activitySku.setSku(skuDetail.getSku());
            activitySku.setSalePrice(skuPrice.getSalePrice());
            activitySku.setActivityPrice(activityPrice);
            activitySku.setLadderPrice(skuPrice.getLadderPrice());
            return activitySku;
        }
        return null;
    }


    public List<ActivityItemScopeValueObject> listActivityItemConfigs(List<String> skus, Integer areaNo) {
        //查询运营城市生效中的临保或者特价活动,不需要考虑人群包的活动
        List<ActivityScopeQueryParam> scopeList = buildScopeList(areaNo);
        List<ActivityItemScopeValueObject> itemConfigs = Lists.newArrayList();
        //特价活动查询
        List<ActivityItemScopeValueObject> specialItemConfigs = activityBasicInfoQueryRepository.listByScope(
                scopeList,
                ActivityTypeEnum.SPECIAL_PRICE.getCode(), ActivityStatusEnum.EFFECTING.getCode());
        itemConfigs.addAll(specialItemConfigs);

        if (skus.size() == 1) {
            //临保活动查询（非临保品可跳过）
            InventoryEntity inventory = inventoryQueryRepository.queryBySku(skus.get(0));
            //有可能因为sku是失效的查不到
            if (inventory == null) {
                return itemConfigs;
            }
            if (Objects.equals(inventory.getExtType(),
                    InventoryExtTypeEnum.TEMPORARY_INSURANCE.type())) {
                List<ActivityItemScopeValueObject> extItemConfigs = activityBasicInfoQueryRepository.listByScope(
                        scopeList,
                        ActivityTypeEnum.NEAR_EXPIRED.getCode(),
                        ActivityStatusEnum.EFFECTING.getCode());
                itemConfigs.addAll(extItemConfigs);
            }
        } else {
            List<ActivityItemScopeValueObject> extItemConfigs = activityBasicInfoQueryRepository.listByScope(
                    scopeList,
                    ActivityTypeEnum.NEAR_EXPIRED.getCode(),
                    ActivityStatusEnum.EFFECTING.getCode());
            itemConfigs.addAll(extItemConfigs);
        }

        return itemConfigs;
    }


    private List<ActivityScopeQueryParam> buildScopeList(Integer areaNo) {
        Area area = areaQueryRepository.selectByAreaNo(areaNo);
        Integer largeAreaNo = area.getLargeAreaNo();
        List<ActivityScopeQueryParam> scopeList = Lists.newArrayList();
        scopeList.add(
                new ActivityScopeQueryParam(Long.valueOf(areaNo), ScopeTypeEnum.AREA.getCode()));
        scopeList.add(new ActivityScopeQueryParam(Long.valueOf(largeAreaNo),
                ScopeTypeEnum.LARGE_AREA.getCode()));
        return scopeList;
    }

    public List<ActivityItemScopeValueObject> selectActivityByAreaNoAndType( Integer areaNo, Integer type) {
        //查询运营城市生效中的临保或者特价活动,不需要考虑人群包的活动
        List<ActivityScopeQueryParam> scopeList = buildScopeList(areaNo);
        //特价活动查询
        return activityBasicInfoQueryRepository.listByScope(scopeList,type, ActivityStatusEnum.EFFECTING.getCode());
    }

}
