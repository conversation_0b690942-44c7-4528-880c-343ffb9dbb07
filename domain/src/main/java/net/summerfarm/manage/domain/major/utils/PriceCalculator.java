package net.summerfarm.manage.domain.major.utils;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.enums.MajorPriceTypeEnum;
import net.summerfarm.manage.common.util.ValidateUtil;
import net.xianmu.common.exception.BizException;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description:
 * @date 2024-04-08 15:19:21
 */
@Slf4j
public class PriceCalculator {


    /**
     * 报价单计算
     * @param price ：报价单的报价
     * @param salePrice ：商城价
     * @param priceAdjustmentValue ：商城价浮动/加减 值
     * @param priceType ：价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮百分比 4商城价下浮百分比 5商城价加价 6商城价减价
     * @return
     */
    public static BigDecimal calculateMajorPriceByType(BigDecimal price, BigDecimal salePrice,
                                                BigDecimal priceAdjustmentValue, Integer priceType) {
        log.info("【开始报价单价格计算】报价方式：{}, 报价：{}, 商城价：{}, 商城价浮动：{}", priceType, ValidateUtil.toStringWithNull(price), ValidateUtil.toStringWithNull(salePrice), ValidateUtil.toStringWithNull(priceAdjustmentValue));

        MajorPriceTypeEnum type = MajorPriceTypeEnum.fromCode(priceType);
        BigDecimal resultPrice = BigDecimal.ZERO;
        switch (type) {
            case MALL_PRICE:
                resultPrice = salePrice;
                break;
            case CONTRACT_PRICE_SPECIFIED:
            case CONTRACT_PRICE_MARGIN:
                resultPrice = price;
                break;
            case MALL_PRICE_ADD_RATE:
                resultPrice = salePrice.multiply(BigDecimal.ONE.add(priceAdjustmentValue.divide(new BigDecimal("100"), 2,RoundingMode.HALF_DOWN)))
                        .setScale(2, RoundingMode.CEILING);
                break;
            case MALL_PRICE_SUB_RATE:
                resultPrice = salePrice.multiply(BigDecimal.ONE.subtract(priceAdjustmentValue.divide(new BigDecimal("100"), 2,RoundingMode.HALF_DOWN)))
                        .setScale(2, RoundingMode.CEILING);
                break;
            case MALL_PRICE_ADD_PRICE:
                resultPrice = salePrice.add(priceAdjustmentValue)
                        .setScale(2, RoundingMode.CEILING);
                break;
            case MALL_PRICE_SUB_PRICE:
                resultPrice = salePrice.subtract(priceAdjustmentValue)
                        .setScale(2, RoundingMode.CEILING);
                break;
            default:
                throw new IllegalStateException("未处理的价格类型: " + type);
        }
        if(resultPrice == null || resultPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("价格小于或等于0!,最终报价：{} ", resultPrice);
            throw new BizException("价格计算异常,最终报价小于等于0，请调整后重试!");
        }

        log.info("【报价单价格计算完成】最终报价：{}", resultPrice);
        return resultPrice;
    }
}
