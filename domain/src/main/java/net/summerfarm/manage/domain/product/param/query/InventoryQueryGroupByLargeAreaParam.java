package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;

import java.util.List;

@Data
public class InventoryQueryGroupByLargeAreaParam {
    /**
     * spu name
     */
//    private String spuTitleLike;
    /**
     * adminId
     */
    private Long adminId;
    /**
     * sku
     */
    private String itemCode;
    /**
     * 运营大区
     */
    private List<Integer> largeAreaNos;
    /**
     * 级别
     */
    private List<String> levelPropertyNames;
    /**
     * 品种
     */
    private List<String> varietyList;
    /**
     * sku类型
     */
    private List<Integer> extTypes;
    private List<String> skus;
    private Integer pageIndex;
    private Integer pageSize;
}
