package net.summerfarm.manage.domain.product.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.param.query.ProductsQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-05-07 11:31:28
* @version 1.0
*
*/
public interface ProductsQueryRepository {

    PageInfo<ProductsEntity> getPage(ProductsQueryParam param);

    ProductsEntity selectById(Long pdId);

    List<ProductsEntity> selectByCondition(ProductsQueryParam param);

    int selectByPdNameCount(String realName);

    /**
     * 根据pdNo查询商品列表
     *
     * <AUTHOR>
     * @date 2024/8/13 16:58
     * @param pdNoList pdNo列表
     * @return java.util.List<net.summerfarm.manage.domain.product.entity.ProductsEntity>
     */
    List<ProductsEntity> selectListByPdNo(List<String> pdNoList);
}