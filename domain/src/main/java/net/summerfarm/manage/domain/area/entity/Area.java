package net.summerfarm.manage.domain.area.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.validation.groups.Add;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "城市实体类")
@Data
public class Area implements Serializable {

    @ApiModelProperty(value = "id")
    @Null(groups = Add.class)
    private Integer id;

    @ApiModelProperty(value = "城市编号")
    @Null(groups = Add.class)
    private Integer areaNo;

    @ApiModelProperty(value = "城市名称")
    @NotNull(groups = Add.class)
    private String areaName;

    @ApiModelProperty(value = "城市负责人")
    private Integer adminId;

    @Deprecated
    @ApiModelProperty(value = "父城市编号")
    private Integer parentNo;

    @ApiModelProperty(value = "运费")
    private BigDecimal deliveryFee;

    @ApiModelProperty(value = "是否开放，0不开放，1开放")
    private Boolean status;

    @ApiModelProperty(value = "具体地址")
    private String address;

    @ApiModelProperty(value = "详细信息")
    private String info;

    @ApiModelProperty(value = "快递费")
    private BigDecimal expressFee;

    @ApiModelProperty(value = "", hidden = true)
    private List<Area> children;

    @ApiModelProperty(value = "运费规则")
    private String deliveryRule;

    @ApiModelProperty(value = "会员规则")
    private String memberRule;

    /**
     * @see net.summerfarm.enums.PayChannelEnum
     */
    @ApiModelProperty(value = "收款途径")
    private Integer payChannel;

    @ApiModelProperty(value = "企业支付账号id")
    private Integer companyAccountId;

    @ApiModelProperty(value = "截单映射区域")
    private String mapSection;

    @ApiModelProperty(value = "免配送费日")
    private String freeDay;

    private String mailToAddress;

    @ApiModelProperty(value = "是否发送微信通知 0 否 1 是")
    private Integer weChatNotify;

    @ApiModelProperty(value = "通知标题")
    private String notifyTitle;

    @ApiModelProperty(value = "通知内容")
    private String notifyContent;

    @ApiModelProperty(value = "通知备注")
    private String notifyRemarks;

    @ApiModelProperty(value = "同步城市")
    private Integer originAreaNo;

    @ApiModelProperty(value = "开始/下次配送时间")
    private LocalDate nextDeliveryDate;

    @ApiModelProperty(value = "是否正在切换城市")
    private Boolean changeFlag;

    @ApiModelProperty(value = "切换的城市编号(处理完后清空)")
    private Integer changeStoreNo;

    @ApiModelProperty(value = "切换状态：0、默认 1、预约中 2、大客户停服 3、城市停服")
    private Integer changeStatus;

    @ApiModelProperty(value = "开始切仓时间")
    private LocalDateTime changStartTime;

    @ApiModelProperty(value = "排序失效时间")
    private LocalDateTime sortExpireTime;

    @ApiModelProperty(value = "城市的国家行政区域划分")
    private String administrativeArea;

    @ApiModelProperty(value = "是否支持加单 加单时间")
    private Integer supportAddOrder;

    @ApiModelProperty(value = "是否支持加单 加单时间 0 支持 1 不支持")
    private Integer updateSupportAddOrder;

    private Integer largeAreaNo;

    /**
     * 仓库类型:0本部仓、1外部仓、2合伙人仓
     */
    private Integer type;

    /**
     * bd维度-城市等级
     */
    private String grade;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;


    /**
     * '配送频率',
     */
    private String deliveryFrequent;

    /**
     * 微信小程序是否使用招行收款：0、不使用（默认）1、使用
     */
    private Integer wxlitePayChannel;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * POI字符串
     */
    private String poiNote;

    public Area() {
    }

    public Area(Integer id, Integer areaNo, String areaName) {
        this.id = id;
        this.areaNo = areaNo;
        this.areaName = areaName;
    }

}
