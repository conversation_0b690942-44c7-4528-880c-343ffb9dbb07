package net.summerfarm.manage.domain.major.service;


import net.summerfarm.manage.domain.major.repository.MajorPriceLogQueryRepository;
import net.summerfarm.manage.domain.major.repository.MajorPriceLogCommandRepository;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.command.MajorPriceLogCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 *
 * @Title: price log领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@Service
public class MajorPriceLogCommandDomainService {


    @Autowired
    private MajorPriceLogCommandRepository majorPriceLogCommandRepository;
    @Autowired
    private MajorPriceLogQueryRepository majorPriceLogQueryRepository;



    public MajorPriceLogEntity insert(MajorPriceLogCommandParam param) {
        return majorPriceLogCommandRepository.insertSelective(param);
    }

    public void insertBatch(List<MajorPriceLogCommandParam> params) {
        majorPriceLogCommandRepository.insertBatch(params);
    }


    public int update(MajorPriceLogCommandParam param) {
        return majorPriceLogCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return majorPriceLogCommandRepository.remove(id);
    }
}
