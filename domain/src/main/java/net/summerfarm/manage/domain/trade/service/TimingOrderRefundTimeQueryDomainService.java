package net.summerfarm.manage.domain.trade.service;


import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeQueryRepository;
import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeCommandRepository;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 省心送订单退款时间表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-22 14:45:54
 * @version 1.0
 *
 */
@Service
public class TimingOrderRefundTimeQueryDomainService {


}
