package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MarketItemBaseEntity {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * sku主键
     */
    private Long skuId;
    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 商品spuId
     */
    private Long marketId;
    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 规格备注（区间）
     */
    private String weightNotes;
    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;
    /**
     * 0下架 1上架
     */
    private Integer onSale;
    /**
     *  类型 0 自营 1 代仓
     */
    private Integer characters;
    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;
    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓 5-pop
     */
    private Integer subType;
}
