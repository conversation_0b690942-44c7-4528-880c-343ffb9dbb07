package net.summerfarm.manage.domain.merchant.repository;

import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import org.apache.ibatis.annotations.Param;

/**
*
* <AUTHOR>
* @date 2023-09-19 13:44:23
* @version 1.0
*
*/
public interface MerchantSubAccountCommandRepository {

    MerchantSubAccountEntity insertSelective(MerchantSubAccountEntity record);

    /**
     * 修改mid 切 门店状态
     * @param updateMid  要修改的
     * @param mId    where条件的
     * @return
     */
    Integer updateMain2Base(Long updateMid, Long mId);
}