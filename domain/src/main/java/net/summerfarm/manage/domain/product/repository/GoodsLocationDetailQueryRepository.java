package net.summerfarm.manage.domain.product.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import net.summerfarm.manage.domain.product.param.query.GoodsLocationDetailQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:49
* @version 1.0
*
*/
public interface GoodsLocationDetailQueryRepository {

    PageInfo<GoodsLocationDetailEntity> getPage(GoodsLocationDetailQueryParam param);

    GoodsLocationDetailEntity selectById(Long id);

    List<GoodsLocationDetailEntity> selectByCondition(GoodsLocationDetailQueryParam param);

    List<GoodsLocationDetailEntity> selectBySkus(List<String> skus);
}