package net.summerfarm.manage.domain.trade.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024-01-22 14:45:54
 * @version 1.0
 *
 */
@Data
public class TimingOrderRefundTimeQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 省心送订单号
	 */
	private String orderNo;

	/**
	 * 订单退款时间
	 */
	private Date refundTime;

	
	/**
	 * 商户id
	 */
	private Long mId;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	

	
}