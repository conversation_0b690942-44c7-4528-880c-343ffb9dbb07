package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordQueryRepository;
import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordCommandRepository;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 发货信息上传记录表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-10-15 17:49:41
 * @version 1.0
 *
 */
@Service
public class WxShippingInfoUploadRecordQueryDomainService {


}
