package net.summerfarm.manage.domain.activity.service;


import net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryQueryRepository;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryCommandRepository;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-31 17:39:38
 * @version 1.0
 *
 */
@Service
public class MarketRuleHistoryQueryDomainService {

    @Resource
    private MarketRuleHistoryQueryRepository marketRuleHistoryQueryRepository;

}
