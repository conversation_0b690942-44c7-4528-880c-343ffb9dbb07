package net.summerfarm.manage.domain.merchant.param.query;

import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2023-12-27 14:01:49
 * @version 1.0
 *
 */
@Data
public class MerchantCancelQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 门店ID
	 */
	private Long mId;

	/**
	 * 注销状态 默认-1（待注销） MerchantCancelEnum
	 */
	private Integer status;

	/**
	 * 申请原因
	 */
	private String remake;

	/**
	 * 申请凭证-后台申请必填
	 */
	private String certificate;

	/**
	 * 申请人
	 */
	private Long creator;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 更新人
	 */
	private Long updater;

	/**
	 * 申请来源  0-商城  1-后台
	 */
	private Integer resource;

	/**
	 * 注册手机号
	 */
	private String phone;

	/**
	 * 门店名称
	 */
	private String mname;

	/**
	 * 运营区域
	 */
	private Integer areaNo;

	/**
	 * 申请开始时间
	 */
	private Date startTime;

	/**
	 * 申请结束时间
	 */
	private Date endTime;

	
}