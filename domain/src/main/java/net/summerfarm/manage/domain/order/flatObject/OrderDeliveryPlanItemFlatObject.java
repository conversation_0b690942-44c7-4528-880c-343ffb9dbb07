package net.summerfarm.manage.domain.order.flatObject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: 订单配送订单明细<br/>
 * date: 2024/12/31 10:52<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OrderDeliveryPlanItemFlatObject {
    /**
     * 订单项ID
     */
    private Long id;

    /**
     * 商品名称（快照）
     */
    private String pdName;

    /**
     * 产品编号
     */
    private String sku;

    /**
     * 重量/规格
     */
    private String weight;

    /**
     * 生熟度
     */
    private String maturity;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 购买数量
     */
    private Integer amount;

    /**
     * 购买时的商品价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 商品图片
     */
    private String picturePath;

    /**
     * 购买时间
     */
    private LocalDateTime addTime;

    /**
     * 仓储区域
     */
    private Integer storageLocation;

    /**
     * 订单项状态
     */
    private Integer status;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量kg
     */
    private BigDecimal weightNum;

    /**
     * 商品类型：0、普通商品 1、赠品
     */
    private Integer productType;

    /**
     * sku名称
     */
    private String skuName;
}
