package net.summerfarm.manage.domain.product.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-05-06 13:48:40
* @version 1.0
*
*/
public interface InventoryBindQueryRepository {

    PageInfo<InventoryBindEntity> getPage(InventoryBindQueryParam param);

    InventoryBindEntity selectById(Long id);

    List<InventoryBindEntity> selectByCondition(InventoryBindQueryParam param);

    InventoryBindEntity selectOneByCondition(InventoryBindQueryParam bindQueryParam);

    InventoryBindEntity selectByBindSkuAndExtType(InventoryBindQueryParam bindQueryParam);

    List<InventoryBindEntity> selectByPdIdAndExtType(InventoryBindQueryParam bindQueryParam);
}