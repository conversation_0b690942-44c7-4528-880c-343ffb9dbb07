package net.summerfarm.manage.domain.activity.repository;



import net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivityBasicInfoCommandParam;




/**
*
* <AUTHOR>
* @date 2024-04-09 14:55:30
* @version 1.0
*
*/
public interface ActivityBasicInfoCommandRepository {

    ActivityBasicInfoEntity insertSelective(ActivityBasicInfoCommandParam param);

    int updateSelectiveById(ActivityBasicInfoCommandParam param);

    int remove(Long id);

}