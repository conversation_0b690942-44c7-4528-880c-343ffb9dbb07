package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.FrontCategoryQueryRepository;
import net.summerfarm.manage.domain.product.repository.FrontCategoryCommandRepository;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 前台类目领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
@Service
public class FrontCategoryQueryDomainService {


}
