package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyMappingCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-05-07 16:18:00
* @version 1.0
*
*/
public interface ProductsPropertyMappingCommandRepository {

    ProductsPropertyMappingEntity insertSelective(ProductsPropertyMappingCommandParam param);

    int updateSelectiveById(ProductsPropertyMappingCommandParam param);

    int remove(Long id);

    void deleteBySelective(int type, Integer propertyId, List<Integer> pdIdList);
}