package net.summerfarm.manage.domain.price.param.command;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
@Data
public class AreaSkuPriceMarkupConfigCommandParam {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 商品编号
	 */
	private String sku;

	/**
	 * 城市编号
	 */
	private Integer areaNo;

	/**
	 * 加价类型：0-按金额加价，1-按百分比加价
	 */
	private Integer markupType;

	/**
	 * 加价金额/加价比例
	 */
	private BigDecimal markupValue;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 添加时间
	 */
	private LocalDateTime createTime;

	/**
	 * 创建人(名字)
	 */
	private String creator;

	/**
	 * 更新人（名字）
	 */
	private String updater;

	

	
}