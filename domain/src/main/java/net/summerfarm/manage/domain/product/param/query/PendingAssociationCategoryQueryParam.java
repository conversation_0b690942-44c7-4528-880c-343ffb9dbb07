package net.summerfarm.manage.domain.product.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2025/4/27 15:33
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PendingAssociationCategoryQueryParam {

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
}
