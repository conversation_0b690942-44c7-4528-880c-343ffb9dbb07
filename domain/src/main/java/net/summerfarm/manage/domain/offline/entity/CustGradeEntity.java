package net.summerfarm.manage.domain.offline.entity;

import java.math.BigDecimal;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-24 11:26:16
 * @version 1.0
 *
 */
@Data
public class CustGradeEntity {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 门店ID
	 */
	private Long custId;

	/**
	 * 门店名称
	 */
	private String custName;

	/**
	 * 当前月累计配送实付金额
	 */
	private BigDecimal dlvRealAmt;

	/**
	 * 当前等级 0,1,2,3
	 */
	private Integer grade;

	/**
	 * 下个月等级 0,1,2,3
	 */
	private Integer nextGrade;

	/**
	 * 日期标签
	 */
	private String dateTag;

	

	
}