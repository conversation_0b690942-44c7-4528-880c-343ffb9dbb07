package net.summerfarm.manage.domain.merchant.param.command;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName MerchantCommandParam
 * @Description TODO
 * <AUTHOR>
 * @Date 10:25 2024/1/23
 * @Version 1.0
 **/
@Data
public class MerchantCommandParam {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 当月会员积分
     */
    private BigDecimal memberIntegral;

    /**
     * 会员等级
     */
    private Integer grade;

    /**
     * 会员等级
     */
    private Integer nextGrade;

    /**
     * 当月的消费金额
     */
    private BigDecimal lastAmount;
}
