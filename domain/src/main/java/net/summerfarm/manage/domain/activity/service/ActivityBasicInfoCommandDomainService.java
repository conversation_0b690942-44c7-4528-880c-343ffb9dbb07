package net.summerfarm.manage.domain.activity.service;


import net.summerfarm.manage.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivityBasicInfoCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivityBasicInfoCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动基本信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Service
public class ActivityBasicInfoCommandDomainService {


    @Autowired
    private ActivityBasicInfoCommandRepository activityBasicInfoCommandRepository;
    @Autowired
    private ActivityBasicInfoQueryRepository activityBasicInfoQueryRepository;



    public ActivityBasicInfoEntity insert(ActivityBasicInfoCommandParam param) {
        return activityBasicInfoCommandRepository.insertSelective(param);
    }


    public int update(ActivityBasicInfoCommandParam param) {
        return activityBasicInfoCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return activityBasicInfoCommandRepository.remove(id);
    }
}
