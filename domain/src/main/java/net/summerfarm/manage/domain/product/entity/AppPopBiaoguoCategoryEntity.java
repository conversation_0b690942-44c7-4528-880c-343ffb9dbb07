package net.summerfarm.manage.domain.product.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2024/11/25 14:28
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppPopBiaoguoCategoryEntity {

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 一级类目
     */
    private String category1;
    /**
     * 二级类目
     */
    private String category2;
    /**
     * 三级类目
     */
    private String category3;

}
