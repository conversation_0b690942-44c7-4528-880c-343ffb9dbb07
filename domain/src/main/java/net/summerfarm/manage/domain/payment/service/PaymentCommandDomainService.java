package net.summerfarm.manage.domain.payment.service;


import net.summerfarm.manage.domain.payment.repository.PaymentQueryRepository;
import net.summerfarm.manage.domain.payment.repository.PaymentCommandRepository;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.PaymentCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 支付领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-12-23 17:01:31
 * @version 1.0
 *
 */
@Service
public class PaymentCommandDomainService {


    @Autowired
    private PaymentCommandRepository paymentCommandRepository;
    @Autowired
    private PaymentQueryRepository paymentQueryRepository;



    public PaymentEntity insert(PaymentCommandParam param) {
        return paymentCommandRepository.insertSelective(param);
    }


    public int update(PaymentCommandParam param) {
        return paymentCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long paymentId) {
        return paymentCommandRepository.remove(paymentId);
    }
}
